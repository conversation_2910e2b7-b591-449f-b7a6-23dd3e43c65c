using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using System.Text.Json;
using SqlSugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 装备服务实现
    /// </summary>
    public class EquipmentService : IEquipmentService
    {
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly IGemstoneService _gemstoneService;
        private readonly ISuitService _suitService;
        private readonly DbContext _dbContext;
        private readonly ILogger<EquipmentService> _logger;
        private readonly EquipmentResolveService _resolveService;

        // 装备系统常量
        private const string STRENGTHEN_STONE_ID = "2017060302";
        private const string ELEMENT_TRANSFORM_STONE_ID = "2017092001";
        private const long GEMSTONE_EMBED_COST = 1000000000;
        private const long STRENGTHEN_BASE_COST = 200000;

        // 五行相生相克关系
        private readonly Dictionary<string, string> _elementGeneration = new()
        {
            {"生命", "金"}, {"魔法", "木"}, {"攻击", "火"}, {"防御", "土"},
            {"命中", "雷"}, {"闪避", "水"}, {"速度", "风"}
        };

        private readonly Dictionary<string, string> _elementRestraint = new()
        {
            {"生命", "火"}, {"魔法", "金"}, {"攻击", "水"}, {"防御", "木"},
            {"命中", "风"}, {"闪避", "土"}, {"速度", "雷"}
        };

        public EquipmentService(
            IEquipmentRepository equipmentRepository,
            IGemstoneService gemstoneService,
            ISuitService suitService,
            DbContext dbContext,
            ILogger<EquipmentService> logger,
            EquipmentResolveService resolveService)
        {
            _equipmentRepository = equipmentRepository;
            _gemstoneService = gemstoneService;
            _suitService = suitService;
            _dbContext = dbContext;
            _logger = logger;
            _resolveService = resolveService;
        }

        #region 基础装备管理

        public async Task<List<UserEquipmentDto>> GetUserEquipmentsAsync(int userId)
        {
            try
            {
                // 使用JOIN查询一次性获取所有需要的信息
                var equipmentData = await _dbContext.Db.Queryable<user_equipment>()
                    .LeftJoin<equipment>((ue, e) => ue.equip_id == e.equip_id)
                    .LeftJoin<equipment_detail>((ue, e, ed) => ue.equip_id == ed.equip_id)
                    .LeftJoin<equipment_type>((ue, e, ed, et) => ue.equip_type_id == et.equip_type_id)
                    .Where((ue, e, ed, et) => ue.user_id == userId)
                    .OrderBy((ue, e, ed, et) => ue.create_time, OrderByType.Desc)
                    .Select((ue, e, ed, et) => new
                    {
                        // user_equipment 字段
                        Id = ue.id,
                        UserId = ue.user_id,
                        EquipId = ue.equip_id,
                        EquipTypeId = ue.equip_type_id,
                        StrengthenLevel = ue.strengthen_level,
                        IsEquipped = ue.is_equipped,
                        PetId = ue.pet_id,
                        Element = ue.element,
                        Slot = ue.slot,
                        CreateTime = ue.create_time,
                        UpdateTime = ue.update_time,
                        Position = ue.position,
                        Name = ue.name,

                        // equipment 字段（装备名称）
                        EquipName = e.name,
                        EquipIcon = e.icon,

                        // equipment_detail 字段
                        MainAttr = ed.main_attr,
                        MainAttrValue = ed.main_attr_value,
                        DetailEquipName = ed.equip_name,

                        // equipment_type 字段（装备类型名称）
                        TypeName = et.type_name
                    })
                    .ToListAsync();

                var result = new List<UserEquipmentDto>();

                foreach (var item in equipmentData)
                {
                    var dto = new UserEquipmentDto
                    {
                        Id = item.Id,
                        UserId = item.UserId,
                        EquipId = item.EquipId,
                        Name = item.Name ?? "",
                        EquipTypeId = item.EquipTypeId,
                        StrengthenLevel = item.StrengthenLevel ?? 0,
                        IsEquipped = item.IsEquipped ?? false,
                        PetId = item.PetId,
                        Element = item.Element,
                        Slot = item.Slot ?? 1,
                        Position = item.Position,
                        CreateTime = item.CreateTime,
                        UpdateTime = item.UpdateTime,

                        // 装备名称优先级：equipment.name > equipment_detail.equip_name > user_equipment.name
                        EquipName = item.EquipName ?? item.DetailEquipName ?? item.Name ?? "未知装备",
                        Icon = item.EquipIcon,

                        // 装备类型名称
                        TypeName = item.TypeName ?? "未知类型",

                        // 装备属性
                        MainAttr = item.MainAttr,
                        MainAttrValue = item.MainAttrValue
                    };

                    // 获取宝石信息
                    var gemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(item.Id);
                    dto.Gemstones = gemstones.Select(g => new GemstoneDto
                    {
                        Id = g.id,
                        TypeName = g.gemstone_type_name,
                        Position = g.position,
                        CreateTime = g.create_time
                    }).ToList();

                    result.Add(dto);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户装备失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        public async Task<UserEquipmentDto?> GetEquipmentByIdAsync(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return null;

                var dto = new UserEquipmentDto
                {
                    Id = equipment.id,
                    UserId = equipment.user_id,
                    EquipId = equipment.equip_id,
                    EquipTypeId = equipment.equip_type_id,
                    StrengthenLevel = equipment.strengthen_level ?? 0,
                    IsEquipped = equipment.is_equipped ?? false,
                    PetId = equipment.pet_id,
                    Element = equipment.element,
                    Slot = equipment.slot ?? 1,
                    CreateTime = equipment.create_time,
                    UpdateTime = equipment.update_time
                };

                // 获取装备详情
                var detail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                if (detail != null)
                {
                    dto.EquipName = detail.equip_name;
                    dto.MainAttr = detail.main_attr;
                    dto.MainAttrValue = detail.main_attr_value;
                }

                // 获取宝石信息
                var gemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(equipment.id);
                dto.Gemstones = gemstones.Select(g => new GemstoneDto
                {
                    Id = g.id,
                    TypeName = g.gemstone_type_name,
                    Position = g.position,
                    CreateTime = g.create_time
                }).ToList();

                return dto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备详情失败，装备ID: {EquipmentId}", userEquipmentId);
                return null;
            }
        }

        public async Task<ApiResult> AddEquipmentAsync(int userId, string equipId)
        {
            try
            {
                var equipmentEntity = new user_equipment
                {
                    user_id = userId,
                    equip_id = equipId,
                    strengthen_level = 0,
                    slot = 1,
                    is_equipped = false,
                    create_time = DateTime.Now,
                    update_time = DateTime.Now
                };

                var success = await _equipmentRepository.AddEquipmentAsync(equipmentEntity);
                if (success)
                {
                    await LogOperationAsync(userId, equipmentEntity.id, "ADD", "添加装备", "SUCCESS", "成功添加装备");
                    return ApiResult.CreateSuccess("成功添加装备");
                }

                return ApiResult.CreateError("添加装备失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加装备失败");
                return ApiResult.CreateError("添加装备失败");
            }
        }

        public async Task<ApiResult> DeleteEquipmentAsync(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return ApiResult.CreateError("装备不存在");

                if (equipment.is_equipped == true)
                    return ApiResult.CreateError("已装备的装备无法删除，请先卸下");

                var success = await _equipmentRepository.DeleteEquipmentAsync(userEquipmentId);
                if (success)
                {
                    await LogOperationAsync(equipment.user_id, userEquipmentId, "DELETE", "删除装备", "SUCCESS", "成功删除装备");
                    return ApiResult.CreateSuccess("成功删除装备");
                }

                return ApiResult.CreateError("删除装备失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除装备失败，装备ID: {EquipmentId}", userEquipmentId);
                return ApiResult.CreateError("删除装备失败");
            }
        }

        #endregion

        #region 装备穿戴

        public async Task<ApiResult> EquipToPetAsync(int userEquipmentId, int petId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备信息
                    var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                    if (equipment == null)
                        return ApiResult.CreateError("装备不存在");

                    if (equipment.is_equipped == true)
                        return ApiResult.CreateError("装备已被穿戴");

                    // 2. 获取装备详细信息
                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                    if (equipDetail == null)
                        return ApiResult.CreateError("装备配置不存在");

                    // 3. 获取宠物信息
                    var pet = await _dbContext.Db.Queryable<user_pet>()
                        .Where(x => x.id == petId)
                        .FirstAsync();
                    if (pet == null)
                        return ApiResult.CreateError("宠物不存在");

                    // 4. 检查五行限制
                    if (!string.IsNullOrEmpty(equipDetail.element_limit) &&
                        equipDetail.element_limit != pet.element)
                    {
                        return ApiResult.CreateError($"佩戴失败，当前宠物必须{equipDetail.element_limit}系的宠物才能够佩戴！");
                    }

                    // 5. 巫系宠物特殊限制
                    if (pet.element == "巫" && equipDetail.element_limit != "巫")
                    {
                        return ApiResult.CreateError("巫系宠物只能够佩戴专属五行装备，无法佩戴其他装备！");
                    }

                    // 6. 检查是否已有同类型装备
                    var existingEquip = await _dbContext.Db.Queryable<user_equipment>()
                        .Where(x => x.pet_id == petId &&
                                   x.equip_type_id == equipment.equip_type_id &&
                                   x.is_equipped == true)
                        .FirstAsync();

                    if (existingEquip != null)
                    {
                        // 获取装备类型名称
                        var equipType = await _dbContext.Db.Queryable<equipment_type>()
                            .Where(x => x.equip_type_id == equipment.equip_type_id)
                            .FirstAsync();
                        var typeName = equipType?.type_name ?? "未知类型";
                        return ApiResult.CreateError($"失败!当前宠物身上已经佩戴了{typeName}类型的装备,无法重复佩戴!");
                    }

                    // 7. 更新装备状态
                    equipment.pet_id = petId;
                    equipment.is_equipped = true;
                    equipment.update_time = DateTime.Now;

                    var success = await _equipmentRepository.UpdateEquipmentAsync(equipment);
                    if (!success)
                        return ApiResult.CreateError("装备穿戴失败");

                    // 8. 记录操作日志
                    await LogOperationAsync(equipment.user_id, userEquipmentId, "EQUIP",
                        $"装备到宠物{petId}", "SUCCESS", "成功穿戴装备");

                    // TODO: 刷新宠物属性 (需要实现宠物属性计算服务)
                    // await _petAttributeService.RefreshPetAttributesAsync(petId);

                    return ApiResult.CreateSuccess("成功佩戴装备!恭喜您的宠物能力得到了进一步的提升!");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备穿戴失败，装备ID: {EquipmentId}, 宠物ID: {PetId}",
                    userEquipmentId, petId);
                return ApiResult.CreateError("装备穿戴失败");
            }
        }

        public async Task<ApiResult> UnequipFromPetAsync(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return ApiResult.CreateError("装备不存在");

                if (equipment.is_equipped != true)
                    return ApiResult.CreateError("装备未被穿戴");

                equipment.pet_id = null;
                equipment.is_equipped = false;
                equipment.position = null;
                equipment.update_time = DateTime.Now;

                var success = await _equipmentRepository.UpdateEquipmentAsync(equipment);
                if (success)
                {
                    await LogOperationAsync(equipment.user_id, userEquipmentId, "UNEQUIP",
                        "卸下装备", "SUCCESS", "成功卸下装备");

                    // TODO: 刷新宠物属性 (需要实现宠物属性计算服务)
                    // await _petAttributeService.RefreshPetAttributesAsync(equipment.pet_id);

                    return ApiResult.CreateSuccess("成功卸下装备");
                }

                return ApiResult.CreateError("卸下装备失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "卸下装备失败，装备ID: {EquipmentId}", userEquipmentId);
                return ApiResult.CreateError("卸下装备失败");
            }
        }

        /// <summary>
        /// 卸下宠物身上的所有装备
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns></returns>
        public async Task<ApiResult> UnequipAllFromPetAsync(int petId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 获取宠物所有装备
                    var equipments = await _dbContext.Db.Queryable<user_equipment>()
                        .Where(x => x.pet_id == petId && x.is_equipped == true)
                        .ToListAsync();

                    if (!equipments.Any())
                        return ApiResult.CreateSuccess("宠物身上没有装备");

                    int unequippedCount = 0;
                    foreach (var equipment in equipments)
                    {
                        equipment.pet_id = null;
                        equipment.is_equipped = false;
                        equipment.position = null;
                        equipment.update_time = DateTime.Now;

                        var success = await _equipmentRepository.UpdateEquipmentAsync(equipment);
                        if (success)
                        {
                            await LogOperationAsync(equipment.user_id, equipment.id, "UNEQUIP_ALL",
                                $"批量卸下宠物{petId}装备", "SUCCESS", "批量卸下装备成功");
                            unequippedCount++;
                        }
                    }

                    // TODO: 刷新宠物属性 (需要实现宠物属性计算服务)
                    // await _petAttributeService.RefreshPetAttributesAsync(petId);

                    return ApiResult.CreateSuccess($"成功卸下{unequippedCount}件装备");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量卸下装备失败，宠物ID: {PetId}", petId);
                return ApiResult.CreateError("批量卸下装备失败");
            }
        }

        public async Task<List<UserEquipmentDto>> GetPetEquipmentsAsync(int petId)
        {
            try
            {
                // 使用JOIN查询一次性获取所有需要的信息
                var equipmentData = await _dbContext.Db.Queryable<user_equipment>()
                    .LeftJoin<equipment>((ue, e) => ue.equip_id == e.equip_id)
                    .LeftJoin<equipment_detail>((ue, e, ed) => ue.equip_id == ed.equip_id)
                    .LeftJoin<equipment_type>((ue, e, ed, et) => ue.equip_type_id == et.equip_type_id)
                    .Where((ue, e, ed, et) => ue.pet_id == petId && ue.is_equipped == true)
                    .Select((ue, e, ed, et) => new
                    {
                        // user_equipment 字段
                        Id = ue.id,
                        UserId = ue.user_id,
                        EquipId = ue.equip_id,
                        EquipTypeId = ue.equip_type_id,
                        StrengthenLevel = ue.strengthen_level,
                        IsEquipped = ue.is_equipped,
                        PetId = ue.pet_id,
                        Element = ue.element,
                        Slot = ue.slot,
                        Position = ue.position,
                        Name = ue.name,

                        // equipment 字段（装备名称）
                        EquipName = e.name,
                        EquipIcon = e.icon,

                        // equipment_detail 字段
                        MainAttr = ed.main_attr,
                        MainAttrValue = ed.main_attr_value,
                        DetailEquipName = ed.equip_name,

                        // equipment_type 字段（装备类型名称）
                        TypeName = et.type_name
                    })
                    .ToListAsync();

                var result = new List<UserEquipmentDto>();

                foreach (var item in equipmentData)
                {
                    var dto = new UserEquipmentDto
                    {
                        Id = item.Id,
                        UserId = item.UserId,
                        EquipId = item.EquipId,
                        Name = item.Name ?? "",
                        EquipTypeId = item.EquipTypeId,
                        StrengthenLevel = item.StrengthenLevel ?? 0,
                        IsEquipped = item.IsEquipped ?? false,
                        PetId = item.PetId,
                        Element = item.Element,
                        Slot = item.Slot ?? 1,
                        Position = item.Position,

                        // 装备名称优先级：equipment.name > equipment_detail.equip_name > user_equipment.name
                        EquipName = item.EquipName ?? item.DetailEquipName ?? item.Name ?? "未知装备",
                        Icon = item.EquipIcon,

                        // 装备类型名称
                        TypeName = item.TypeName ?? "未知类型",

                        // 装备属性
                        MainAttr = item.MainAttr,
                        MainAttrValue = item.MainAttrValue
                    };

                    // 获取宝石信息
                    var gemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(item.Id);
                    dto.Gemstones = gemstones.Select(g => new GemstoneDto
                    {
                        Id = g.id,
                        TypeName = g.gemstone_type_name,
                        Position = g.position,
                        CreateTime = g.create_time
                    }).ToList();

                    result.Add(dto);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物装备失败，宠物ID: {PetId}", petId);
                throw;
            }
        }

        public async Task<List<UserEquipmentDto>> GetUnusedEquipmentsAsync(int userId)
        {
            try
            {
                // 使用JOIN查询一次性获取所有需要的信息
                var equipmentData = await _dbContext.Db.Queryable<user_equipment>()
                    .LeftJoin<equipment>((ue, e) => ue.equip_id == e.equip_id)
                    .LeftJoin<equipment_detail>((ue, e, ed) => ue.equip_id == ed.equip_id)
                    .LeftJoin<equipment_type>((ue, e, ed, et) => ue.equip_type_id == et.equip_type_id)
                    .Where((ue, e, ed, et) => ue.user_id == userId && (ue.is_equipped == false || ue.is_equipped == null))
                    .Select((ue, e, ed, et) => new
                    {
                        // user_equipment 字段
                        Id = ue.id,
                        UserId = ue.user_id,
                        EquipId = ue.equip_id,
                        EquipTypeId = ue.equip_type_id,
                        StrengthenLevel = ue.strengthen_level,
                        IsEquipped = ue.is_equipped,
                        PetId = ue.pet_id,
                        Element = ue.element,
                        Slot = ue.slot,
                        Position = ue.position,
                        Name = ue.name,

                        // equipment 字段（装备名称）
                        EquipName = e.name,
                        EquipIcon = e.icon,

                        // equipment_detail 字段
                        MainAttr = ed.main_attr,
                        MainAttrValue = ed.main_attr_value,
                        DetailEquipName = ed.equip_name,

                        // equipment_type 字段（装备类型名称）
                        TypeName = et.type_name
                    })
                    .ToListAsync();

                var result = new List<UserEquipmentDto>();

                foreach (var item in equipmentData)
                {
                    var dto = new UserEquipmentDto
                    {
                        Id = item.Id,
                        UserId = item.UserId,
                        EquipId = item.EquipId,
                        Name = item.Name ?? "",
                        EquipTypeId = item.EquipTypeId,
                        StrengthenLevel = item.StrengthenLevel ?? 0,
                        IsEquipped = item.IsEquipped ?? false,
                        PetId = item.PetId,
                        Element = item.Element,
                        Slot = item.Slot ?? 1,
                        Position = item.Position,

                        // 装备名称优先级：equipment.name > equipment_detail.equip_name > user_equipment.name
                        EquipName = item.EquipName ?? item.DetailEquipName ?? item.Name ?? "未知装备",
                        Icon = item.EquipIcon,

                        // 装备类型名称
                        TypeName = item.TypeName ?? "未知类型",

                        // 装备属性
                        MainAttr = item.MainAttr,
                        MainAttrValue = item.MainAttrValue
                    };

                    result.Add(dto);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取未使用装备失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        #endregion

        #region 装备强化

        public async Task<ApiResult<StrengthenResult>> StrengthenEquipmentAsync(int userEquipmentId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备信息
                    var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                    if (equipment == null)
                        return ApiResult<StrengthenResult>.CreateError("装备不存在");

                    // 2. 获取装备详情
                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                    if (equipDetail == null)
                        return ApiResult<StrengthenResult>.CreateError("装备配置不存在");

                    // 3. 计算强化消耗
                    var cost = await CalculateStrengthenCostAsync(userEquipmentId);

                    // 4. 检查强化石
                    var userStone = await _dbContext.Db.Queryable<user_item>()
                        .Where(x => x.user_id == equipment.user_id && x.item_id == STRENGTHEN_STONE_ID)
                        .FirstAsync();

                    if (userStone == null || userStone.item_count < cost.StoneCount)
                        return ApiResult<StrengthenResult>.CreateError($"强化石不足，需要{cost.StoneCount}个");

                    // 5. 检查金币
                    var user = await _dbContext.Db.Queryable<user>()
                        .Where(x => x.id == equipment.user_id)
                        .FirstAsync();

                    if (user == null || Convert.ToInt64(user.money) < cost.MoneyCost)
                        return ApiResult<StrengthenResult>.CreateError($"金币不足，需要{cost.MoneyCost}金币");

                    // 6. 执行强化
                    var currentLevel = equipment.strengthen_level ?? 0;
                    var newLevel = currentLevel + 1;

                    // 7. 扣除消耗
                    await _dbContext.Db.Updateable<user_item>()
                        .SetColumns(x => x.item_count == x.item_count - cost.StoneCount)
                        .Where(x => x.user_id == equipment.user_id && x.item_id == STRENGTHEN_STONE_ID)
                        .ExecuteCommandAsync();

                    await _dbContext.Db.Updateable<user>()
                        .SetColumns(x => x.money == (Convert.ToInt64(x.money) - cost.MoneyCost).ToString())
                        .Where(x => x.id == equipment.user_id)
                        .ExecuteCommandAsync();

                    // 8. 更新装备等级
                    equipment.strengthen_level = newLevel;
                    equipment.update_time = DateTime.Now;
                    await _equipmentRepository.UpdateEquipmentAsync(equipment);

                    // 9. 记录操作日志
                    var costItems = new Dictionary<string, int> { { STRENGTHEN_STONE_ID, cost.StoneCount } };
                    await LogOperationAsync(equipment.user_id, userEquipmentId, "STRENGTHEN",
                        $"强化到+{newLevel}", "SUCCESS", "强化成功！", cost.MoneyCost, costItems);

                    var result = new StrengthenResult
                    {
                        Success = true,
                        NewLevel = newLevel,
                        Message = $"强化成功！装备等级提升到+{newLevel}",
                        CostMoney = cost.MoneyCost,
                        CostItems = costItems
                    };

                    return ApiResult<StrengthenResult>.CreateSuccess(result, "强化成功！");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备强化失败");
                return ApiResult<StrengthenResult>.CreateError("装备强化失败");
            }
        }

        public async Task<StrengthenCostDto> CalculateStrengthenCostAsync(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return new StrengthenCostDto { StoneCount = 0, MoneyCost = 0 };

                var currentLevel = equipment.strengthen_level ?? 0;

                // 强化消耗计算（基于原系统逻辑）
                var stoneCount = Math.Max(1, currentLevel / 5 + 1);
                var moneyCost = STRENGTHEN_BASE_COST * (currentLevel + 1);

                return new StrengthenCostDto
                {
                    StoneCount = stoneCount,
                    MoneyCost = moneyCost
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算强化消耗失败");
                return new StrengthenCostDto { StoneCount = 0, MoneyCost = 0 };
            }
        }

        #endregion

        #region 宝石镶嵌

        public async Task<ApiResult> EmbedGemstoneAsync(int userEquipmentId, string gemstoneTypeId, int position)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 检查装备是否存在
                    var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                    if (equipment == null)
                        return ApiResult.CreateError("装备不存在");

                    // 2. 检查宝石配置
                    var gemstoneConfig = await _equipmentRepository.GetGemstoneConfigAsync(gemstoneTypeId);
                    if (gemstoneConfig == null)
                        return ApiResult.CreateError("宝石类型不存在");

                    // 3. 检查槽位限制
                    if (position < 1 || position > 3)
                        return ApiResult.CreateError("无效的槽位位置");

                    if (equipment.slot < position)
                        return ApiResult.CreateError("装备槽位不足");

                    // 4. 检查宝石等级和槽位匹配
                    if ((position == 1 && gemstoneConfig.level != 1) ||
                        (position == 2 && gemstoneConfig.level != 2) ||
                        (position == 3 && gemstoneConfig.level != 3))
                    {
                        return ApiResult.CreateError($"该槽位只能打{position}孔宝石！");
                    }

                    // 5. 检查位置是否已有宝石
                    var existingGemstones = await _equipmentRepository.GetEquipmentGemstonesAsync(userEquipmentId);
                    var existingGemstone = existingGemstones.FirstOrDefault(x => x.position == position);

                    // 6. 检查用户是否有该宝石
                    var userGemstone = await _dbContext.Db.Queryable<user_item>()
                        .Where(x => x.user_id == equipment.user_id && x.item_id == gemstoneConfig.prop_id)
                        .FirstAsync();

                    if (userGemstone == null || userGemstone.item_count < 1)
                        return ApiResult.CreateError("您没有该类型的宝石");

                    // 7. 检查金币
                    var user = await _dbContext.Db.Queryable<user>()
                        .Where(x => x.id == equipment.user_id)
                        .FirstAsync();

                    if (user == null || Convert.ToInt64(user.money) < GEMSTONE_EMBED_COST)
                        return ApiResult.CreateError($"金币不足{GEMSTONE_EMBED_COST}，无法镶嵌。");

                    // 8. 如果位置已有宝石，检查是否可以替换
                    if (existingGemstone != null)
                    {
                        var existingConfig = await _equipmentRepository.GetGemstoneConfigAsync(existingGemstone.gemstone_type_name);
                        if (existingConfig != null && existingConfig.type_class == gemstoneConfig.type_class)
                        {
                            if (gemstoneConfig.level <= existingConfig.level)
                                return ApiResult.CreateError("槽位不足或当前镶嵌宝石等级低于所有已镶嵌同类型宝石，无法继续镶嵌。");

                            // 替换宝石
                            await _equipmentRepository.RemoveGemstoneAsync(userEquipmentId, position);
                        }
                        else
                        {
                            return ApiResult.CreateError("该位置已有其他类型宝石，无法镶嵌");
                        }
                    }

                    // 9. 扣除消耗
                    await _dbContext.Db.Updateable<user_item>()
                        .SetColumns(x => x.item_count == x.item_count - 1)
                        .Where(x => x.user_id == equipment.user_id && x.item_id == gemstoneConfig.prop_id)
                        .ExecuteCommandAsync();

                    await _dbContext.Db.Updateable<user>()
                        .SetColumns(x => x.money == (Convert.ToInt64(x.money) - GEMSTONE_EMBED_COST).ToString())
                        .Where(x => x.id == equipment.user_id)
                        .ExecuteCommandAsync();

                    // 10. 镶嵌宝石
                    var gemstoneEntity = new equipment_gemstone
                    {
                        user_equipment_id = userEquipmentId,
                        gemstone_type_name = gemstoneTypeId,
                        position = position,
                        create_time = DateTime.Now
                    };

                    await _equipmentRepository.AddGemstoneAsync(gemstoneEntity);

                    // 11. 记录操作日志
                    var costItems = new Dictionary<string, int> { { gemstoneConfig.prop_id!, 1 } };
                    await LogOperationAsync(equipment.user_id, userEquipmentId, "EMBED_GEM",
                        $"镶嵌{gemstoneTypeId}到位置{position}", "SUCCESS", "镶嵌成功！",
                        GEMSTONE_EMBED_COST, costItems);

                    return ApiResult.CreateSuccess("镶嵌成功！");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石镶嵌失败");
                return ApiResult.CreateError("宝石镶嵌失败");
            }
        }

        public async Task<ApiResult> RemoveGemstoneAsync(int userEquipmentId, int position)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return ApiResult.CreateError("装备不存在");

                var success = await _equipmentRepository.RemoveGemstoneAsync(userEquipmentId, position);
                if (success)
                {
                    await LogOperationAsync(equipment.user_id, userEquipmentId, "REMOVE_GEM",
                        $"拆卸位置{position}的宝石", "SUCCESS", "宝石拆卸成功");
                    return ApiResult.CreateSuccess("宝石拆卸成功");
                }

                return ApiResult.CreateError("该位置没有宝石");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石拆卸失败");
                return ApiResult.CreateError("宝石拆卸失败");
            }
        }

        public async Task<ApiResult> ClearAllGemstonesAsync(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return ApiResult.CreateError("装备不存在");

                await _equipmentRepository.ClearAllGemstonesAsync(userEquipmentId);

                await LogOperationAsync(equipment.user_id, userEquipmentId, "CLEAR_GEMS",
                    "清空所有宝石", "SUCCESS", "成功清空所有宝石");

                return ApiResult.CreateSuccess("成功清空所有宝石");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空宝石失败");
                return ApiResult.CreateError("清空宝石失败");
            }
        }

        public async Task<ApiResult> ExpandSlotAsync(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return ApiResult.CreateError("装备不存在");

                if (equipment.slot >= 3)
                    return ApiResult.CreateError("装备槽位已达到最大值");

                // 这里需要检查开孔道具和消耗
                // 暂时简化实现
                equipment.slot = (equipment.slot ?? 1) + 1;
                await _equipmentRepository.UpdateEquipmentAsync(equipment);

                await LogOperationAsync(equipment.user_id, userEquipmentId, "EXPAND_SLOT",
                    $"扩展槽位到{equipment.slot}", "SUCCESS", "槽位扩展成功");

                return ApiResult.CreateSuccess($"成功扩展槽位到{equipment.slot}个");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扩展槽位失败");
                return ApiResult.CreateError("扩展槽位失败");
            }
        }

        #endregion

        #region 五行点化

        public async Task<ApiResult> TransformElementAsync(int userEquipmentId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备信息
                    var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                    if (equipment == null)
                        return ApiResult.CreateError("装备不存在");

                    // 2. 获取装备详情
                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                    if (equipDetail == null)
                        return ApiResult.CreateError("装备配置不存在");

                    // 3. 检查五行点化石
                    var userStone = await _dbContext.Db.Queryable<user_item>()
                        .Where(x => x.user_id == equipment.user_id && x.item_id == ELEMENT_TRANSFORM_STONE_ID)
                        .FirstAsync();

                    if (userStone == null || userStone.item_count < 1)
                        return ApiResult.CreateError("必须拥有足够的五行点化石才能点化哦!");

                    // 4. 获取装备主属性
                    string mainAttr = equipDetail.main_attr ?? "攻击";
                    string bestElement = _elementGeneration.GetValueOrDefault(mainAttr, "金");
                    string worstElement = _elementRestraint.GetValueOrDefault(mainAttr, "火");

                    string newElement;
                    // 5. 如果装备没有五行
                    if (string.IsNullOrEmpty(equipment.element))
                    {
                        var availableElements = new List<string> { "金", "木", "水", "火", "土", "雷", "风" };
                        newElement = availableElements[new Random().Next(availableElements.Count)];
                    }
                    // 6. 如果已经是最佳五行
                    else if (equipment.element == bestElement)
                    {
                        return ApiResult.CreateError("您的装备五行已经是最佳的了，无需再点化了！");
                    }
                    // 7. 否则按概率点化
                    else
                    {
                        newElement = GenerateRandomElement(bestElement, worstElement);
                    }

                    // 8. 扣除五行点化石
                    await _dbContext.Db.Updateable<user_item>()
                        .SetColumns(x => x.item_count == x.item_count - 1)
                        .Where(x => x.user_id == equipment.user_id && x.item_id == ELEMENT_TRANSFORM_STONE_ID)
                        .ExecuteCommandAsync();

                    // 9. 更新装备五行
                    equipment.element = newElement;
                    equipment.update_time = DateTime.Now;
                    await _equipmentRepository.UpdateEquipmentAsync(equipment);

                    // 10. 记录操作日志
                    var costItems = new Dictionary<string, int> { { ELEMENT_TRANSFORM_STONE_ID, 1 } };
                    await LogOperationAsync(equipment.user_id, userEquipmentId, "TRANSFORM_ELEMENT",
                        $"五行点化为{newElement}", "SUCCESS", "五行点化成功！", 0, costItems);

                    return ApiResult.CreateSuccess("五行点化成功！");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "五行点化失败");
                return ApiResult.CreateError("五行点化失败");
            }
        }

        private string GenerateRandomElement(string bestElement, string worstElement)
        {
            var random = new Random();
            var allElements = new List<string> { "金", "木", "水", "火", "土", "雷", "风" };

            // 30%概率获得最佳五行，10%概率获得最差五行，60%概率获得其他五行
            var chance = random.Next(100);
            if (chance < 30)
                return bestElement;
            else if (chance < 40)
                return worstElement;
            else
            {
                var otherElements = allElements.Where(x => x != bestElement && x != worstElement).ToList();
                return otherElements[random.Next(otherElements.Count)];
            }
        }

        #endregion

        #region 装备分解

        public async Task<ApiResult<ResolveResult>> ResolveEquipmentAsync(int userEquipmentId)
        {
            // 使用专用的分解服务
            return await _resolveService.ResolveEquipmentAsync(userEquipmentId);
        }

        private string? DetermineSuitIdByEquipId(string equipId)
        {
            // 基于装备ID前缀判断套装
            if (equipId.StartsWith("2017070101")) return "2017070101"; // 天魔套装
            if (equipId.StartsWith("2017063001")) return "2017063001"; // 自然套装
            if (equipId.StartsWith("20170612")) return "20170612";     // 黑白套装
            if (equipId.StartsWith("2016123001")) return "2016123001"; // 盛世套装
            if (equipId.StartsWith("2017051801")) return "2017051801"; // 龙鳞套装
            if (equipId.StartsWith("2017042901")) return "2017042901"; // 凤羽套装

            return null; // 非套装装备
        }

        #endregion

        #region 套装和属性计算

        public async Task<List<SuitActivationDto>> GetPetSuitActivationsAsync(int petId)
        {
            return await _suitService.GetPetSuitActivationsAsync(petId);
        }

        public async Task<Dictionary<string, double>> CalculateEquipmentAttributesAsync(int petId)
        {
            try
            {
                var equipments = await GetPetEquipmentsAsync(petId);
                var totalAttributes = new Dictionary<string, double>();

                foreach (var equipment in equipments)
                {
                    // 获取装备详情
                    var detail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.EquipId);
                    if (detail == null) continue;

                    // 计算装备基础属性
                    var equipAttributes = CalculateEquipmentBaseAttributes(detail, equipment);

                    // 叠加到总属性
                    foreach (var attr in equipAttributes)
                    {
                        if (totalAttributes.ContainsKey(attr.Key))
                            totalAttributes[attr.Key] += attr.Value;
                        else
                            totalAttributes[attr.Key] = attr.Value;
                    }
                }

                // 计算套装属性加成
                var suitActivations = await GetPetSuitActivationsAsync(petId);
                foreach (var suit in suitActivations)
                {
                    foreach (var attr in suit.ActivatedAttributes)
                    {
                        var value = attr.IsPercentage ?
                            double.Parse(attr.AttributeValue) / 100.0 :
                            double.Parse(attr.AttributeValue);

                        if (totalAttributes.ContainsKey(attr.AttributeType))
                            totalAttributes[attr.AttributeType] += value;
                        else
                            totalAttributes[attr.AttributeType] = value;
                    }
                }

                return totalAttributes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算装备属性失败，宠物ID: {PetId}", petId);
                return new Dictionary<string, double>();
            }
        }

        private Dictionary<string, double> CalculateEquipmentBaseAttributes(equipment_detail detail, UserEquipmentDto equipment)
        {
            var attributes = new Dictionary<string, double>();

            // 主属性计算
            if (!string.IsNullOrEmpty(detail.main_attr) && !string.IsNullOrEmpty(detail.main_attr_value))
            {
                var baseValue = double.Parse(detail.main_attr_value);
                var finalValue = CalculateAttributeWithEnhancements(baseValue, equipment.StrengthenLevel, equipment.Element, detail.main_attr, true);
                attributes[detail.main_attr] = finalValue;
            }

            // 副属性计算
            if (!string.IsNullOrEmpty(detail.sub_attr) && !string.IsNullOrEmpty(detail.sub_attr_value))
            {
                var baseValue = double.Parse(detail.sub_attr_value);
                var finalValue = CalculateAttributeWithEnhancements(baseValue, equipment.StrengthenLevel, equipment.Element, detail.sub_attr, false);
                attributes[detail.sub_attr] = finalValue;
            }

            return attributes;
        }

        private double CalculateAttributeWithEnhancements(double baseValue, int strengthenLevel, string? element, string attributeType, bool isMainAttribute)
        {
            // 1. 强化等级加成
            var strengthenMultiplier = 1.0 + (strengthenLevel * 0.1); // 每级10%加成

            // 2. 五行加成
            var elementMultiplier = 1.0;
            if (!string.IsNullOrEmpty(element))
            {
                var bestElement = _elementGeneration.GetValueOrDefault(attributeType, "");
                var worstElement = _elementRestraint.GetValueOrDefault(attributeType, "");

                if (element == bestElement)
                    elementMultiplier = 1.2; // 相生20%加成
                else if (element == worstElement)
                    elementMultiplier = 0.8; // 相克20%减成
            }

            return baseValue * strengthenMultiplier * elementMultiplier;
        }

        #endregion

        #region 操作日志

        private async Task LogOperationAsync(int userId, int equipmentId, string operationType,
            string operationDesc, string result, string message, long moneyCost = 0,
            Dictionary<string, int>? itemCosts = null)
        {
            try
            {
                var log = new equipment_operation_log
                {
                    user_id = userId,
                    user_equipment_id = equipmentId,
                    operation_type = operationType,
                    operation_data = operationDesc,
                    result = result,
                    result_message = message,
                    cost_money = moneyCost,
                    cost_items = itemCosts != null ? JsonSerializer.Serialize(itemCosts) : null,
                    create_time = DateTime.Now
                };

                await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录装备操作日志失败");
                // 日志记录失败不影响主要操作
            }
        }

        #endregion
    }
}