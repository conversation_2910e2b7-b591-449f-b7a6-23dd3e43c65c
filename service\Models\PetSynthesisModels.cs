using System;
using SqlSugar;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 宠物合成配置表模型
    /// </summary>
    [SugarTable("pet_synthesis_config")]
    public class pet_synthesis_config
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 配置键
        /// </summary>
        public string config_key { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        public string config_value { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        public string config_type { get; set; } = "STRING";

        /// <summary>
        /// 配置描述
        /// </summary>
        public string description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool is_active { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;
    }

    // pet_synthesis_formula 类已移动到独立文件 pet_synthesis_formula.cs
    /*
    /// <summary>
    /// 宠物合成公式表模型
    /// </summary>
    [SugarTable("pet_synthesis_formula")]
    public class pet_synthesis_formula
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 主宠编号
        /// </summary>
        public int main_pet_no { get; set; }

        /// <summary>
        /// 副宠编号(-1表示任意)
        /// </summary>
        public int vice_pet_no { get; set; } = -1;

        /// <summary>
        /// 主宠成长要求
        /// </summary>
        public decimal main_growth_required { get; set; } = 0.000000m;

        /// <summary>
        /// 副宠成长要求
        /// </summary>
        public decimal vice_growth_required { get; set; } = 0.000000m;

        /// <summary>
        /// 合成结果宠物编号
        /// </summary>
        public int result_pet_no { get; set; }

        /// <summary>
        /// 成功率加成(%)
        /// </summary>
        public decimal success_rate_bonus { get; set; } = 0.00m;

        /// <summary>
        /// 成长加成(%)
        /// </summary>
        public decimal growth_bonus { get; set; } = 0.00m;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool is_active { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;
    }
    */

    // pet_synthesis_log 类已移动到独立文件 pet_synthesis_log.cs
    /*
    /// <summary>
    /// 宠物合成记录表模型
    /// </summary>
    [SugarTable("pet_synthesis_log")]
    public class pet_synthesis_log
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// 主宠ID
        /// </summary>
        public int main_pet_id { get; set; }

        /// <summary>
        /// 副宠ID
        /// </summary>
        public int vice_pet_id { get; set; }

        /// <summary>
        /// 使用的公式ID
        /// </summary>
        public int? formula_id { get; set; }

        /// <summary>
        /// 合成前主宠成长
        /// </summary>
        public decimal before_main_growth { get; set; }

        /// <summary>
        /// 合成后主宠成长
        /// </summary>
        public decimal? after_main_growth { get; set; }

        /// <summary>
        /// 成长增加值
        /// </summary>
        public decimal? growth_increase { get; set; }

        /// <summary>
        /// 合成前宠物编号
        /// </summary>
        public int before_pet_no { get; set; }

        /// <summary>
        /// 合成后宠物编号
        /// </summary>
        public int? after_pet_no { get; set; }

        /// <summary>
        /// 使用的道具列表(JSON)
        /// </summary>
        public string used_items { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long cost_gold { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool is_success { get; set; }

        /// <summary>
        /// 是否合成出神宠
        /// </summary>
        public bool is_god_pet { get; set; } = false;

        /// <summary>
        /// 实际成功率
        /// </summary>
        public decimal? success_rate { get; set; }

        /// <summary>
        /// 合成时间
        /// </summary>
        public DateTime create_time { get; set; } = DateTime.Now;
    }
    */

    /// <summary>
    /// 五行枚举
    /// </summary>
    public enum ElementType
    {
        金 = 1,
        木 = 2,
        水 = 3,
        火 = 4,
        土 = 5,
        神 = 6,
        神圣 = 7,
        聖 = 8,
        佛 = 9,
        魔 = 10,
        人 = 11,
        鬼 = 12,
        巫 = 13,
        萌 = 14,
        仙 = 15,
        灵 = 16,
        次元 = 17
    }

    /// <summary>
    /// 合成上下文
    /// </summary>
    public class SynthesisContext
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 主宠信息
        /// </summary>
        public user_pet MainPet { get; set; }

        /// <summary>
        /// 副宠信息
        /// </summary>
        public user_pet VicePet { get; set; }

        /// <summary>
        /// 合成公式
        /// </summary>
        public pet_synthesis_formula Formula { get; set; }

        /// <summary>
        /// 使用的道具列表
        /// </summary>
        public List<string> UsedItems { get; set; } = new List<string>();

        /// <summary>
        /// 用户信息
        /// </summary>
        public user UserInfo { get; set; }

        /// <summary>
        /// 主宠配置
        /// </summary>
        public pet_config MainPetConfig { get; set; }

        /// <summary>
        /// 副宠配置
        /// </summary>
        public pet_config VicePetConfig { get; set; }
    }
}
