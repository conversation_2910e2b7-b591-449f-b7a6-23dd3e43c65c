import { Global } from "../Global";
import { HttpRequest } from "../tool/HttpRequest";
import { BattleRequestDTO, BattleResultDTO } from "./BattleTypes";

/**
 * 简化的战斗管理器 - 专注于一次性战斗请求
 */
export class BattleManager {
    private static instance: BattleManager;
    
    // 单例模式
    public static getInstance(): BattleManager {
        if (!BattleManager.instance) {
            BattleManager.instance = new BattleManager();
        }
        return BattleManager.instance;
    }
    
    private constructor() {}
    
    /**
     * 发起完整战斗
     */
    public async startNewBattle(mapId?: number, petId?: number, skillId?: string): Promise<BattleResultDTO | null> {
        try {
            console.log("🎮 准备开始战斗...");
            
            // 使用传入参数或默认全局配置
            const battleRequest: BattleRequestDTO = {
                MapId: mapId || Global.currentMapId,
                UserId: Global.userid,
                PetId: petId || Global.currentPetId,
                SkillId: skillId || Global.currentSkillId
            };
            
            console.log("🚀 发送战斗请求1111:", battleRequest);
            
            const result = await HttpRequest.postConvertJson('Player/CalculateBattle', battleRequest);
            
            if (result) {
                console.log("⚔️ 收到完整战斗结果:", result);
                return result as BattleResultDTO;
            }
            
            return null;
        } catch (error) {
            console.error("💥 战斗请求失败:", error);
            throw error;
        }
    }
    
    /**
     * 格式化战斗结果显示文本
     */
    public formatBattleResult(result: BattleResultDTO): {
        resultText: string;
        expText: string;
        dropText: string;
        messageText: string;
    } {
        const resultText = result.isWin ? "🎉 获得胜利！" : "💀 你的宠物战败了！";
        const expText = result.exp > 0 ? `经验+${result.exp}` : "";
        
        // 处理掉落物品
        let dropText = "";
        if (result.dropItems && result.dropItems.length > 0) {
            dropText = result.dropItems.join(", ");
        }
        
        return {
            resultText,
            expText,
            dropText,
            messageText: result.message
        };
    }
    
    /**
     * 检查是否可以发起战斗
     */
    public validateBattleConditions(): { isValid: boolean; errorMessage: string } {
        if (!Global.userid || Global.userid <= 0) {
            return { isValid: false, errorMessage: "用户未登录" };
        }
        
        if (!Global.currentPetId || Global.currentPetId <= 0) {
            return { isValid: false, errorMessage: "未选择出战宠物" };
        }
        
        if (!Global.currentMapId || Global.currentMapId <= 0) {
            return { isValid: false, errorMessage: "未选择战斗地图" };
        }
        
        if (!Global.currentSkillId) {
            return { isValid: false, errorMessage: "未选择战斗技能" };
        }
        
        return { isValid: true, errorMessage: "" };
    }

    /**
     * 🆕 格式化战斗统计信息
     */
    public formatBattleStats(result: any): {
        totalDamage: string;
        damageEffects: string;
        firstStrike: string;
        economicRewards: string;
    } {
        return {
            totalDamage: `总伤害: ${result.totalDamageDealt || 0}`,
            damageEffects: this.formatDamageEffects(result),
            firstStrike: result.isFirstStrike ? "🚀 先手攻击" : "🛡️ 后手防守",
            economicRewards: this.formatEconomicRewards(result)
        };
    }

    /**
     * 🆕 格式化伤害特效信息
     */
    private formatDamageEffects(result: any): string {
        const effects = [];
        if (result.damageAmplified > 0) effects.push(`加深+${result.damageAmplified}`);
        if (result.damageReduced > 0) effects.push(`抵消+${result.damageReduced}`);
        if (result.lifeSteal > 0) effects.push(`吸血+${result.lifeSteal}`);
        if (result.manaSteal > 0) effects.push(`吸魔+${result.manaSteal}`);
        return effects.length > 0 ? effects.join(" | ") : "无特效";
    }

    /**
     * 🆕 格式化经济奖励信息
     */
    private formatEconomicRewards(result: any): string {
        const rewards = [];
        if (result.goldGained > 0) rewards.push(`金币+${result.goldGained}`);
        if (result.yuanbaoGained > 0) rewards.push(`元宝+${result.yuanbaoGained}`);
        return rewards.length > 0 ? rewards.join(" ") : "";
    }

    /**
     * 🆕 格式化完整奖励信息
     */
    public formatRewards(result: any): {
        gold: string;
        yuanbao: string;
        experience: string;
        items: string;
    } {
        return {
            gold: result.goldGained > 0 ? `金币+${result.goldGained}` : "",
            yuanbao: result.yuanbaoGained > 0 ? `元宝+${result.yuanbaoGained}` : "",
            experience: result.experienceGained > 0 ? `经验+${result.experienceGained}` : `经验+${result.exp || 0}`,
            items: result.itemsGainedString || result.dropItems?.join(", ") || ""
        };
    }
}
