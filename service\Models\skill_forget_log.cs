﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///技能遗忘记录表
    ///</summary>
    [SugarTable("skill_forget_log")]
    public partial class skill_forget_log
    {
           public skill_forget_log(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int pet_id {get;set;}

           /// <summary>
           /// Desc:技能ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string skill_id {get;set;}

           /// <summary>
           /// Desc:遗忘时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? forget_time {get;set;}

    }
}
