# Cocos Creator 3.8 渲染管线模块相关接口规则文档

## 一、概述
RenderPipeline 用于控制场景的渲染流程，涵盖光照管理、物体剔除、渲染物体排序、渲染目标切换等操作。由于不同项目对各渲染阶段的优化处理方式不同，统一的渲染流程难以实现最优效果。可定制化的渲染管线能对渲染场景的每个阶段进行更灵活的控制，从而针对不同项目制定更深入的优化方案。

## 二、命名空间
### 1. pipeline
此命名空间可能包含渲染管线的核心功能和基础类，是构建渲染管线的基础模块，为其他相关类和接口提供支持。
### 2. postProcess
该命名空间主要涉及后处理相关的功能和类，后处理是在场景渲染完成后对图像进行的一系列处理，如添加特效、调整颜色等。
### 3. rendering
推测该命名空间包含渲染过程中通用的功能和工具，可能涉及渲染算法、渲染状态管理等方面。

## 三、类
### 1. BasePass
#### 说明
渲染阶段的基类，定义了渲染阶段的基本结构和行为，其他具体的渲染阶段类可以继承自它。
#### 使用方式
```typescript
import { BasePass } from 'cc';
class CustomPass extends BasePass {
    constructor() {
        super();
    }
    // 重写渲染方法
    render() {
        // 实现自定义渲染逻辑
    }
}
```
### 2. BlitScreen
#### 说明
用于将一个渲染目标的内容复制到另一个渲染目标，通常用于后处理中的图像复制操作。
#### 使用方式
```typescript
import { BlitScreen, RenderTexture } from 'cc';
const sourceTexture = new RenderTexture();
const destinationTexture = new RenderTexture();
const blitScreen = new BlitScreen();
blitScreen.blit(sourceTexture, destinationTexture);
```
### 3. BlitScreenPass
#### 说明
基于 `BlitScreen` 实现的渲染阶段，专门用于执行屏幕复制操作。
#### 使用方式
```typescript
import { BlitScreenPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const blitScreenPass = new BlitScreenPass();
renderPipeline.addPass(blitScreenPass);
```
### 4. Bloom
#### 说明
实现 Bloom（泛光）效果的类，Bloom 效果可以使场景中的明亮区域产生光晕，增强视觉效果。
#### 使用方式
```typescript
import { Bloom, Camera } from 'cc';
const camera = this.node.getComponent(Camera);
const bloom = new Bloom();
camera.addPostProcess(bloom);
```
### 5. BloomPass
#### 说明
执行 Bloom 效果渲染的阶段，负责计算和应用 Bloom 效果。
#### 使用方式
```typescript
import { BloomPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const bloomPass = new BloomPass();
renderPipeline.addPass(bloomPass);
```
### 6. BloomStage
#### 说明
Bloom 后处理阶段，管理 Bloom 效果的整个处理流程。
#### 使用方式
```typescript
import { BloomStage, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const bloomStage = new BloomStage();
renderPipeline.addStage(bloomStage);
```
### 7. ColorGrading
#### 说明
用于颜色分级的类，可以对场景的颜色进行调整和校正，实现不同的色彩风格。
#### 使用方式
```typescript
import { ColorGrading, Camera } from 'cc';
const camera = this.node.getComponent(Camera);
const colorGrading = new ColorGrading();
// 设置颜色分级参数
colorGrading.setParams({ ... });
camera.addPostProcess(colorGrading);
```
### 8. ColorGradingPass
#### 说明
执行颜色分级渲染的阶段，负责应用颜色分级效果。
#### 使用方式
```typescript
import { ColorGradingPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const colorGradingPass = new ColorGradingPass();
renderPipeline.addPass(colorGradingPass);
```
### 9. CopyPair
#### 说明
用于存储一对渲染目标，通常用于在渲染过程中进行数据的复制和交换。
#### 使用方式
```typescript
import { CopyPair, RenderTexture } from 'cc';
const sourceTexture = new RenderTexture();
const destinationTexture = new RenderTexture();
const copyPair = new CopyPair(sourceTexture, destinationTexture);
```
### 10. DebugView
#### 说明
渲染调试控制类，用于在调试过程中显示渲染管线的相关信息，如渲染目标、光照信息等。
#### 使用方式
```typescript
import { DebugView, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const debugView = new DebugView();
renderPipeline.setDebugView(debugView);
// 开启调试视图
debugView.enable();
```
### 11. DeferredPipeline
#### 说明
延迟渲染管线，采用延迟渲染的方式进行场景渲染，适合处理复杂的光照和阴影效果。
#### 使用方式
```typescript
import { DeferredPipeline, Scene } from 'cc';
const scene = new Scene();
const deferredPipeline = new DeferredPipeline();
scene.renderPipeline = deferredPipeline;
```
### 12. Descriptor
#### 说明
描述渲染资源的类，包含资源的类型、大小、格式等信息，用于在渲染管线中管理和配置资源。
#### 使用方式
```typescript
import { Descriptor } from 'cc';
const descriptor = new Descriptor();
descriptor.type = 'texture';
descriptor.size = { width: 1024, height: 1024 };
```
### 13. DescriptorBlock
#### 说明
用于组织和管理多个 `Descriptor` 的类，方便对一组相关的渲染资源进行统一管理。
#### 使用方式
```typescript
import { DescriptorBlock, Descriptor } from 'cc';
const descriptorBlock = new DescriptorBlock();
const descriptor1 = new Descriptor();
const descriptor2 = new Descriptor();
descriptorBlock.addDescriptor(descriptor1);
descriptorBlock.addDescriptor(descriptor2);
```
### 14. DescriptorBlockFlattened
#### 说明
将 `DescriptorBlock` 扁平化的类，便于在渲染管线中进行快速访问和处理。
#### 使用方式
```typescript
import { DescriptorBlockFlattened, DescriptorBlock } from 'cc';
const descriptorBlock = new DescriptorBlock();
// 添加描述符...
const flattenedBlock = new DescriptorBlockFlattened(descriptorBlock);
```
### 15. DescriptorBlockIndex
#### 说明
用于索引 `DescriptorBlock` 中描述符的类，方便快速定位和访问特定的描述符。
#### 使用方式
```typescript
import { DescriptorBlockIndex, DescriptorBlock } from 'cc';
const descriptorBlock = new DescriptorBlock();
// 添加描述符...
const index = new DescriptorBlockIndex(descriptorBlock);
const descriptor = index.getDescriptor(0);
```
### 16. DOF
#### 说明
实现景深（Depth of Field）效果的类，景深效果可以使场景中的部分区域清晰，部分区域模糊，增强场景的真实感。
#### 使用方式
```typescript
import { DOF, Camera } from 'cc';
const camera = this.node.getComponent(Camera);
const dof = new DOF();
camera.addPostProcess(dof);
```
### 17. DofPass
#### 说明
执行景深效果渲染的阶段，负责计算和应用景深效果。
#### 使用方式
```typescript
import { DofPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const dofPass = new DofPass();
renderPipeline.addPass(dofPass);
```
### 18. FloatOutputProcessPass
#### 说明
用于处理浮点输出的渲染阶段，可能用于处理高精度的渲染数据。
#### 使用方式
```typescript
import { FloatOutputProcessPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const floatOutputProcessPass = new FloatOutputProcessPass();
renderPipeline.addPass(floatOutputProcessPass);
```
### 19. ForwardFinalPass
#### 说明
前向渲染的最终阶段，负责完成前向渲染的最后步骤，如合并渲染结果、输出最终图像等。
#### 使用方式
```typescript
import { ForwardFinalPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const forwardFinalPass = new ForwardFinalPass();
renderPipeline.addPass(forwardFinalPass);
```
### 20. ForwardFlow
#### 说明
前向渲染流程类，管理前向渲染的整个流程，包括多个渲染阶段的调度和执行。
#### 使用方式
```typescript
import { ForwardFlow, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const forwardFlow = new ForwardFlow();
renderPipeline.addFlow(forwardFlow);
```
### 21. ForwardPass
#### 说明
前向渲染的基本阶段类，定义了前向渲染阶段的基本行为和属性。
#### 使用方式
```typescript
import { ForwardPass } from 'cc';
class CustomForwardPass extends ForwardPass {
    constructor() {
        super();
    }
    // 重写渲染方法
    render() {
        // 实现自定义前向渲染逻辑
    }
}
```
### 22. ForwardPipeline
#### 说明
前向渲染管线，采用前向渲染的方式进行场景渲染，适合处理简单的场景和光照效果。
#### 使用方式
```typescript
import { ForwardPipeline, Scene } from 'cc';
const scene = new Scene();
const forwardPipeline = new ForwardPipeline();
scene.renderPipeline = forwardPipeline;
```
### 23. ForwardStage
#### 说明
前向渲染阶段类，管理前向渲染的一个特定阶段，如阴影渲染、光照计算等。
#### 使用方式
```typescript
import { ForwardStage, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const forwardStage = new ForwardStage();
renderPipeline.addStage(forwardStage);
```
### 24. ForwardTransparencyPass
#### 说明
用于处理前向渲染中透明物体的渲染阶段，确保透明物体的正确渲染。
#### 使用方式
```typescript
import { ForwardTransparencyPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const forwardTransparencyPass = new ForwardTransparencyPass();
renderPipeline.addPass(forwardTransparencyPass);
```
### 25. ForwardTransparencySimplePass
#### 说明
简化版的前向透明物体渲染阶段，可能采用了更简单的算法来提高渲染效率。
#### 使用方式
```typescript
import { ForwardTransparencySimplePass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const forwardTransparencySimplePass = new ForwardTransparencySimplePass();
renderPipeline.addPass(forwardTransparencySimplePass);
```
### 26. FSR
#### 说明
实现 FSR（ FidelityFX Super Resolution）超分辨率技术的类，FSR 可以在不降低画质的前提下提高渲染的分辨率。
#### 使用方式
```typescript
import { FSR, Camera } from 'cc';
const camera = this.node.getComponent(Camera);
const fsr = new FSR();
camera.addPostProcess(fsr);
```
### 27. FSRPass
#### 说明
执行 FSR 超分辨率渲染的阶段，负责计算和应用 FSR 效果。
#### 使用方式
```typescript
import { FSRPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const fsrPass = new FSRPass();
renderPipeline.addPass(fsrPass);
```
### 28. FxaaPass
#### 说明
执行 FXAA（Fast Approximate Anti - Aliasing）抗锯齿效果的渲染阶段，FXAA 可以快速地减少图像的锯齿现象。
#### 使用方式
```typescript
import { FxaaPass, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const fxaaPass = new FxaaPass();
renderPipeline.addPass(fxaaPass);
```
### 29. GbufferStage
#### 说明
前向渲染阶段，主要用于生成 G - buffer（几何缓冲区），G - buffer 包含了场景的几何信息，用于后续的光照计算。
#### 使用方式
```typescript
import { GbufferStage, RenderPipeline } from 'cc';
const renderPipeline = new RenderPipeline();
const gbufferStage = new GbufferStage();
renderPipeline.addStage(gbufferStage);
```
### 30. GeometryRenderer
#### 说明
负责渲染场景中几何物体的类，管理几何物体的渲染过程，包括顶点处理、图元装配等。
#### 使用方式
```typescript
import { GeometryRenderer, Scene } from 'cc';
const scene = new Scene();
const geometryRenderer = new GeometryRenderer();
scene.geometryRenderer = geometryRenderer;
```
### 31. HBAO
#### 说明
实现 HBAO（Horizon - Based Ambient Occlusion）环境光遮蔽效果的类，HBAO 可以增强场景的真实感和立体感。
#### 使用方式
```typescript
import { HBAO, Camera } from 'cc';
const camera = this.node.getComponent(Camera);
const hbao = new HBAO();
camera.addPostProcess(hbao);
```
### 32. InstancedBuffer
#### 说明
用于管理实例化渲染的缓冲区类，实例化渲染可以提高大量相同物体的渲染效率。
#### 使用方式
```typescript
import { InstancedBuffer, MeshRenderer } from 'cc';
const meshRenderer = this.node.getComponent(MeshRenderer);
const instancedBuffer = new InstancedBuffer();
meshRenderer.instancedBuffer = instancedBuffer;
```

综上所述，Cocos Creator 3.8 的渲染管线模块提供了丰富的接口和类，通过合理使用这些接口和类，开发者可以定制出适合不同项目需求的渲染管线，实现各种复杂的渲染效果。