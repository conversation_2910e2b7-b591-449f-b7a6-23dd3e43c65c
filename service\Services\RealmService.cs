using WebApplication_HM.Models;
using SqlSugar;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 境界系统服务
    /// 基于原项目WindowsFormsApplication7的境界系统完整迁移
    /// </summary>
    public class RealmService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<RealmService> _logger;

        public RealmService(ISqlSugarClient db, ILogger<RealmService> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 获取宠物当前境界信息
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>境界信息</returns>
        public async Task<realm_config?> GetPetRealmAsync(int petId)
        {
            try
            {
                var pet = await _db.Queryable<user_pet>()
                    .Where(x => x.id == petId)
                    .FirstAsync();

                if (pet == null || string.IsNullOrEmpty(pet.realm))
                    return null;

                var realmLevel = int.Parse(pet.realm);
                return await _db.Queryable<realm_config>()
                    .Where(x => x.realm_level == realmLevel)
                    .FirstAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物境界信息失败，宠物ID: {PetId}", petId);
                return null;
            }
        }

        /// <summary>
        /// 计算境界属性加成 (基于原项目PetCalc.cs算法)
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>境界属性加成倍率</returns>
        public async Task<double> CalculateRealmAttributeBonusAsync(int petId)
        {
            try
            {
                var pet = await _db.Queryable<user_pet>()
                    .Where(x => x.id == petId)
                    .FirstAsync();

                if (pet == null || string.IsNullOrEmpty(pet.realm))
                    return 1.0; // 无境界加成

                var realmLevel = int.Parse(pet.realm);
                
                // 基于原项目算法计算境界加成
                double stateBuffer = 1.0;
                
                if (realmLevel < 16)
                {
                    // 16级以下，每级增加1%的属性
                    stateBuffer = 1.0 + (double)realmLevel / 100.0;
                }
                else if (realmLevel >= 16)
                {
                    // 16级以上，基础16% + 每级额外3%的属性
                    stateBuffer = 1.0 + 0.16 + ((double)(realmLevel - 15) / 100.0) * 3;
                }

                return stateBuffer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算境界属性加成失败，宠物ID: {PetId}", petId);
                return 1.0;
            }
        }

        /// <summary>
        /// 境界提升 (基于原项目"修炼丹"逻辑)
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物ID</param>
        /// <returns>提升结果</returns>
        public async Task<RealmUpgradeResultDTO> UpgradeRealmAsync(int userId, int petId)
        {
            try
            {
                // 1. 验证宠物
                var pet = await _db.Queryable<user_pet>()
                    .Where(x => x.id == petId && x.user_id == userId)
                    .FirstAsync();

                if (pet == null)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "宠物不存在或不属于该用户"
                    };
                }

                // 2. 验证等级要求 (满级才能提升境界)
                if (pet.level < 130)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "只有满级宠物才能提升境界！"
                    };
                }

                // 3. 获取当前境界信息
                var currentRealmLevel = string.IsNullOrEmpty(pet.realm) ? 1 : int.Parse(pet.realm);
                var currentRealm = await _db.Queryable<realm_config>()
                    .Where(x => x.realm_level == currentRealmLevel)
                    .FirstAsync();

                if (currentRealm == null)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "当前境界配置不存在"
                    };
                }

                // 4. 检查是否已达到最高境界
                if (currentRealm.realm_name == "天地同寿")
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "已达到天地同寿境界，无法继续提升"
                    };
                }

                // 5. 验证升级道具和金币
                var user = await _db.Queryable<user>()
                    .Where(x => x.id == userId)
                    .FirstAsync();

                if (user == null || user.gold < currentRealm.upgrade_cost_gold)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = $"金币不足，需要{currentRealm.upgrade_cost_gold}金币"
                    };
                }

                // 6. 检查升级道具
                if (!string.IsNullOrEmpty(currentRealm.upgrade_item_required))
                {
                    var hasItem = await _db.Queryable<user_item>()
                        .Where(x => x.user_id == userId && x.item_id == currentRealm.upgrade_item_required && x.item_count > 0)
                        .AnyAsync();

                    if (!hasItem)
                    {
                        return new RealmUpgradeResultDTO
                        {
                            Success = false,
                            Message = $"缺少升级道具：{currentRealm.upgrade_item_required}"
                        };
                    }
                }

                // 7. 计算升级成功率 (基于原项目2.22%成功率)
                var random = new Random();
                var successRoll = random.Next(1, 101);
                bool isSuccess = successRoll <= currentRealm.upgrade_success_rate;

                // 8. 开启事务处理升级
                await _db.Ado.BeginTranAsync();

                try
                {
                    // 扣除金币
                    await _db.Updateable<user>()
                        .SetColumns(x => x.gold == x.gold - currentRealm.upgrade_cost_gold)
                        .Where(x => x.id == userId)
                        .ExecuteCommandAsync();

                    // 扣除道具
                    if (!string.IsNullOrEmpty(currentRealm.upgrade_item_required))
                    {
                        await _db.Updateable<user_item>()
                            .SetColumns(x => x.item_count == x.item_count - 1)
                            .Where(x => x.user_id == userId && x.item_id == currentRealm.upgrade_item_required)
                            .ExecuteCommandAsync();
                    }

                    var result = new RealmUpgradeResultDTO();

                    if (isSuccess)
                    {
                        // 升级成功
                        var nextRealmLevel = currentRealmLevel + 1;
                        var nextRealm = await _db.Queryable<realm_config>()
                            .Where(x => x.realm_level == nextRealmLevel)
                            .FirstAsync();

                        if (nextRealm != null)
                        {
                            // 更新宠物境界，重置等级和经验 (基于原项目逻辑)
                            await _db.Updateable<user_pet>()
                                .SetColumns(x => new user_pet
                                {
                                    realm = nextRealmLevel.ToString(),
                                    level = 1,
                                    exp = 1
                                })
                                .Where(x => x.id == petId)
                                .ExecuteCommandAsync();

                            result.Success = true;
                            result.Message = "宠物境界提升成功！";
                            result.NewRealmName = nextRealm.realm_name;
                            result.NewRealmLevel = nextRealmLevel;
                        }
                    }
                    else
                    {
                        // 升级失败
                        result.Success = false;
                        result.Message = $"宠物境界没有丝毫提升,并损失了{currentRealm.upgrade_cost_gold}金币！";
                    }

                    // 记录升级日志
                    await _db.Insertable(new realm_upgrade_log
                    {
                        user_id = userId,
                        pet_id = petId,
                        from_realm_level = currentRealmLevel,
                        to_realm_level = isSuccess ? currentRealmLevel + 1 : currentRealmLevel,
                        upgrade_type = currentRealm.upgrade_item_required ?? "修炼丹",
                        success = isSuccess ? 1 : 0,
                        cost_gold = currentRealm.upgrade_cost_gold,
                        cost_items = currentRealm.upgrade_item_required,
                        created_at = DateTime.Now
                    }).ExecuteCommandAsync();

                    await _db.Ado.CommitTranAsync();
                    return result;
                }
                catch (Exception ex)
                {
                    await _db.Ado.RollbackTranAsync();
                    _logger.LogError(ex, "境界升级事务失败");
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "境界升级失败，用户ID: {UserId}, 宠物ID: {PetId}", userId, petId);
                return new RealmUpgradeResultDTO
                {
                    Success = false,
                    Message = $"境界升级失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 境界突破 (基于原项目"玄元丹"逻辑，适用于16级以上境界)
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物ID</param>
        /// <returns>突破结果</returns>
        public async Task<RealmUpgradeResultDTO> BreakthroughRealmAsync(int userId, int petId)
        {
            try
            {
                var pet = await _db.Queryable<user_pet>()
                    .Where(x => x.id == petId && x.user_id == userId)
                    .FirstAsync();

                if (pet == null)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "宠物不存在或不属于该用户"
                    };
                }

                if (pet.level < 130)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "只有满级宠物才能突破境界！"
                    };
                }

                var currentRealmLevel = string.IsNullOrEmpty(pet.realm) ? 1 : int.Parse(pet.realm);

                if (currentRealmLevel < 15)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "当前境界无法使用玄元丹,请先使用修炼丹提升境界！"
                    };
                }

                if (currentRealmLevel >= 36) // 神轮境
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "无法继续突破境界！"
                    };
                }

                // 检查玄元丹
                var hasXuanyuandan = await _db.Queryable<user_item>()
                    .Where(x => x.user_id == userId && x.item_id == "xuanyuandan" && x.item_count > 0)
                    .AnyAsync();

                if (!hasXuanyuandan)
                {
                    return new RealmUpgradeResultDTO
                    {
                        Success = false,
                        Message = "缺少突破道具：玄元丹"
                    };
                }

                // 玄元丹突破100%成功 (基于原项目逻辑)
                await _db.Ado.BeginTranAsync();

                try
                {
                    var nextRealmLevel = currentRealmLevel + 1;
                    var nextRealm = await _db.Queryable<realm_config>()
                        .Where(x => x.realm_level == nextRealmLevel)
                        .FirstAsync();

                    if (nextRealm != null)
                    {
                        // 扣除玄元丹
                        await _db.Updateable<user_item>()
                            .SetColumns(x => x.item_count == x.item_count - 1)
                            .Where(x => x.user_id == userId && x.item_id == "xuanyuandan")
                            .ExecuteCommandAsync();

                        // 更新宠物境界
                        await _db.Updateable<user_pet>()
                            .SetColumns(x => new user_pet
                            {
                                realm = nextRealmLevel.ToString(),
                                level = 1,
                                exp = 1
                            })
                            .Where(x => x.id == petId)
                            .ExecuteCommandAsync();

                        // 记录突破日志
                        await _db.Insertable(new realm_upgrade_log
                        {
                            user_id = userId,
                            pet_id = petId,
                            from_realm_level = currentRealmLevel,
                            to_realm_level = nextRealmLevel,
                            upgrade_type = "玄元丹",
                            success = 1,
                            cost_gold = 0,
                            cost_items = "玄元丹",
                            created_at = DateTime.Now
                        }).ExecuteCommandAsync();

                        await _db.Ado.CommitTranAsync();

                        return new RealmUpgradeResultDTO
                        {
                            Success = true,
                            Message = "宠物突破境界成功！",
                            NewRealmName = nextRealm.realm_name,
                            NewRealmLevel = nextRealmLevel
                        };
                    }
                    else
                    {
                        await _db.Ado.RollbackTranAsync();
                        return new RealmUpgradeResultDTO
                        {
                            Success = false,
                            Message = "下一境界配置不存在"
                        };
                    }
                }
                catch (Exception ex)
                {
                    await _db.Ado.RollbackTranAsync();
                    _logger.LogError(ex, "境界突破事务失败");
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "境界突破失败，用户ID: {UserId}, 宠物ID: {PetId}", userId, petId);
                return new RealmUpgradeResultDTO
                {
                    Success = false,
                    Message = $"境界突破失败：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 获取境界列表
        /// </summary>
        /// <returns>境界配置列表</returns>
        public async Task<List<realm_config>> GetRealmListAsync()
        {
            return await _db.Queryable<realm_config>()
                .OrderBy(x => x.realm_level)
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户境界升级历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="petId">宠物ID (可选)</param>
        /// <returns>升级历史</returns>
        public async Task<List<realm_upgrade_log>> GetUpgradeHistoryAsync(int userId, int? petId = null)
        {
            var query = _db.Queryable<realm_upgrade_log>()
                .Where(x => x.user_id == userId);

            if (petId.HasValue)
            {
                query = query.Where(x => x.pet_id == petId.Value);
            }

            return await query.OrderByDescending(x => x.created_at)
                .ToListAsync();
        }
    }
}
