import { Global } from '../Global';

export class HttpRequest {

    public static sendGetRequest(): void {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", "http://1.koudaisk.com:8088/api/login?username=黑马&password=qiqiwan1", true);
        xhr.responseType = "json";

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                // 请求成功，处理响应数据  
                console.log("Success:", xhr.response);
            } else {
                // 请求失败  
                console.error("Error:", xhr.statusText);
            }
        };

        xhr.onerror = function () {
            // 请求过程中发生错误  
            console.error("Request error...");
        };

        xhr.send();
    }

    public static removeSlashes(str: string): string {
        // 使用正则表达式替换所有的斜杠（\）为空字符串  
        return str.replace(/\\/g, '');
    }

    /**  
    * 发送 GET 请求  
    * @param url 请求的 URL  
    * @param params URL 参数对象  
    */
    public static get(url: string, params?: any): Promise<any> {
        let queryString = '';
        if (params) {
            const keys = Object.keys(params);
            queryString = keys.map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
        }
        const fullUrl = `${url}?${queryString}`;

        return fetch(fullUrl, {
            method: 'GET',
            headers: {
                // 'Content-Type': 'application/json', // 这行可能不需要，取决于服务器要求  
                // 其他需要的头部信息  
            },
        })
            .then(response => {
                // if (!response.ok) {
                //     throw new Error('Network response was not ok');
                // }
                console.log(response);
                return response.text();
            })
            .catch(error => console.error('Error during GET request:', error));
    }

    /**  
    * 发送 GET 请求  
    * @param url 请求的 URL  
    * @param params URL 参数对象  
    */
    public static getConvertJson(url: string, params?: any): Promise<any> {
        let queryString = '';
        if (params) {
            const keys = Object.keys(params);
            queryString = keys.map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
        }
        const fullUrl = `${url}?${queryString}`;

        return fetch(fullUrl, {
            method: 'GET',
            headers: {
                // 'Content-Type': 'application/json', // 这行可能不需要，取决于服务器要求  
                // 其他需要的头部信息  
            },
        })
            .then(response => {
                // if (!response.ok) {
                //     throw new Error('Network response was not ok');
                // }
                console.log(response);
                return response.text();
            })
            .catch(error => console.error('Error during GET request:', error));
    }

    /*
    获取数据并转换json
    */
    public static getConvertJson1(url: string, params?: any): Promise<any> {
        let queryString = '';
        if (params) {
            const keys = Object.keys(params);
            queryString = keys.map(key => `${key}=${encodeURIComponent(params[key])}`).join('&');
        }

        const fullUrl = Global.Url + `${url}?${queryString}`;

        return fetch(fullUrl, {
            method: 'GET',
            headers: {
                //'Accept': 'application/json',
            },
        })
            .then(response => {
                return response.text();
            })
            .then(data => {

                try {
                    return JSON.parse(data);
                } catch (parseError) {
                    // 如果解析失败，则抛出错误  
                    throw new Error('Failed to parse JSON data: ' + parseError.message);
                }
            })
            .catch(error => {
                console.error('Error during GET request:', error);

            });
    }

    /**  
    * 发送 POST 请求  
    * @param url 请求的 URL  
    * @param body 请求体数据  
    */
    public static post(url: string, body?: any): Promise<any> {
        return fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body)
        })
            .then(response => {
                console.log(response);
                return response.text();
            })
            .catch(error => console.error('Error during POST request:', error));
    }

    /**  
    * 发送 POST 请求并转换为 JSON  
    * @param url 请求的 URL  
    * @param body 请求体数据  
    */
    public static postConvertJson(url: string, body?: any): Promise<any> {
        const fullUrl = Global.Url + url;
        console.log(fullUrl);
        return fetch(fullUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(body)
        })
            .then(response => {
                return response.text();
            })
            .then(data => {
                try {
                    return JSON.parse(data);
                } catch (parseError) {
                    throw new Error('Failed to parse JSON data: ' + parseError.message);
                }
            })
            .catch(error => {
                console.error('Error during POST request:', error);
            });
    }

    /**
    * 发送 DELETE 请求并转换为 JSON
    * @param url 请求的 URL
    */
    public static deleteConvertJson(url: string): Promise<any> {
        const fullUrl = Global.Url + url;
        console.log("DELETE请求URL:", fullUrl);
        return fetch(fullUrl, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
            .then(response => {
                return response.text();
            })
            .then(data => {
                try {
                    return JSON.parse(data);
                } catch (parseError) {
                    throw new Error('Failed to parse JSON data: ' + parseError.message);
                }
            })
            .catch(error => {
                console.error('Error during DELETE request:', error);
            });
    }

}


