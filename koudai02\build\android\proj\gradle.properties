# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
org.gradle.jvmargs=-Xmx4608m -Dfile.encoding=UTF-8
android.useAndroidX=true
android.suppressUnsupportedCompileSdk=34

android.injected.testOnly=false

android.native.buildOutput=verbose

# Android SDK version that will be used as the compile project
PROP_COMPILE_SDK_VERSION=34

# Android SDK version that will be used as the earliest version of android this application can run on
PROP_MIN_SDK_VERSION=21

# Android SDK version that will be used as the latest version of android this application has been tested on
PROP_TARGET_SDK_VERSION=34

# Android Build Tools version that will be used as the compile project
PROP_BUILD_TOOLS_VERSION=30.0.3

# Android Application Name
PROP_APP_NAME=koudai01

# Instant App
PROP_ENABLE_INSTANT_APP=false

# InputSDK
PROP_ENABLE_INPUTSDK=false

PROP_NDK_PATH=D:\\Android\\android-ndk-r23c

# Build variant
PROP_IS_DEBUG=false

# Cocos Engine Path
COCOS_ENGINE_PATH=C:/ProgramData/cocos/editors/Creator/3.8.3/resources/resources/3d/engine/native

# Res path
RES_PATH=D:/AI OB/HM_one_cocos -test/koudai02/build/android

# Native source dir
NATIVE_DIR=D:/AI OB/HM_one_cocos -test/koudai02/native/engine/android

# Application ID
APPLICATION_ID=com.koudai.game

# List of CPU Archtexture to build that application with
# Available architextures (armeabi-v7a | arm64-v8a | x86 | x86_64)
# To build for multiple architexture, use the `:` between them
# Example - PROP_APP_ABI=arm64-v8a
PROP_APP_ABI=arm64-v8a

# fill in sign information for release mode
RELEASE_STORE_FILE=C:/ProgramData/cocos/editors/Creator/3.8.3/resources/tools/keystore/debug.keystore
RELEASE_STORE_PASSWORD=123456
RELEASE_KEY_ALIAS=debug_keystore
RELEASE_KEY_PASSWORD=123456
