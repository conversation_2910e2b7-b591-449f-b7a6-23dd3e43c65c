namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 装备强化通知DTO
    /// </summary>
    public class EquipmentEnhanceNotificationDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "equipment_enhance";

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 装备ID
        /// </summary>
        public int EquipmentId { get; set; }

        /// <summary>
        /// 装备名称
        /// </summary>
        public string EquipmentName { get; set; }

        /// <summary>
        /// 强化前等级
        /// </summary>
        public int OldLevel { get; set; }

        /// <summary>
        /// 强化后等级
        /// </summary>
        public int NewLevel { get; set; }

        /// <summary>
        /// 是否大成功
        /// </summary>
        public bool CriticalSuccess { get; set; }

        /// <summary>
        /// 强化结果描述
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 消耗的金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 消耗的材料
        /// </summary>
        public List<string> CostMaterials { get; set; } = new List<string>();

        /// <summary>
        /// 强化时间
        /// </summary>
        public DateTime EnhanceTime { get; set; } = DateTime.Now;
    }
} 