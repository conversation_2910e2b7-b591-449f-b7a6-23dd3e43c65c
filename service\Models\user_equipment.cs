﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///用户装备表
    ///</summary>
    [SugarTable("user_equipment")]
    public partial class user_equipment
    {
           public user_equipment(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:所属用户ID（关联user表）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:装备唯一ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string equip_id {get;set;}

           /// <summary>
           /// Desc:装备名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;}

           /// <summary>
           /// Desc:装备图标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? icon {get;set;}

           /// <summary>
           /// Desc:装备类型ID（关联equipment_type表）
           /// Default:
           /// Nullable:False
           /// </summary>
           public string equip_type_id {get;set;}

           /// <summary>
           /// Desc:强化等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? strengthen_level {get;set;}

           /// <summary>
           /// Desc:扩展槽位
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? slot {get;set;}

           /// <summary>
           /// Desc:装备位置（装备栏位置）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? position {get;set;}

           /// <summary>
           /// Desc:是否已装备（0-未装备，1-已装备）
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_equipped {get;set;}

           /// <summary>
           /// Desc:获得时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

           /// <summary>
           /// Desc:五行属性（金/木/水/火/土/雷/风）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? element {get;set;}

           /// <summary>
           /// Desc:关联宠物ID（装备到哪个宠物）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? pet_id {get;set;}

           /// <summary>
           /// Desc:宝石槽位数量（1-3个）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? gemstone_slots {get;set;}

           /// <summary>
           /// Desc:套装ID（关联套装系统）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? suit_id {get;set;}

           /// <summary>
           /// Desc:历史属性（灵饰装备专用）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lssx {get;set;}

           /// <summary>
           /// Desc:特殊效果（法宝等特殊装备）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? special_effect {get;set;}

    }
}
