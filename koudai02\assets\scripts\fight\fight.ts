import { _decorator, Component, Node, SpriteAtlas, SpriteFrame, resources, Sprite, Animation, Button } from 'cc';
const { ccclass, property } = _decorator;

import { fightMian } from './fightMian';
import { MassiveResourceManager } from '../manager/MassiveResourceManager';

//战斗脚本
@ccclass('fight')
export class fight extends Component {

    @property(Sprite)
    sprite!: Sprite;//精灵

    @property(String)
    Name: string = '';//名称

    @property(String)
    AtlasName: string = '';//图集名称

    @property(String)
    attackName: string = 'g';//攻击的动作

    @property(String)
    initialName: string = 'z';//初始的动作

    @property(String)
    ForwardName: string = '';//前进动画名称

    @property(String)
    RefunddName: string = '';//后退动画名称

    @property(Button)
    testBut: Button;

    private interval?: number; // 在 Cocos Creator 中，我们使用 number 类型来存储定时器 ID 

    public spriteAtlas: SpriteAtlas = null; // 图集

    private ActionFrames: { [key: string]: SpriteFrame[] } = {}; //动作

    private FtAnimation: Animation = null; //动画

    private fightMian: fightMian; //攻击主脚本，用于回调播放完成通知

    //图像前缀：g、S:攻击 z:动态 q、k、t:头像

    async start() {
        try {
            //加载战斗动画组件
            this.FtAnimation = this.node.getComponent(Animation);
            if (!this.FtAnimation) {
                console.error('❌ 动画组件加载失败');
                return;
            }

            // 使用大规模资源管理器加载图集
            const resourceManager = MassiveResourceManager.getInstance();
            console.log(`🎮 开始加载图集: ${this.AtlasName}`);
            
            // 智能加载：根据名称ID确定使用哪个图集
            const entityId = parseInt(this.Name) || 1;
            let atlasPath = this.AtlasName;
            
            // 如果是宠物，使用智能分组
            if (this.AtlasName.includes('pet')) {
                const { atlas } = resourceManager.getPetGroup(entityId);
                atlasPath = atlas;
            }
            // 如果是怪物，使用智能分组
            else if (this.AtlasName.includes('monster')) {
                const { atlas } = resourceManager.getMonsterGroup(entityId);
                atlasPath = atlas;
            }
            
            this.spriteAtlas = await resourceManager.loadAtlas(atlasPath);
            
            // 🔧 修复：设置Sprite组件的SpriteAtlas
            if (this.sprite && this.spriteAtlas) {
                this.sprite.spriteFrame = null; // 先清空
                console.log(`🖼️ 设置Sprite组件的SpriteAtlas: ${atlasPath}`);
            }
            
            // 预加载动作帧
            console.log(`🎬 预加载初始动作: ${this.initialName + this.Name}`);
            this.loadActionFrames(this.initialName + this.Name);
            console.log(`🎬 预加载攻击动作: ${this.attackName + this.Name}`);
            this.loadActionFrames(this.attackName + this.Name);
            
            // 🔧 修复：播放默认动作并显示第一帧
            console.log(`▶️ 开始播放默认动作: ${this.initialName + this.Name}`);
            this.playAction(this.initialName + this.Name);
            
            console.log(`✅ 图集加载完成: ${atlasPath}, 实体ID: ${entityId}`);
            
        } catch (error) {
            console.error('❌ 资源加载失败:', error);
            this.showLoadingError();
        }
    }

    /**
     * 显示资源加载失败的提示
     */
    private showLoadingError(): void {
        console.error(`❌ 图集${this.AtlasName}加载失败，请检查资源路径和网络连接`);
        // 可以在这里添加UI提示
    }

    update(deltaTime: number) {

    }

    onDestroy() {
        // 清除定时器
        clearInterval(this.interval);
    }

    //移动并攻击
    public playFirstAnimation(fightPanel: Node) {
        this.fightMian = fightPanel.getComponent(fightMian);

        if (this.FtAnimation) {
            this.FtAnimation.play(this.ForwardName); // 使用动画名称播放  
        }

    }

    //退回结束攻击
    private stopFirstAnimation() {
        if (this.FtAnimation) {
            this.FtAnimation.play(this.RefunddName); // 使用动画名称播放  
        }

    }

    //移动完成： 攻击函数，在移动动画播放完成后侦事件调用
    public attackFn() {
        // 🔧 修复：播放攻击动作序列（g类型）
        this.playAction(this.attackName + this.Name);
        if (this.node.name == 'petNode') {
            this.fightMian.PetshowNotice();//显示伤害值面板
        }

        if (this.node.name == 'monsterNode') {
            this.fightMian.MonstershowNotice();//显示伤害值面板
        }

    }

    //退回完成：动画完成后播放初始动画
    public initialFn() {
        // 🔧 修复：播放初始动作序列（z类型）
        this.playAction(this.initialName + this.Name);

        //调用fightMian，表示已播放完成动画
        if (this.fightMian) {
            this.fightMian.attackOver(this.node.name);
        }

        if (this.node.name == 'petNode') {
            this.fightMian.PethaideNotice();//隐藏伤害值面板
        }

        if (this.node.name == 'monsterNode') {
            this.fightMian.MonsterhideNotice();//隐藏伤害值面板
        }
    }

    //加载动作帧
    public loadActionFrames(ActionName: string) {
        // 🔧 修复：动作帧加载逻辑，正确处理序列格式
        console.log(`🎬 开始加载动作帧: ${ActionName}`);
        
        // 初始化动作帧 默认最大支持加载20帧
        this.ActionFrames[ActionName] = [];
        
        for (let i = 0; i <= 20; i++) {
            const frameName = `${ActionName}-${i}`;
            const frame = this.spriteAtlas.getSpriteFrame(frameName);
            if (frame) {
                this.ActionFrames[ActionName].push(frame);
                console.log(`✅ 加载动作帧: ${frameName}`);
            } else {
                // 如果连续几帧都找不到，说明序列结束
                if (i > 0 && this.ActionFrames[ActionName].length === 0) {
                    console.warn(`⚠️ 动作${ActionName}未找到任何帧`);
                    break;
                } else if (i > 0 && this.ActionFrames[ActionName].length > 0) {
                    // 找到了一些帧但当前帧不存在，序列可能结束
                    console.log(`✅ 动作${ActionName}加载完成，共${this.ActionFrames[ActionName].length}帧`);
                    break;
                }
            }
        }
        
        // 如果没有找到任何帧，尝试查找单帧（用于头像等静态资源）
        if (this.ActionFrames[ActionName].length === 0) {
            const singleFrame = this.spriteAtlas.getSpriteFrame(ActionName);
            if (singleFrame) {
                this.ActionFrames[ActionName].push(singleFrame);
                console.log(`✅ 加载单帧: ${ActionName}`);
            } else {
                console.error(`❌ 未找到动作帧: ${ActionName} 或 ${ActionName}-0`);
                delete this.ActionFrames[ActionName]; // 清理空数组
            }
        }
    }

    /**
     * 🔧 清空所有动作帧（供外部调用）
     */
    public clearActionFrames(): void {
        this.ActionFrames = {};
        console.log("🧹 已清空所有动作帧");
    }

    /**
     * 🔧 重新初始化动作帧（供外部调用）
     */
    public reinitializeActions(): void {
        this.clearActionFrames();
        
        const initialAction = this.initialName + this.Name;
        const attackAction = this.attackName + this.Name;
        
        console.log(`🔄 重新初始化动作: ${initialAction}, ${attackAction}`);
        this.loadActionFrames(initialAction);
        this.loadActionFrames(attackAction);
        
        // 播放默认动作
        this.playAction(initialAction);
    }

    // 播放动作函数，actionType 例：'z32' 或 'g32'
    public playAction(actionType: string) {
        const frames = this.ActionFrames[actionType];
        if (frames && frames.length > 0) {
            console.log(`🎬 播放动作: ${actionType}, 共${frames.length}帧`);
            
            // 🔧 修复：立即显示第一帧
            this.sprite.spriteFrame = frames[0];
            console.log(`🖼️ 显示第一帧: ${actionType}-0`);
            
            // 清除之前的定时器  
            if (this.interval) {
                clearInterval(this.interval);
                this.interval = undefined;
            }

            // 如果只有一帧或者不需要动画循环，就只显示第一帧
            if (frames.length === 1) {
                console.log(`📋 动作${actionType}只有1帧，静态显示`);
                return;
            }

            let index = 0;
            this.interval = setInterval(() => {
                this.sprite.spriteFrame = frames[index];
                index = (index + 1) % frames.length;

                if (index >= (frames.length - 1) && actionType === this.attackName + this.Name) {
                    // 过了260毫秒后播放完最后一张动画调用  
                    this.scheduleOnce(() => {
                        clearInterval(this.interval); // 清除定时器，动画播放完毕  
                        this.interval = undefined;
                        // 动画播放完毕后执行的操作  
                        this.stopFirstAnimation(); // 播放完攻击动画后执行  
                    }, 0.8); // 0.8秒后执行  
                }
            }, 260); // 260 毫秒切换一帧  
        } else {
            console.warn(`⚠️ 动作${actionType}没有可用的帧或未加载`);
        }
    }

}


