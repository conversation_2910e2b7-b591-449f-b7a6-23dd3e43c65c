import { _decorator, Component, Node, ProgressBar, Label, Sprite, find } from 'cc';
const { ccclass, property } = _decorator;

//怪物信息栏脚本
@ccclass('monsterInformation')
export class monsterInformation extends Component {

    HP_monster: ProgressBar | null = null;//怪物
    public Nmae: Label;//宠物名称
    public grade: Label;//等级

    start() {
        const HP_monsterNode: Node = find("/HP_monster", this.node);//读取宠物节点
        const NameCode: Node = find("/dr02/name", this.node);//读取名称节点
        const gradeCode: Node = find("/dr02/grade", this.node);//读取等级节点

        if (!HP_monsterNode) {
            console.error("HP_monsterNode节点不存在");
            return;
        }

        if (!NameCode) {
            console.error("怪物NameCode节点不存在");
            return;
        }

        if (!gradeCode) {
            console.error("怪物gradeCode节点不存在");
            return;
        }

        this.HP_monster = HP_monsterNode.getComponent(ProgressBar);//获取宠物节点
        this.Nmae = NameCode.getComponent(Label);//获取名称节点
        this.grade = gradeCode.getComponent(Label);//获取等级节点
    }

    update(deltaTime: number) {

    }

    //更新进度条
    public UpdateProgressBar(Name: string, value: number) {
        if (Name == "HP") {
            this.HP_monster.progress = value;
        }
    }

    //更新宠物信息
    public UpdateMonsterInformation(Name: string, grade: number) {
        this.Nmae.string = Name;
        this.grade.string = grade.toString();
    }



}


