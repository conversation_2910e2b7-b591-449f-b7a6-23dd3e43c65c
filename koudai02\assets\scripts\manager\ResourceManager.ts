import { SpriteAtlas, SpriteFrame, Prefab, AudioClip, resources, JsonAsset, Asset } from 'cc';

/**
 * 统一资源管理器 - 管理项目中所有动态加载的资源
 * 提供缓存、预加载、错误处理等功能
 */
export class ResourceManager {
    private static _instance: ResourceManager;
    private _loadedAssets: Map<string, any> = new Map();
    private _loadingPromises: Map<string, Promise<any>> = new Map();

    public static getInstance(): ResourceManager {
        if (!ResourceManager._instance) {
            ResourceManager._instance = new ResourceManager();
        }
        return ResourceManager._instance;
    }

    /**
     * 预加载关键资源
     */
    public async preloadCriticalAssets(): Promise<void> {
        const criticalAssets = [
            'pets/starter_atlas',    // 新手宠物图集
            'monsters/normal_atlas', // 普通怪物图集
            'head/t32'              // 默认头像
        ];

        console.log('开始预加载关键资源...');
        const promises = criticalAssets.map(path => this.preloadAsset(path));
        
        try {
            await Promise.all(promises);
            console.log('✅ 关键资源预加载完成');
        } catch (error) {
            console.warn('⚠️ 部分关键资源预加载失败，但不影响游戏运行:', error);
        }
    }

    /**
     * 预加载单个资源
     */
    private preloadAsset(path: string): Promise<void> {
        return new Promise((resolve, reject) => {
            resources.preload(path, (err) => {
                if (err) {
                    console.error(`预加载失败: ${path}`, err);
                    reject(err);
                } else {
                    console.log(`✅ 预加载成功: ${path}`);
                    resolve();
                }
            });
        });
    }

    /**
     * 加载图集
     */
    public async loadAtlas(path: string): Promise<SpriteAtlas> {
        return this.loadResource<SpriteAtlas>(path, SpriteAtlas);
    }

    /**
     * 加载单个SpriteFrame
     */
    public async loadSpriteFrame(path: string): Promise<SpriteFrame> {
        return this.loadResource<SpriteFrame>(path, SpriteFrame);
    }

    /**
     * 加载预制体
     */
    public async loadPrefab(path: string): Promise<Prefab> {
        return this.loadResource<Prefab>(path, Prefab);
    }

    /**
     * 加载音频
     */
    public async loadAudio(path: string): Promise<AudioClip> {
        return this.loadResource<AudioClip>(path, AudioClip);
    }

    /**
     * 加载JSON配置
     */
    public async loadJson(path: string): Promise<any> {
        const jsonAsset = await this.loadResource<JsonAsset>(path, JsonAsset);
        return jsonAsset.json;
    }

    /**
     * 通用资源加载方法
     */
    private async loadResource<T>(path: string, type: any): Promise<T> {
        // 检查缓存
        if (this._loadedAssets.has(path)) {
            console.log(`📦 从缓存加载: ${path}`);
            return this._loadedAssets.get(path);
        }

        // 检查是否正在加载
        if (this._loadingPromises.has(path)) {
            console.log(`⏳ 等待加载完成: ${path}`);
            return this._loadingPromises.get(path);
        }

        // 开始加载
        const promise = new Promise<T>((resolve, reject) => {
            resources.load(path, type, (err, asset) => {
                this._loadingPromises.delete(path);
                
                if (err) {
                    console.error(`❌ 加载资源失败: ${path}`, err);
                    reject(err);
                } else {
                    this._loadedAssets.set(path, asset);
                    console.log(`✅ 加载资源成功: ${path}`);
                    resolve(asset as T);
                }
            });
        });

        this._loadingPromises.set(path, promise);
        return promise;
    }

    /**
     * 批量加载目录下的资源
     */
    public async loadDir(path: string, type?: any): Promise<any[]> {
        return new Promise((resolve, reject) => {
            resources.loadDir(path, type, (err, assets) => {
                if (err) {
                    console.error(`❌ 批量加载失败: ${path}`, err);
                    reject(err);
                } else {
                    // 缓存所有加载的资源
                    assets.forEach((asset) => {
                        const assetPath = `${path}/${asset.name}`;
                        this._loadedAssets.set(assetPath, asset);
                    });
                    console.log(`✅ 批量加载成功: ${path}，共${assets.length}个资源`);
                    resolve(assets);
                }
            });
        });
    }

    /**
     * 释放资源
     */
    public releaseAsset(path: string): void {
        const asset = this._loadedAssets.get(path);
        if (asset) {
            resources.release(asset);
            this._loadedAssets.delete(path);
            console.log(`🗑️ 释放资源: ${path}`);
        }
    }

    /**
     * 释放所有资源
     */
    public releaseAll(): void {
        this._loadedAssets.forEach((asset, path) => {
            resources.release(asset);
        });
        this._loadedAssets.clear();
        console.log('🗑️ 释放所有缓存资源');
    }

    /**
     * 获取缓存的资源
     */
    public getCachedAsset<T>(path: string): T | null {
        return this._loadedAssets.get(path) || null;
    }

    /**
     * 获取资源加载状态信息
     */
    public getLoadingStats(): { loaded: number, loading: number } {
        return {
            loaded: this._loadedAssets.size,
            loading: this._loadingPromises.size
        };
    }
}