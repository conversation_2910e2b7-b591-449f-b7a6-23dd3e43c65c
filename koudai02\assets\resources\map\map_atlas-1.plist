<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{793,1},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>13-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{792,941},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>14-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{1,944},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>17-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{791,1254},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>19-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{1,1257},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{1,316},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>20-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{1,1570},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>23-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{791,1567},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{793,314},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{792,627},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{1,630},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>8-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{790,313}</string>
                <key>spriteSourceSize</key>
                <string>{790,313}</string>
                <key>textureRect</key>
                <string>{{1,1},{790,313}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>map_atlas-1.png</string>
            <key>size</key>
            <string>{1582,1882}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:d4017e87b3a68bcffc1d90d3b6ee1833:07fbf581f2476ddf0f3607bf00146ce6:75ae273111d95dc9dde1c904a9ab475b$</string>
            <key>textureFileName</key>
            <string>map_atlas-1.png</string>
        </dict>
    </dict>
</plist>
