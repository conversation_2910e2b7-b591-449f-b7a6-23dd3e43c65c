# Cocos Creator 3.8 2D 粒子系统接口规则文档

## 一、类
### 1. MotionStreak
#### 说明
`MotionStreak` 是用于在游戏对象的运动轨迹上实现拖尾渐隐效果的类。在游戏中，常用于表现角色快速移动时留下的残影，或者武器挥舞时的轨迹效果，能增强游戏的视觉效果和动态感。
#### 使用方式
```typescript
import { MotionStreak, Node } from 'cc';
// 创建一个节点
const node = new Node();
// 为节点添加 MotionStreak 组件
const motionStreak = node.addComponent(MotionStreak);
// 设置拖尾的宽度
motionStreak.stroke = 10;
// 设置拖尾的颜色
motionStreak.color.set(255, 0, 0);
// 设置拖尾的持续时间
motionStreak.fadeTime = 1.0;
```
### 2. ParticleAsset
#### 说明
`ParticleAsset` 是 2D 粒子资产类，用于存储粒子系统的相关配置信息，如粒子的发射模式、生命周期、颜色变化等。可以通过该类加载和管理粒子系统的资源。
#### 使用方式
```typescript
import { ParticleAsset, resources } from 'cc';
// 加载粒子资产
resources.load('particleAssets/myParticleAsset', (err, asset: ParticleAsset) => { 
    if (!err) { 
        // 加载成功，使用粒子资产 
        console.log('粒子资产加载成功'); 
    } else { 
        console.error('粒子资产加载失败:', err); 
    } 
});
```
### 3. ParticleSystem2D
#### 说明
`ParticleSystem2D` 是 2D 粒子基础类型，用于在 2D 世界下进行特效的开发。它支持由 `Particle Designer` 生成的粒子，并且在运行时可以自定义粒子的多种属性，如旋转粒子、切向加速度、径向加速度等。
#### 使用方式
```typescript
import { ParticleSystem2D, Node, resources } from 'cc';
// 创建一个节点
const node = new Node();
// 为节点添加 ParticleSystem2D 组件
const particleSystem = node.addComponent(ParticleSystem2D);
// 加载粒子资产并设置给粒子系统
resources.load('particleAssets/myParticleAsset', (err, asset: ParticleAsset) => { 
    if (!err) { 
        particleSystem.asset = asset; 
        // 播放粒子系统 
        particleSystem.play(); 
    } else { 
        console.error('粒子资产加载失败:', err); 
    } 
});
// 在运行时自定义属性
particleSystem.radialAccel = 15;
particleSystem.startSpin = 0;
```

## 二、变量
### 1. MotionStreakAssemblerManager
#### 说明
`MotionStreakAssemblerManager` 可能是用于管理 `MotionStreak` 组件渲染装配的管理器。它负责协调 `MotionStreak` 组件的渲染数据和渲染流程，确保拖尾效果能够正确地显示在屏幕上。
#### 使用方式
目前文档未提供具体使用方式，推测可能在自定义渲染流程或者优化渲染性能时会使用到。例如，在自定义渲染管线中，可以通过该管理器来定制 `MotionStreak` 的渲染逻辑。
### 2. ParticleSystem2DAssembler
#### 说明
`ParticleSystem2DAssembler` 用于处理 `ParticleSystem2D` 组件的渲染装配工作。它将粒子系统的各种属性和数据转换为可渲染的格式，以便在屏幕上显示粒子效果。
#### 使用方式
目前文档未提供具体使用方式，推测在需要对粒子系统的渲染进行定制或者优化时会使用到。例如，在实现一些特殊的粒子渲染效果时，可以通过修改该装配器的逻辑来实现。
