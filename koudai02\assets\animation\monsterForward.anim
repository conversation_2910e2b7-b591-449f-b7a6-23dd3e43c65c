[{"__type__": "cc.AnimationClip", "_name": "monsterForward", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 0.5, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 11}, {"__id__": 16}], "_exoticAnimation": null, "_events": [{"frame": 0.5, "func": "", "params": []}, {"frame": 0.5, "func": "attackFn", "params": []}], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 21}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channels": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": ["position"]}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 4}}, {"__type__": "cc.RealCurve", "_times": [0, 0.5], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 630, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 270, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 6}}, {"__type__": "cc.RealCurve", "_times": [0, 0.5], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -215, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": -215, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 8}}, {"__type__": "cc.RealCurve", "_times": [0, 0.5], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 1, "leftTangent": 0, "leftTangentWeight": 1, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 10}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 12}, "proxy": null}, "_channel": {"__id__": 14}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 13}, "active"]}, {"__type__": "cc.animation.HierarchyPath", "path": "Sprite"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.ObjectCurve", "_times": [0, 0.5], "_values": [false, true]}, {"__type__": "cc.animation.ObjectTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 17}, "proxy": null}, "_channel": {"__id__": 19}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 18}, "active"]}, {"__type__": "cc.animation.HierarchyPath", "path": "monsterNotice"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 20}}, {"__type__": "cc.ObjectCurve", "_times": [0], "_values": [false]}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]