# Cocos Creator 3.8 缓动系统模块相关接口规则文档

## 一、命名空间
### tweenProgress
暂未获取到详细说明，推测该命名空间可能包含与缓动进度相关的工具函数或常量，用于处理缓动过程中的进度计算、状态判断等操作。

## 二、类
### 1. Tween
#### 说明
`Tween` 提供了一个简单灵活的方法来缓动目标，从 Creator 移植而来。它可以在一定时间内对对象的属性进行变化，实现各种动画效果，如位置移动、缩放、旋转等。
#### 使用方式
```typescript
import { tween, Node } from 'cc';
// 创建一个节点
const node = new Node();
// 创建一个 Tween 实例并对节点的位置属性进行缓动
const tweenAction = tween(node.position)
  .to(2, { x: 100, y: 100 }, { easing: 'linear' })
  .start();
```
在上述代码中，`tween(node.position)` 创建了一个针对节点位置属性的 Tween 实例，`.to(2, { x: 100, y: 100 }, { easing: 'linear' })` 表示在 2 秒内将节点的位置缓动到 `(100, 100)`，并使用线性缓动函数，最后 `.start()` 启动缓动动画。

### 2. TweenAction
暂未获取到详细说明，推测该类可能是 `Tween` 类中具体的动作类，用于封装每个缓动动作的相关信息，如目标属性、持续时间、缓动函数等。

### 3. TweenSystem
#### 说明
缓动系统，负责管理所有的缓动动画，包括动画的更新、调度等操作。它是整个缓动系统的核心，确保所有的缓动动画能够按照预期的时间和效果进行播放。
#### 使用方式
在实际开发中，通常不需要直接操作 `TweenSystem`，因为 `Tween` 类会自动与 `TweenSystem` 进行交互。但在一些特殊情况下，可能需要对缓动系统进行全局的控制，例如暂停或恢复所有的缓动动画。
```typescript
import { TweenSystem } from 'cc';
// 暂停所有缓动动画
TweenSystem.instance.pause();
// 恢复所有缓动动画
TweenSystem.instance.resume();
```

## 三、接口
### 1. ITweenCustomProperty
暂未获取到详细说明，推测该接口用于定义自定义属性的缓动规则。当需要对对象的非标准属性进行缓动时，可以实现该接口来定义属性的获取和设置方法，以及属性的插值计算方式。
### 2. ITweenCustomPropertyStartParameter
暂未获取到详细说明，推测该接口用于传递自定义属性缓动的起始参数，例如自定义属性的初始值、目标值等。
### 3. ITweenOption
#### 说明
缓动的可选属性的接口定义，包含了一些可以自定义缓动效果的参数，如缓动函数、延迟时间、重复次数等。
#### 使用方式
```typescript
import { tween, Node } from 'cc';
const node = new Node();
const options: ITweenOption = { 
  easing: 'quadOut', 
  delay: 1, 
  repeat: 2 
}; 
tween(node.position)
  .to(2, { x: 100, y: 100 }, options)
  .start();
```
在上述代码中，`options` 对象包含了缓动函数 `quadOut`、延迟时间 1 秒和重复次数 2 次的设置，将其传递给 `.to()` 方法，实现了自定义的缓动效果。

## 四、函数
### 1. bezier
#### 说明
用于计算贝塞尔曲线的值，贝塞尔曲线可以实现复杂的缓动效果，如非线性的运动轨迹。
#### 使用方式
```typescript
import { bezier } from 'cc';
// 计算贝塞尔曲线在 t = 0.5 时的值
const value = bezier(0, 1, 2, 3, 0.5);
console.log('贝塞尔曲线值:', value);
```
### 2. catmullRom
#### 说明
用于计算 Catmull - Rom 样条曲线的值，Catmull - Rom 样条曲线可以实现平滑的曲线插值，常用于实现平滑的动画过渡。
#### 使用方式
```typescript
import { catmullRom } from 'cc';
// 计算 Catmull - Rom 样条曲线在 t = 0.5 时的值
const value = catmullRom(0, 1, 2, 3, 0.5);
console.log('Catmull - Rom 样条曲线值:', value);
```
### 3. tween
#### 说明
是一个工具函数，帮助实例化 `Tween` 实例。通过该函数可以更方便地创建 `Tween` 对象并进行缓动操作。
#### 使用方式
```typescript
import { tween, Node } from 'cc';
const node = new Node();
tween(node)
  .to(2, { position: { x: 100, y: 100 } })
  .start();
```
### 4. tweenUtil
#### 说明
也是一个工具函数，帮助实例化 `Tween` 实例，与 `tween` 函数类似，可能在功能上有一些细微的差别或提供了额外的便利方法。
#### 使用方式
```typescript
import { tweenUtil, Node } from 'cc';
const node = new Node();
tweenUtil(node)
  .to(2, { position: { x: 100, y: 100 } })
  .start();
```
