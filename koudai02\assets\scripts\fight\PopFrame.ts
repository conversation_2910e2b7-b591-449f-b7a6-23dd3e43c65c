import { _decorator, Button, Component, Label, Node, Prefab, instantiate, find, director } from 'cc';
const { ccclass, property } = _decorator;

import { fightMian } from './fightMian';

//战斗界面提示框脚本
@ccclass('PopFrame')
export class PopFrame extends Component {
    
    @property(Node)
    private fightMianNod: Node;
    private combatResult: Label;//战斗结果
    private exp: Label; //获得经验
    private gold: Label; //获得金币
    private Pet: Label;  //获得宠物
    private Item: Label; //获得物品
    private continue: Button;//继续按钮
    private back: Button;//返回按钮
    private fightMian: fightMian; //攻击主脚本，用于回调播放完成通知

    start() {
        this.node.active = false;//默认隐藏

        const combatResultNod: Node = find("/notice/combatResult", this.node);//读取宠物节点
        const expNod: Node = find("/notice/exp", this.node);//获得经验
        const goldNod: Node = find("/notice/gold", this.node);//获得金币
        const PetNod: Node = find("/notice/Pet", this.node);//获得宠物
        const ItemNod: Node = find("/notice/Item", this.node);//获得物品
        const continueNod: Node = find("/continue", this.node);//继续按钮
        const backNod: Node = find("/back", this.node);//返回按钮

        if (!combatResultNod) {
            console.error("combatResultNod战斗结果节点不存在");
            return;
        }

        if (!expNod) {
            console.error("expNod获得经验节点不存在");
            return;
        }

        if (!goldNod) {
            console.error("goldNod获得金币节点不存在");
            return;
        }

        if (!PetNod) {
            console.error("PetNod获得宠物节点不存在");
            return;
        }

        if (!ItemNod) {
            console.error("ItemNod获得物品节点不存在");
            return;
        }

        if (!continueNod) {
            console.error("continueNod继续按钮节点不存在");
            return;
        }

        if (!backNod) {
            console.error("backNod返回按钮节点不存在");
            return;
        }

        this.fightMian = this.fightMianNod.getComponent(fightMian);
        this.combatResult = combatResultNod.getComponent(Label);
        this.exp = expNod.getComponent(Label);
        this.gold = goldNod.getComponent(Label);
        this.Pet = PetNod.getComponent(Label);
        this.Item = ItemNod.getComponent(Label);
        this.continue = continueNod.getComponent(Button);
        this.back = backNod.getComponent(Button);


        this.continue.node.on(Node.EventType.MOUSE_DOWN,this.CkPopFrameContinue, this);//绑定继续点击事件
        this.back.node.on(Node.EventType.MOUSE_DOWN,this.PopFrameback, this);//绑定返回点击事件
    }

    update(deltaTime: number) {
    
    }
    
    //点击继续战斗
    private CkPopFrameContinue(){
        this.node.active = false;//隐藏
        this.fightMian.PopFrameContinue();//调用继续战斗
    
    }
    
    //点击返回
    private PopFrameback(){
        this.node.active = false;//隐藏
        this.fightMian.PopFrameback();//调用返回
    }

    //更新信息
    public UpdatePopFrameInfo(combatResult: string, exp: string, gold: string, Pet: string, Item: string) {
        console.log("🎉 战斗结果已显示函数");
        this.combatResult.string = combatResult;
        this.exp.string = exp;
        this.gold.string = gold;
        this.Pet.string = Pet;
        this.Item.string = Item;
        this.node.active = true;//显示
        console.log("🎉 战斗结果已显示函数1");
    }

}


