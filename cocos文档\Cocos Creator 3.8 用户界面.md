# Cocos Creator 3.8 用户界面模块相关接口规则文档

## 一、命名空间
### Widget
暂未获取到详细说明，推测该命名空间包含了与 `Widget` 组件相关的工具函数、常量或类型定义等，用于辅助 `Widget` 组件的使用和开发。

## 二、类
### 1. BlockInputEvents
#### 说明
该组件将拦截所属节点尺寸内的所有输入事件（鼠标和触摸），防止输入穿透到下层节点，一般用于上层 UI 的背景。该组件没有任何 API 接口，直接添加到场景即可生效。
#### 使用方式
```typescript
import { Node, BlockInputEvents } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 BlockInputEvents 组件
node.addComponent(BlockInputEvents);
```
### 2. Button
#### 说明
按钮组件，可以被按下或点击。可以通过修改 `Transition` 来设置按钮状态过渡的方式，还可以绑定多种事件。
#### 使用方式
```typescript
import { Node, Button, EventHandler } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 Button 组件
const button = node.addComponent(Button);
// 设置按钮状态过渡方式为颜色过渡
button.transition = Button.Transition.COLOR;
// 绑定点击事件
const eventHandler = new EventHandler();
eventHandler.target = node;
eventHandler.component = 'MyScript';
eventHandler.handler = 'onButtonClick';
button.clickEvents.push(eventHandler);
```
在 `MyScript` 脚本中实现 `onButtonClick` 方法：
```typescript
import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MyScript')
export class MyScript extends Component {
    onButtonClick() {
        console.log('按钮被点击了');
    }
}
```
### 3. EditBox
#### 说明
用于获取用户的输入文本。
#### 使用方式
```typescript
import { Node, EditBox } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 EditBox 组件
const editBox = node.addComponent(EditBox);
// 获取用户输入的文本
const inputText = editBox.string;
```
### 4. Layout
#### 说明
`Layout` 组件相当于一个容器，能自动对它的所有子节点进行统一排版。需要注意的是，不会考虑子节点的缩放和旋转，对 `Layout` 设置后结果需要到下一帧才会更新，除非手动调用 `updateLayout`。
#### 使用方式
```typescript
import { Node, Layout } from 'cc';
// 创建一个节点作为 Layout 容器
const layoutNode = new Node();
// 添加 Layout 组件
const layout = layoutNode.addComponent(Layout);
// 设置布局类型为水平布局
layout.type = Layout.Type.HORIZONTAL;
// 添加子节点
const childNode1 = new Node();
const childNode2 = new Node();
layoutNode.addChild(childNode1);
layoutNode.addChild(childNode2);
// 手动更新布局
layout.updateLayout();
```
### 5. PageView
#### 说明
页面视图组件，用于实现多页面的切换和展示。
#### 使用方式
```typescript
import { Node, PageView } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 PageView 组件
const pageView = node.addComponent(PageView);
// 添加页面节点
const page1 = new Node();
const page2 = new Node();
pageView.addPage(page1);
pageView.addPage(page2);
// 切换到指定页面
pageView.scrollToPage(1);
```
### 6. PageViewIndicator
#### 说明
页面视图每页标记组件，用于显示 `PageView` 的当前页面标记。
#### 使用方式
```typescript
import { Node, PageView, PageViewIndicator } from 'cc';
// 创建 PageView 节点
const pageViewNode = new Node();
const pageView = pageViewNode.addComponent(PageView);
// 创建 PageViewIndicator 节点
const indicatorNode = new Node();
const indicator = indicatorNode.addComponent(PageViewIndicator);
// 将 PageView 关联到 PageViewIndicator
indicator.pageView = pageView;
```
### 7. ProgressBar
#### 说明
进度条组件，可用于显示加载资源时的进度。
#### 使用方式
```typescript
import { Node, ProgressBar } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 ProgressBar 组件
const progressBar = node.addComponent(ProgressBar);
// 设置进度值
progressBar.progress = 0.5;
```
### 8. ResolutionPolicy
#### 说明
`ResolutionPolicy` 类是适配策略的根策略类，它的主要任务是保持与 Cocos2d-x 的兼容性。
#### 使用方式
在设置游戏的分辨率适配策略时可能会用到，例如：
```typescript
import { director, ResolutionPolicy } from 'cc';
// 设置分辨率适配策略
const policy = new ResolutionPolicy();
director.setResolutionPolicy(policy);
```
### 9. SafeArea
#### 说明
该组件会将所在节点的布局适配到 iPhone X 等异形屏手机的安全区域内，通常用于 UI 交互区域的顶层节点。该组件内部通过 `sys.getSafeAreaRect()` 获取到当前 iOS 或 Android 设备的安全区域，并通过 `Widget` 组件实现适配。
#### 使用方式
```typescript
import { Node, SafeArea } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 SafeArea 组件
node.addComponent(SafeArea);
```
### 10. ScrollBar
#### 说明
滚动条组件，通常与 `ScrollView` 配合使用，用于控制滚动视图的滚动位置。
#### 使用方式
```typescript
import { Node, ScrollBar, ScrollView } from 'cc';
// 创建 ScrollView 节点
const scrollViewNode = new Node();
const scrollView = scrollViewNode.addComponent(ScrollView);
// 创建 ScrollBar 节点
const scrollBarNode = new Node();
const scrollBar = scrollBarNode.addComponent(ScrollBar);
// 将 ScrollBar 关联到 ScrollView
scrollBar.target = scrollView;
```
### 11. ScrollView
#### 说明
滚动视图组件，用于显示超出其显示区域的内容，并支持滚动操作。
#### 使用方式
```typescript
import { Node, ScrollView } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 ScrollView 组件
const scrollView = node.addComponent(ScrollView);
// 添加内容节点
const contentNode = new Node();
scrollView.content = contentNode;
```
### 12. Slider
#### 说明
滑动器组件，用于让用户通过滑动操作来选择一个值。
#### 使用方式
```typescript
import { Node, Slider, EventHandler } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 Slider 组件
const slider = node.addComponent(Slider);
// 绑定滑动事件
const eventHandler = new EventHandler();
eventHandler.target = node;
eventHandler.component = 'MyScript';
eventHandler.handler = 'onSliderChanged';
slider.slideEvents.push(eventHandler);
```
在 `MyScript` 脚本中实现 `onSliderChanged` 方法：
```typescript
import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MyScript')
export class MyScript extends Component {
    onSliderChanged(slider: Slider) {
        console.log('滑动器的值:', slider.progress);
    }
}
```
### 13. SubContextView
#### 说明
`SubContextView` 可以用来控制微信小游戏平台开放数据域在主域中的视窗的位置。该组件的节点尺寸决定了开放数据域内容在主域中的尺寸，整个开放数据域会被缩放到节点的包围盒范围内。当子域节点的包围盒发生改变时，开发者需要使用 `updateSubContextViewport` 来手动更新子域视窗。
#### 使用方式
```typescript
import { Node, SubContextView } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 SubContextView 组件
const subContextView = node.addComponent(SubContextView);
// 当子域节点包围盒改变时，手动更新子域视窗
subContextView.updateSubContextViewport();
```
### 14. Toggle
#### 说明
`Toggle` 是一个 CheckBox，当它和 `ToggleGroup` 一起使用的时候，可以变成 RadioButton。
#### 使用方式
```typescript
import { Node, Toggle } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 Toggle 组件
const toggle = node.addComponent(Toggle);
// 设置 Toggle 的选中状态
    toggle.isChecked = true;
```
### 15. ToggleContainer
#### 说明
`ToggleGroup` 不是一个可见的 UI 组件，它可以用来修改一组 `Toggle` 组件的行为。当一组 `Toggle` 属于同一个 `ToggleGroup` 的时候，任何时候只能有一个 `Toggle` 处于选中状态。
#### 使用方式
```typescript
import { Node, Toggle, ToggleContainer } from 'cc';
// 创建 ToggleGroup 节点
const toggleGroupNode = new Node();
const toggleGroup = toggleGroupNode.addComponent(ToggleContainer);
// 创建两个 Toggle 节点
const toggleNode1 = new Node();
const toggle1 = toggleNode1.addComponent(Toggle);
const toggleNode2 = new Node();
const toggle2 = toggleNode2.addComponent(Toggle);
// 将 Toggle 添加到 ToggleGroup 中
toggleGroup.addToggle(toggle1);
toggleGroup.addToggle(toggle2);
```
### 16. UICoordinateTracker
#### 说明
3D 节点坐标转换到 UI 节点坐标组件，主要提供映射后的转换世界坐标以及模拟透视相机远近比。
#### 使用方式
```typescript
import { Node, UICoordinateTracker } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 UICoordinateTracker 组件
const tracker = node.addComponent(UICoordinateTracker);
// 设置 3D 节点
const target3DNode = new Node();
tracker.target = target3DNode;
// 获取转换后的 UI 坐标
const uiPosition = tracker.uiPosition;
```
### 17. UIReorderComponent
暂未获取到详细说明，推测该组件可能用于对 UI 节点进行重新排序，以实现特定的布局效果。
### 18. View
暂未获取到详细说明，推测该类可能与视图相关，用于管理和控制 UI 视图的显示和交互。
### 19. ViewGroup
暂未获取到详细说明，推测该类可能用于管理一组视图，实现视图的分组和布局。
### 20. Widget
#### 说明
`Widget` 组件用于设置和适配其相对于父节点的边距，通常被用于 UI 界面，也可以用于其他地方。`Widget` 会自动调整当前节点的坐标和宽高，不过目前调整后的结果要到下一帧才能在脚本里获取到，除非先手动调用 `updateAlignment`。
#### 使用方式
```typescript
import { Node, Widget } from 'cc';
// 创建一个节点
const node = new Node();
// 添加 Widget 组件
const widget = node.addComponent(Widget);
// 设置节点相对于父节点的左边距
widget.left = 10;
// 手动更新对齐
widget.updateAlignment();
```

## 三、变量
### 1. view
`view` 是全局的视图单例对象，可能用于获取和设置游戏视图的相关属性，如分辨率、可见区域等。
### 2. widgetManager
`widget` 管理器，用于对齐操作，可能提供了一些方法来统一管理和更新 `Widget` 组件的对齐状态。

综上所述，Cocos Creator 3.8 的用户界面模块提供了丰富的组件和接口，通过合理使用这些组件和接口，可以方便地创建出各种复杂的 UI 界面。