<!DOCTYPE html
	PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>map</title>
	<style>
		body {
			padding: 0;
			margin: 0
		}

		#main {
			background-image: url(Content/Flash/map_city_c.png);
			background-size: 788px 311px;
			background-repeat: no-repeat;
			height: 313px;
			width: 790px;
		}

		#button {
			width: 123px;
			height: 113px;

			position: absolute;
			cursor: pointer;
		}

		#button1 {
			width: 125px;
			height: 155px;
			position: absolute;

			top: 56px;
			left: 230px;
			cursor: pointer;

		}

		#button1:hover {
			width: 125px;
			height: 155px;
			position: absolute;
			/* border: 1px solid #00C; */
			top: 56px;
			left: 235px;
			cursor: pointer;
			background: url(Content/Flash/map_city_c_a.png) no-repeat -235px -56px;
			background-size: 788px 311px;

		}

		#button2 {
			width: 129px;
			height: 86px;
			position: absolute;
			top: 0;
			left: 356px;
			cursor: pointer;
		}

		#button2:hover {
			width: 129px;
			height: 86px;
			position: absolute;
			/* border: 1px solid #00C; */
			top: 0;
			left: 353px;
			cursor: pointer;
			background: url(Content/Flash/map_city_c_a.png) no-repeat -353px -0px;
			background-size: 788px 311px;

		}

		#button3 {
			width: 186px;
			height: 132px;
			position: absolute;

			top: 0;
			left: 479px;
			cursor: pointer;

		}

		#button3:hover {
			width: 186px;
			height: 132px;
			position: absolute;
			/* border: 1px solid #00C; */
			top: 0;
			left: 479px;
			cursor: pointer;
			background: url(Content/Flash/map_city_c_a.png) no-repeat -479px -0px;
			background-size: 788px 311px;

		}

		#button4 {
			width: 100px;
			height: 100px;
			position: absolute;
			top: 148px;
			left: 606px;
			cursor: pointer;
		}

		#button4:hover {
			width: 100px;
			height: 100px;
			position: absolute;
			/* border: 1px solid #00C; */
			top: 148Px;
			left: 606px;
			cursor: pointer;
			background: url(Content/Flash/map_city_c_a.png) no-repeat -606px -148px;
			background-size: 788px 311px;

		}

		#button:hover {
			background-image: url(Content/Flash/map_city_c_a.png);
			background-size: 788px 311px;
		}

		#button5 {
			width: 100px;
			height: 100px;
			position: absolute;
			top: 11px;
			left: 680px;
			cursor: pointer;

		}

		#button5:hover {
			background: url(Content/Flash/map_city_c_a.png) no-repeat -680px -11px;
			background-size: 788px 311px;
		}

		#button6 {
			width: 100px;
			height: 100px;
			position: absolute;
			top: 66px;
			left: 152px;
			cursor: pointer;

		}

		#button6:hover {
			background: url(Content/Flash/map_city_c_a.png) no-repeat -152px -66px;
			background-size: 788px 311px;
		}

		#button7 {
			width: 100px;
			height: 100px;
			position: absolute;
			top: 135px;
			left: 440px;
			/* background-color: rgb(184, 184, 184); */
			cursor: pointer;

		}

		#button7:hover {
			background: url(Content/Flash/map_city_c_a.png) no-repeat -440px -135px;
			background-size: 788px 311px;
		}
		#button8 {
			width: 165px;
    		height: 155px;
    		position: absolute;
    		top: 140px;
    		left: 20px;
    		cursor: pointer;

		}

		#button8:hover {
			background: url(Content/Flash/map_city_c_a.png) no-repeat -20px -140px;
			background-size: 788px 311px;
		}

		div {
			padding: 0;
			margin: 0;
		}
	</style>
</head>

<body>
	<div id="main">
		<div id="button" onclick="window.parent.openNpc(1)"></div>
		<div id="button1" onclick="window.parent.openNpc(2)"></div>
		<div id="button2" onclick="window.parent.openNpc(3)"></div>
		<div id="button3" onclick="window.parent.openNpc(4)"></div>
		<div id="button4" onclick="window.parent.openNpc(5)"></div>
		<div id="button5" onclick="window.parent.openNpc(6)"></div>
		<div id="button6" onclick="window.parent.openNpc(8)"></div>
		<div id="button7" onclick=""></div>
		<div id="button8" onclick="window.parent.openNpc(9)"></div>
	</div>
</body>

</html>