using Microsoft.AspNetCore.Mvc;
using System.Net.WebSockets;
using WebApplication_HM.Services.SocketInfo;

namespace WebApplication_HM.Controllers;

// WebSocket 
[ApiController]
[Route("ws")]
public class WebSocketController : ControllerBase
{
    //注入ChatMessageHandler
    private readonly ChatMessageHandler _webSocketHandler;

    public WebSocketController(ChatMessageHandler webSocketHandler)
    {
        _webSocketHandler = webSocketHandler;
    }

    //请求连接 ws://localhost:xxxx/ws/connect
    //客户端消息 ---> WebSocketController ---> ChatMessageHandler ---> WebSocketConnectionManager

    [HttpGet("connect")]
    public async Task Get()
    {
        //判断是否为WebSocket请求
        if (HttpContext.WebSockets.IsWebSocketRequest)
        {
            //接受WebSocket连接
            using var webSocket = await HttpContext.WebSockets.AcceptWebSocketAsync();

            //调用ChatMessageHandler的OnConnected方法
            await _webSocketHandler.OnConnected(webSocket);

            //调用ChatMessageHandler的ReceiveAsync方法，接收客户端消息
            await _webSocketHandler.ReceiveAsync(webSocket, async (result, message) =>
            {
                //判断消息类型是否为文本
                if (result.MessageType == WebSocketMessageType.Text)
                {
                    //获取WebSocket连接的ID
                    var socketId = _webSocketHandler.GetConnectionId(webSocket);

                    //调用ChatMessageHandler的HandleIncomingMessage方法，处理客户端消息
                    await _webSocketHandler.HandleIncomingMessage(socketId, message);
                }
            });

            //获取WebSocket连接的ID
            var socketId = _webSocketHandler.GetConnectionId(webSocket);

            //调用ChatMessageHandler的OnDisconnected方法，处理WebSocket断开连接
            await _webSocketHandler.OnDisconnected(socketId);
        }
        else
        {
            //如果不是WebSocket请求，返回400错误
            HttpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
        }
    }
} 