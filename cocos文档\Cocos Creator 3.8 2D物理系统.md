# Cocos Creator 3.8 2D 物理系统接口规则文档

## 一、类

### 1. BoxCollider2D
#### 说明
`BoxCollider2D` 是 2D 物理系统中的盒子碰撞体类，用于模拟物体的方形碰撞区域。它可以为节点添加方形的碰撞检测功能，使物体在物理模拟中能够与其他碰撞体发生交互。
#### 使用方式
```typescript
import { Node, BoxCollider2D } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 BoxCollider2D 组件
const boxCollider = node.addComponent(BoxCollider2D);
// 设置碰撞体的大小
boxCollider.size = { width: 100, height: 50 };
```

### 2. CircleCollider2D
#### 说明
`CircleCollider2D` 是 2D 物理系统中的圆形碰撞体类，用于模拟物体的圆形碰撞区域。它可以为节点添加圆形的碰撞检测功能，适用于需要圆形碰撞检测的物体。
#### 使用方式
```typescript
import { Node, CircleCollider2D } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 CircleCollider2D 组件
const circleCollider = node.addComponent(CircleCollider2D);
// 设置碰撞体的半径
circleCollider.radius = 20;
```

### 3. Collider2D
#### 说明
`Collider2D` 是 2D 碰撞体的基类，`BoxCollider2D`、`CircleCollider2D` 等具体的碰撞体类都继承自它。它定义了碰撞体的基本属性和方法，是创建自定义碰撞体的基础。
#### 使用方式
由于 `Collider2D` 是基类，一般不直接使用，而是使用它的子类。但可以通过它来统一管理碰撞体的一些通用操作。
```typescript
import { Node, Collider2D } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 Collider2D 组件
const collider = node.addComponent(Collider2D);
// 设置碰撞体的分组
collider.group = 1;
```

### 4. DistanceJoint2D
#### 说明
`DistanceJoint2D` 是 2D 物理系统中的距离关节类，用于约束两个刚体之间的距离。它可以使两个刚体保持固定的距离，或者在一定范围内改变距离。
#### 使用方式
```typescript
import { Node, RigidBody2D, DistanceJoint2D } from 'cc';

// 创建两个节点
const node1 = new Node();
const node2 = new Node();

// 为节点添加刚体组件
const rigidBody1 = node1.addComponent(RigidBody2D);
const rigidBody2 = node2.addComponent(RigidBody2D);

// 添加 DistanceJoint2D 组件
const distanceJoint = node1.addComponent(DistanceJoint2D);
// 设置连接的另一个刚体
distanceJoint.connectedBody = rigidBody2;
// 设置关节的长度
distanceJoint.distance = 100;
```

### 5. FixedJoint2D
#### 说明
`FixedJoint2D` 是 2D 物理系统中的固定关节类，用于将两个刚体固定在一起，使它们之间的相对位置和角度保持不变。
#### 使用方式
```typescript
import { Node, RigidBody2D, FixedJoint2D } from 'cc';

// 创建两个节点
const node1 = new Node();
const node2 = new Node();

// 为节点添加刚体组件
const rigidBody1 = node1.addComponent(RigidBody2D);
const rigidBody2 = node2.addComponent(RigidBody2D);

// 添加 FixedJoint2D 组件
const fixedJoint = node1.addComponent(FixedJoint2D);
// 设置连接的另一个刚体
fixedJoint.connectedBody = rigidBody2;
```

### 6. HingeJoint2D
#### 说明
`HingeJoint2D` 是 2D 物理系统中的铰链关节类，用于模拟两个刚体之间的旋转连接，类似于门的合页。它允许两个刚体绕一个固定点旋转。
#### 使用方式
```typescript
import { Node, RigidBody2D, HingeJoint2D } from 'cc';

// 创建两个节点
const node1 = new Node();
const node2 = new Node();

// 为节点添加刚体组件
const rigidBody1 = node1.addComponent(RigidBody2D);
const rigidBody2 = node2.addComponent(RigidBody2D);

// 添加 HingeJoint2D 组件
const hingeJoint = node1.addComponent(HingeJoint2D);
// 设置连接的另一个刚体
hingeJoint.connectedBody = rigidBody2;
// 设置铰链的锚点
hingeJoint.anchor = { x: 0, y: 0 };
```

### 7. Intersection2D
#### 说明
`Intersection2D` 是一个辅助类，用于测试形状与形状是否相交。可以通过它来判断两个碰撞体是否发生了重叠，从而实现更精确的碰撞检测。
#### 使用方式
```typescript
import { Intersection2D, BoxCollider2D, CircleCollider2D } from 'cc';

// 创建两个碰撞体
const boxCollider = new BoxCollider2D();
const circleCollider = new CircleCollider2D();

// 判断两个碰撞体是否相交
const isIntersecting = Intersection2D.boxCircle(boxCollider.worldAABB, circleCollider.worldPosition, circleCollider.radius);
console.log('是否相交:', isIntersecting);
```

### 8. Joint2D
#### 说明
`Joint2D` 是 2D 关节的基类，`DistanceJoint2D`、`FixedJoint2D` 等具体的关节类都继承自它。它定义了关节的基本属性和方法，是创建自定义关节的基础。
#### 使用方式
由于 `Joint2D` 是基类，一般不直接使用，而是使用它的子类。但可以通过它来统一管理关节的一些通用操作。
```typescript
import { Node, Joint2D, RigidBody2D } from 'cc';

// 创建一个节点
const node = new Node();
// 为节点添加刚体组件
const rigidBody = node.addComponent(RigidBody2D);

// 添加 Joint2D 组件
const joint = node.addComponent(Joint2D);
// 设置连接的刚体
joint.connectedBody = rigidBody;
```

### 9. MouseJoint2D
#### 说明
`MouseJoint2D` 是 2D 物理系统中的鼠标关节类，用于实现鼠标与刚体之间的交互。可以通过鼠标拖动刚体，使刚体跟随鼠标移动。
#### 使用方式
```typescript
import { Node, RigidBody2D, MouseJoint2D } from 'cc';

// 创建一个节点
const node = new Node();
// 为节点添加刚体组件
const rigidBody = node.addComponent(RigidBody2D);

// 添加 MouseJoint2D 组件
const mouseJoint = node.addComponent(MouseJoint2D);
// 设置连接的刚体
mouseJoint.connectedBody = rigidBody;
```

### 10. PhysicsSystem2D
#### 说明
`PhysicsSystem2D` 是 2D 物理系统的核心类，负责管理整个 2D 物理世界的模拟和更新。它可以控制物理系统的开启和关闭、设置物理世界的参数等。
#### 使用方式
```typescript
import { PhysicsSystem2D } from 'cc';

// 开启物理系统
PhysicsSystem2D.instance.enable = true;
// 设置物理世界的重力
PhysicsSystem2D.instance.gravity = { x: 0, y: -9.8 };
```

### 11. PolygonCollider2D
#### 说明
`PolygonCollider2D` 是 2D 物理系统中的多边形碰撞体类，用于模拟物体的多边形碰撞区域。它可以为节点添加任意形状的多边形碰撞检测功能。
#### 使用方式
```typescript
import { Node, PolygonCollider2D } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 PolygonCollider2D 组件
const polygonCollider = node.addComponent(PolygonCollider2D);
// 设置多边形的顶点
polygonCollider.points = [
    { x: 0, y: 0 },
    { x: 50, y: 0 },
    { x: 50, y: 50 },
    { x: 0, y: 50 }
];
```

### 12. RelativeJoint2D
#### 说明
`RelativeJoint2D` 是 2D 物理系统中的相对关节类，用于约束两个刚体之间的相对位置和角度。它可以使两个刚体之间保持一定的相对关系。
#### 使用方式
```typescript
import { Node, RigidBody2D, RelativeJoint2D } from 'cc';

// 创建两个节点
const node1 = new Node();
const node2 = new Node();

// 为节点添加刚体组件
const rigidBody1 = node1.addComponent(RigidBody2D);
const rigidBody2 = node2.addComponent(RigidBody2D);

// 添加 RelativeJoint2D 组件
const relativeJoint = node1.addComponent(RelativeJoint2D);
// 设置连接的另一个刚体
relativeJoint.connectedBody = rigidBody2;
```

### 13. RigidBody2D
#### 说明
`RigidBody2D` 是 2D 物理系统中的刚体类，用于模拟物体的物理属性，如质量、速度、加速度等。它可以使节点具有物理行为，参与物理模拟。
#### 使用方式
```typescript
import { Node, RigidBody2D } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 RigidBody2D 组件
const rigidBody = node.addComponent(RigidBody2D);
// 设置刚体的类型
rigidBody.type = RigidBody2D.Type.Dynamic;
```

### 14. SliderJoint2D
#### 说明
`SliderJoint2D` 是 2D 物理系统中的滑块关节类，用于约束两个刚体之间的线性运动，使它们只能沿着指定的轴进行滑动。
#### 使用方式
```typescript
import { Node, RigidBody2D, SliderJoint2D } from 'cc';

// 创建两个节点
const node1 = new Node();
const node2 = new Node();

// 为节点添加刚体组件
const rigidBody1 = node1.addComponent(RigidBody2D);
const rigidBody2 = node2.addComponent(RigidBody2D);

// 添加 SliderJoint2D 组件
const sliderJoint = node1.addComponent(SliderJoint2D);
// 设置连接的另一个刚体
sliderJoint.connectedBody = rigidBody2;
// 设置滑动的轴
sliderJoint.axis = { x: 1, y: 0 };
```

### 15. SpringJoint2D
#### 说明
`SpringJoint2D` 是 2D 物理系统中的弹簧关节类，用于模拟两个刚体之间的弹簧连接。它可以使两个刚体之间产生弹性的相互作用。
#### 使用方式
```typescript
import { Node, RigidBody2D, SpringJoint2D } from 'cc';

// 创建两个节点
const node1 = new Node();
const node2 = new Node();

// 为节点添加刚体组件
const rigidBody1 = node1.addComponent(RigidBody2D);
const rigidBody2 = node2.addComponent(RigidBody2D);

// 添加 SpringJoint2D 组件
const springJoint = node1.addComponent(SpringJoint2D);
// 设置连接的另一个刚体
springJoint.connectedBody = rigidBody2;
// 设置弹簧的刚度
springJoint.stiffness = 10;
```

### 16. WheelJoint2D
#### 说明
`WheelJoint2D` 是 2D 物理系统中的车轮关节类，用于模拟车轮的物理行为。它可以使刚体像车轮一样旋转，并可以设置车轮的悬挂和阻尼等属性。
#### 使用方式
```typescript
import { Node, RigidBody2D, WheelJoint2D } from 'cc';

// 创建两个节点
const node1 = new Node();
const node2 = new Node();

// 为节点添加刚体组件
const rigidBody1 = node1.addComponent(RigidBody2D);
const rigidBody2 = node2.addComponent(RigidBody2D);

// 添加 WheelJoint2D 组件
const wheelJoint = node1.addComponent(WheelJoint2D);
// 设置连接的另一个刚体
wheelJoint.connectedBody = rigidBody2;
// 设置车轮的轴
wheelJoint.axis = { x: 0, y: 1 };
```

## 二、接口

### 1. IPhysics2DContact
#### 说明
物理接触会在开始和结束碰撞之间生成，并作为参数传入到碰撞回调函数中。注意：传入的物理接触会被系统进行重用，所以不要在使用中缓存里面的任何信息。
#### 使用方式
```typescript
import { IPhysics2DContact, Collider2D } from 'cc';

// 碰撞开始回调函数
function onCollisionBegin(contact: IPhysics2DContact) {
    const colliderA = contact.colliderA as Collider2D;
    const colliderB = contact.colliderB as Collider2D;
    console.log('碰撞开始:', colliderA.node.name, colliderB.node.name);
}

// 为碰撞体添加碰撞开始回调
const collider = new Collider2D();
collider.on('onCollisionBegin', onCollisionBegin);
```

### 2. IPhysics2DImpulse
#### 说明
用于返回给回调的接触冲量。可以通过它获取碰撞时的冲量信息，用于实现一些特殊的效果，如碰撞时的震动等。
#### 使用方式
```typescript
import { IPhysics2DContact, IPhysics2DImpulse } from 'cc';

// 碰撞开始回调函数
function onCollisionBegin(contact: IPhysics2DContact) {
    const impulse: IPhysics2DImpulse = contact.getImpulse();
    console.log('碰撞冲量:', impulse.normalImpulses, impulse.tangentImpulses);
}

// 为碰撞体添加碰撞开始回调
const collider = new Collider2D();
collider.on('onCollisionBegin', onCollisionBegin);
```

### 3. IPhysics2DManifold
#### 说明
流形，用于描述两个碰撞体之间的接触信息，如接触点、法线等。
#### 使用方式
```typescript
import { IPhysics2DContact, IPhysics2DManifold } from 'cc';

// 碰撞开始回调函数
function onCollisionBegin(contact: IPhysics2DContact) {
    const manifold: IPhysics2DManifold = contact.getManifold();
    console.log('流形信息:', manifold);
}

// 为碰撞体添加碰撞开始回调
const collider = new Collider2D();
collider.on('onCollisionBegin', onCollisionBegin);
```

### 4. IPhysics2DManifoldPoint
#### 说明
`ManifoldPoint` 是接触信息中的接触点信息。它拥有关于几何和接触点的详细信息。注意：信息中的冲量用于系统内部缓存，提供的接触力可能不是很准确，特别是高速移动中的碰撞信息。
#### 使用方式
```typescript
import { IPhysics2DContact, IPhysics2DManifoldPoint } from 'cc';

// 碰撞开始回调函数
function onCollisionBegin(contact: IPhysics2DContact) {
    const manifold = contact.getManifold();
    const points: IPhysics2DManifoldPoint[] = manifold.points;
    for (const point of points) {
        console.log('接触点信息:', point.localPoint, point.normalImpulse);
    }
}

// 为碰撞体添加碰撞开始回调
const collider = new Collider2D();
collider.on('onCollisionBegin', onCollisionBegin);
```

### 5. IPhysics2DWorldManifold
#### 说明
世界坐标系下的流形，与 `IPhysics2DManifold` 类似，但坐标是在世界坐标系下的。
#### 使用方式
```typescript
import { IPhysics2DContact, IPhysics2DWorldManifold } from 'cc';

// 碰撞开始回调函数
function onCollisionBegin(contact: IPhysics2DContact) {
    const worldManifold: IPhysics2DWorldManifold = contact.getWorldManifold();
    console.log('世界坐标系下流形信息:', worldManifold);
}

// 为碰撞体添加碰撞开始回调
const collider = new Collider2D();
collider.on('onCollisionBegin', onCollisionBegin);
```

### 6. RaycastResult2D
#### 说明
射线检测结果类，用于存储射线检测的结果信息，如碰撞点、碰撞体等。
#### 使用方式
```typescript
import { PhysicsSystem2D, RaycastResult2D } from 'cc';

// 进行射线检测
const results: RaycastResult2D[] = [];
PhysicsSystem2D.instance.raycast({ x: 0, y: 0 }, { x: 100, y: 0 }, results);

for (const result of results) {
    console.log('射线检测结果:', result.collider.node.name, result.point);
}
```

## 三、枚举

### 1. ECollider2DType
#### 说明
用于表示 2D 碰撞体的类型，如盒子碰撞体、圆形碰撞体等。
#### 使用方式
```typescript
import { ECollider2DType, BoxCollider2D } from 'cc';

const collider = new BoxCollider2D();
if (collider.type === ECollider2DType.BOX) {
    console.log('这是一个盒子碰撞体');
}
```

### 2. EJoint2DType
#### 说明
用于表示 2D 关节的类型，如距离关节、固定关节等。
#### 使用方式
```typescript
import { EJoint2DType, DistanceJoint2D } from 'cc';

const joint = new DistanceJoint2D();
if (joint.type === EJoint2DType.DISTANCE) {
    console.log('这是一个距离关节');
}
```

### 3. EPhysics2DDrawFlags
#### 说明
用于控制 2D 物理系统的绘制标志，可以显示碰撞体的轮廓、关节等信息，方便调试。
#### 使用方式
```typescript
import { PhysicsSystem2D, EPhysics2DDrawFlags } from 'cc';

// 显示碰撞体的轮廓
PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.SHAPE;
```

### 4. ERaycast2DType
#### 说明
射线检测类型，用于指定射线检测的方式，如最近点检测、所有点检测等。
#### 使用方式
```typescript
import { PhysicsSystem2D, ERaycast2DType } from 'cc';

// 进行最近点射线检测
const results = PhysicsSystem2D.instance.raycast({ x: 0, y: 0 }, { x: 100, y: 0 }, ERaycast2DType.CLOSEST);
```

### 5. ERigidBody2DType
#### 说明
用于表示 2D 刚体的类型，如动态刚体、静态刚体等。
#### 使用方式
```typescript
import { RigidBody2D, ERigidBody2DType } from 'cc';

const rigidBody = new RigidBody2D();
rigidBody.type = ERigidBody2DType.DYNAMIC;
```

### 6. Physics2DManifoldType
#### 说明
流形类型，用于描述流形的不同类型。
#### 使用方式
```typescript
import { IPhysics2DManifold, Physics2DManifoldType } from 'cc';

function handleManifold(manifold: IPhysics2DManifold) {
    if (manifold.type === Physics2DManifoldType.CIRCLES) {
        console.log('流形类型为圆形');
    }
}
```

### 7. PhysicsGroup2D
#### 说明
用于表示 2D 物理系统中的分组，不同的分组可以设置不同的碰撞规则。
#### 使用方式
```typescript
import { Collider2D, PhysicsGroup2D } from 'cc';

const collider = new Collider2D();
collider.group = PhysicsGroup2D.DEFAULT;
```

## 四、变量

### 1. Contact2DType
#### 说明
用于表示 2D 碰撞接触的类型，如碰撞开始、碰撞结束等。
#### 使用方式
```typescript
import { Collider2D, Contact2DType } from 'cc';

const collider = new Collider2D();
collider.on(Contact2DType.BEGIN_CONTACT, (contact) => {
    console.log('碰撞开始');
});
```

### 2. Physics2DUtils
#### 说明
包含一些 2D 物理系统的实用工具函数，如计算碰撞体的面积、判断点是否在碰撞体内等。
#### 使用方式
```typescript
import { Physics2DUtils, BoxCollider2D } from 'cc';

const boxCollider = new BoxCollider2D();
const area = Physics2DUtils.getColliderArea(boxCollider);
console.log('碰撞体的面积:', area);
```

### 3. PhysicsGroup
#### 说明
用于表示物理分组，与 `PhysicsGroup2D` 类似，但可能是更通用的物理分组定义。
#### 使用方式
```typescript
import { Collider2D, PhysicsGroup } from 'cc';

const collider = new Collider2D();
collider.group = PhysicsGroup.DEFAULT;
```

### 4. PHYSICS_2D_PTM_RATIO
#### 说明
2D 物理系统中的像素与米的转换比例，用于将游戏中的像素单位转换为物理系统中的米单位。
#### 使用方式
```typescript
import { PHYSICS_2D_PTM_RATIO } from 'cc';

const pixelValue = 100;
const meterValue = pixelValue / PHYSICS_2D_PTM_RATIO;
console.log('像素值转换为米值:', meterValue);
```

### 5. selector
#### 说明
物理选择器用于注册和切换物理引擎后端，可以选择不同的物理引擎来实现 2D 物理模拟。
#### 使用方式
```typescript
import { selector } from 'cc';

// 选择物理引擎后端
selector.select('box2d');
```
