using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using SqlSugar;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 宠物合成测试控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PetSynthesisTestController : ControllerBase
    {
        private readonly DbContext _dbContext;
        private readonly IPetSynthesisService _synthesisService;
        private readonly ILogger<PetSynthesisTestController> _logger;

        // 测试用户ID
        private const int TEST_USER_ID = 9999;
        private const int TEST_MAIN_PET_ID = 99998;
        private const int TEST_VICE_PET_ID = 99997;

        public PetSynthesisTestController(
            DbContext dbContext,
            IPetSynthesisService synthesisService,
            ILogger<PetSynthesisTestController> logger)
        {
            _dbContext = dbContext;
            _synthesisService = synthesisService;
            _logger = logger;
        }

        /// <summary>
        /// 初始化合成测试数据
        /// </summary>
        [HttpPost("init-test-data")]
        public async Task<IActionResult> InitializeTestData()
        {
            try
            {
                _logger.LogInformation("开始初始化合成测试数据");

                // 1. 清理旧的测试数据
                await CleanupTestDataInternal();

                // 2. 创建测试用户
                var testUser = new user
                {
                    id = TEST_USER_ID,
                    username = "synthesis_test_user",
                    nickname = "合成测试用户",
                    gold = 1000000,
                    vip_level = 5,
                    supreme_vip = true,
                    create_time = DateTime.Now
                };
                await _dbContext.Db.Insertable(testUser).ExecuteCommandAsync();

                // 3. 创建测试宠物配置
                var testPetConfigs = new List<pet_config>
                {
                    new pet_config
                    {
                        pet_no = 999001,
                        name = "测试火系宠物",
                        attribute = "火",
                        is_active = true,
                        create_time = DateTime.Now
                    },
                    new pet_config
                    {
                        pet_no = 999002,
                        name = "测试水系宠物",
                        attribute = "水",
                        is_active = true,
                        create_time = DateTime.Now
                    },
                    new pet_config
                    {
                        pet_no = 999003,
                        name = "测试神系宠物",
                        attribute = "神",
                        is_active = true,
                        create_time = DateTime.Now
                    }
                };
                await _dbContext.Db.Insertable(testPetConfigs).ExecuteCommandAsync();

                // 4. 创建测试用户宠物
                var testUserPets = new List<user_pet>
                {
                    new user_pet
                    {
                        id = TEST_MAIN_PET_ID,
                        user_id = TEST_USER_ID,
                        pet_no = 999001,
                        custom_name = "测试主宠",
                        level = 50,
                        exp = 100000,
                        growth = 35.5m,
                        synthesis_count = 0,
                        element = "火",
                        state = 1,
                        create_time = DateTime.Now
                    },
                    new user_pet
                    {
                        id = TEST_VICE_PET_ID,
                        user_id = TEST_USER_ID,
                        pet_no = 999002,
                        custom_name = "测试副宠",
                        level = 45,
                        exp = 80000,
                        growth = 25.8m,
                        synthesis_count = 0,
                        element = "水",
                        state = 1,
                        create_time = DateTime.Now
                    }
                };
                await _dbContext.Db.Insertable(testUserPets).ExecuteCommandAsync();

                // 5. 创建测试合成配置
                var testConfigs = new List<pet_synthesis_config>
                {
                    new pet_synthesis_config { config_key = "synthesis.cd_time", config_value = "1000", config_type = "LONG", description = "测试CD时间", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.cost_gold", config_value = "50000", config_type = "LONG", description = "合成消耗金币", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.base_success_rate", config_value = "50", config_type = "DECIMAL", description = "基础成功率", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.min_level", config_value = "40", config_type = "INTEGER", description = "最低等级要求", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.effect_multiplier", config_value = "1.0", config_type = "DECIMAL", description = "效果系数", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.vip_growth_bonus", config_value = "0.005", config_type = "DECIMAL", description = "VIP成长加成", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.supreme_vip_growth_bonus", config_value = "0.01", config_type = "DECIMAL", description = "至尊VIP成长加成", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.god_pet_threshold", config_value = "45", config_type = "DECIMAL", description = "神宠合成阈值", is_active = true },
                    new pet_synthesis_config { config_key = "synthesis.god_pet_max_rate", config_value = "35", config_type = "INTEGER", description = "神宠最大概率", is_active = true }
                };
                await _dbContext.Db.Insertable(testConfigs).ExecuteCommandAsync();

                // 6. 创建测试合成公式
                var testFormula = new pet_synthesis_formula
                {
                    main_pet_no = 999001,
                    vice_pet_no = 999002,
                    main_growth_required = 30.0m,
                    vice_growth_required = 20.0m,
                    result_pet_no = 999003,
                    success_rate_bonus = 10.0m,
                    growth_bonus = 5.0m,
                    is_active = true,
                    create_time = DateTime.Now
                };
                await _dbContext.Db.Insertable(testFormula).ExecuteCommandAsync();

                // 7. 创建测试道具
                var testItems = new List<item_config>
                {
                    new item_config
                    {
                        item_no = 2016100402,
                        name = "基础副宠道具",
                        type = "合成道具",
                        description = "提供5%成长加成",
                        script = "成长加成|0.05",
                        is_active = true,
                        create_time = DateTime.Now
                    }
                };
                await _dbContext.Db.Insertable(testItems).ExecuteCommandAsync();

                // 8. 给测试用户添加道具
                var testUserItem = new user_item
                {
                    user_id = TEST_USER_ID,
                    item_id = "2016100402",
                    item_count = 100,
                    create_time = DateTime.Now
                };
                await _dbContext.Db.Insertable(testUserItem).ExecuteCommandAsync();

                _logger.LogInformation("合成测试数据初始化完成");

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "合成测试数据初始化成功",
                    Data = new
                    {
                        TestUserId = TEST_USER_ID,
                        MainPetId = TEST_MAIN_PET_ID,
                        VicePetId = TEST_VICE_PET_ID,
                        ConfigCount = testConfigs.Count,
                        FormulaCount = 1,
                        ItemCount = testItems.Count
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化合成测试数据异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"初始化测试数据失败: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// 运行完整的合成测试
        /// </summary>
        [HttpPost("run-full-test")]
        public async Task<IActionResult> RunFullSynthesisTest()
        {
            var testResults = new List<string>();

            try
            {
                testResults.Add("=== 开始宠物合成系统测试 ===");

                // 1. 验证测试数据
                var mainPet = await _dbContext.Db.Queryable<user_pet>()
                    .FirstAsync(x => x.id == TEST_MAIN_PET_ID);
                var vicePet = await _dbContext.Db.Queryable<user_pet>()
                    .FirstAsync(x => x.id == TEST_VICE_PET_ID);

                if (mainPet == null || vicePet == null)
                {
                    testResults.Add("❌ 测试数据不存在，请先初始化");
                    return BadRequest(new ApiResponse<List<string>>
                    {
                        Success = false,
                        Message = "测试数据不存在",
                        Data = testResults
                    });
                }

                testResults.Add($"✅ 测试数据验证成功：主宠成长{mainPet.growth}, 副宠成长{vicePet.growth}");

                // 2. 测试合成配置获取
                var config = await _synthesisService.GetSynthesisConfigAsync(TEST_USER_ID, TEST_MAIN_PET_ID, TEST_VICE_PET_ID);
                if (config != null)
                {
                    testResults.Add($"✅ 获取合成配置成功：成功率{config.ActualSuccessRate}%, 消耗金币{config.CostGold}");
                    testResults.Add($"✅ 神宠概率：{config.GodPetProbability}%");
                    testResults.Add($"✅ VIP加成：{config.VipBonus.BonusDescription}");
                    
                    if (config.MatchedFormula != null)
                    {
                        testResults.Add($"✅ 匹配公式：{config.MatchedFormula.ResultPetName}");
                    }
                }
                else
                {
                    testResults.Add("❌ 获取合成配置失败");
                }

                // 3. 测试条件验证
                var (isValid, errorMessage) = await _synthesisService.ValidateSynthesisConditionsAsync(TEST_USER_ID, TEST_MAIN_PET_ID, TEST_VICE_PET_ID);
                if (isValid)
                {
                    testResults.Add("✅ 合成条件验证通过");
                }
                else
                {
                    testResults.Add($"❌ 合成条件验证失败：{errorMessage}");
                }

                // 4. 执行合成测试
                if (isValid)
                {
                    var synthesisRequest = new SynthesisRequestDto
                    {
                        MainPetId = TEST_MAIN_PET_ID,
                        VicePetId = TEST_VICE_PET_ID,
                        UsedItems = new List<string> { "2016100402" }
                    };

                    var synthesisResult = await _synthesisService.SynthesizePetAsync(TEST_USER_ID, synthesisRequest);
                    
                    if (synthesisResult.Success)
                    {
                        testResults.Add($"✅ 合成成功：成长增加{synthesisResult.GrowthIncrease}, 新成长{synthesisResult.NewGrowth}");
                        testResults.Add($"✅ 合成结果：{synthesisResult.BeforePetNo} → {synthesisResult.AfterPetNo}");
                        
                        if (synthesisResult.IsGodPet)
                        {
                            testResults.Add("🎉 恭喜！合成出神宠！");
                        }
                        
                        if (synthesisResult.UsedFormula)
                        {
                            testResults.Add($"✅ 使用了合成公式：ID={synthesisResult.FormulaId}");
                        }
                    }
                    else
                    {
                        testResults.Add($"❌ 合成失败：{synthesisResult.Message}");
                    }
                }

                // 5. 测试历史记录
                var history = await _synthesisService.GetSynthesisHistoryAsync(TEST_USER_ID);
                testResults.Add($"✅ 合成历史记录：{history.Count}条");

                // 6. 测试统计信息
                var statistics = await _synthesisService.GetSynthesisStatisticsAsync(TEST_USER_ID);
                testResults.Add($"✅ 合成统计：总次数{statistics.TotalSynthesis}, 成功率{statistics.SuccessRate:F1}%");

                testResults.Add("=== 宠物合成系统测试完成 ===");

                return Ok(new ApiResponse<List<string>>
                {
                    Success = true,
                    Message = "合成系统测试完成",
                    Data = testResults
                });
            }
            catch (Exception ex)
            {
                testResults.Add($"❌ 测试异常：{ex.Message}");
                _logger.LogError(ex, "合成系统测试异常");
                
                return StatusCode(500, new ApiResponse<List<string>>
                {
                    Success = false,
                    Message = "测试执行失败",
                    Data = testResults
                });
            }
        }

        /// <summary>
        /// 获取测试宠物信息
        /// </summary>
        [HttpGet("test-pet-info")]
        public async Task<IActionResult> GetTestPetInfo()
        {
            try
            {
                var mainPet = await _dbContext.Db.Queryable<user_pet>()
                    .FirstAsync(x => x.id == TEST_MAIN_PET_ID);
                var vicePet = await _dbContext.Db.Queryable<user_pet>()
                    .FirstAsync(x => x.id == TEST_VICE_PET_ID);

                if (mainPet == null || vicePet == null)
                {
                    return NotFound(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "测试宠物不存在，请先初始化测试数据"
                    });
                }

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "获取测试宠物信息成功",
                    Data = new
                    {
                        MainPet = new
                        {
                            Id = mainPet.id,
                            Name = mainPet.custom_name,
                            PetNo = mainPet.pet_no,
                            Level = mainPet.level,
                            Growth = mainPet.growth,
                            Element = mainPet.element,
                            SynthesisCount = mainPet.synthesis_count ?? 0
                        },
                        VicePet = new
                        {
                            Id = vicePet.id,
                            Name = vicePet.custom_name,
                            PetNo = vicePet.pet_no,
                            Level = vicePet.level,
                            Growth = vicePet.growth,
                            Element = vicePet.element,
                            SynthesisCount = vicePet.synthesis_count ?? 0
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试宠物信息异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "获取测试宠物信息失败"
                });
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        [HttpDelete("cleanup-test-data")]
        public async Task<IActionResult> CleanupTestData()
        {
            try
            {
                await CleanupTestDataInternal();

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "测试数据清理完成"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理测试数据异常");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = "清理测试数据失败"
                });
            }
        }

        /// <summary>
        /// 内部清理测试数据方法
        /// </summary>
        private async Task CleanupTestDataInternal()
        {
            // 删除测试合成记录
            await _dbContext.Db.Deleteable<pet_synthesis_log>()
                .Where(x => x.user_id == TEST_USER_ID)
                .ExecuteCommandAsync();

            // 删除测试用户宠物
            await _dbContext.Db.Deleteable<user_pet>()
                .Where(x => x.user_id == TEST_USER_ID)
                .ExecuteCommandAsync();

            // 删除测试用户道具
            await _dbContext.Db.Deleteable<user_item>()
                .Where(x => x.user_id == TEST_USER_ID)
                .ExecuteCommandAsync();

            // 删除测试用户
            await _dbContext.Db.Deleteable<user>()
                .Where(x => x.id == TEST_USER_ID)
                .ExecuteCommandAsync();

            // 删除测试宠物配置
            await _dbContext.Db.Deleteable<pet_config>()
                .Where(x => x.pet_no >= 999001 && x.pet_no <= 999003)
                .ExecuteCommandAsync();

            // 删除测试道具配置
            await _dbContext.Db.Deleteable<item_config>()
                .Where(x => x.item_no == 2016100402)
                .ExecuteCommandAsync();

            // 删除测试合成公式
            await _dbContext.Db.Deleteable<pet_synthesis_formula>()
                .Where(x => x.main_pet_no >= 999001 && x.main_pet_no <= 999003)
                .ExecuteCommandAsync();

            // 删除测试配置
            await _dbContext.Db.Deleteable<pet_synthesis_config>()
                .Where(x => x.description.Contains("测试"))
                .ExecuteCommandAsync();

            _logger.LogInformation("测试数据清理完成");
        }
    }
}
