### Cocos Creator 3.8 场景图相关接口规则文档

#### 一、命名空间
##### 1. Layers
暂未获取到详细说明，推测该命名空间与节点层管理相关，层数据可能以掩码数据方式存储，用于射线检测、物理碰撞和用户自定义脚本逻辑。

##### 2. Prefab
暂未获取到详细说明，推测该命名空间与预制资源相关，可能包含预制资源的创建、管理等相关功能。

##### 3. _utils
暂未获取到详细说明，推测该命名空间包含一些与场景图相关的工具函数或辅助类。

#### 二、类
##### 1. AmbientInfo
- **说明**：场景的环境光照相关配置。
- **使用场景**：在需要设置场景环境光照时使用，例如创建一个室内场景，可通过该类配置环境光的颜色、强度等参数，营造出不同的氛围。

##### 2. Component
- **说明**：所有附加到节点的基类。需要注意的是，不允许使用组件的子类构造参数，因为组件是由引擎创建的。
- **使用场景**：开发自定义组件时，通常会继承该类，然后实现相应的功能。例如创建一个控制节点移动的组件，就可以继承 `Component` 类，并在其中实现移动逻辑。

##### 3. CompPrefabInfo
暂未获取到详细说明，推测该类与预制资源的组件信息相关，可能用于存储和管理预制资源中组件的配置信息。

##### 4. EventHandler
- **说明**：“EventHandler” 类用来设置场景中的事件回调，该类允许用户设置回调目标节点，目标组件名，组件方法名，并可通过 `emit` 方法调用目标函数。
- **使用场景**：在处理节点的交互事件时使用，比如按钮的点击事件。可以创建一个 `EventHandler` 实例，设置目标节点、组件名和方法名，当按钮被点击时，调用 `emit` 方法触发相应的回调函数。

##### 5. FogInfo
- **说明**：全局雾相关配置。
- **使用场景**：在创建需要雾效果的场景时使用，如森林场景、迷雾场景等。通过该类可以配置雾的颜色、密度、范围等参数，增强场景的氛围和真实感。

##### 6. Layers
- **说明**：节点层管理器，层数据是以掩码数据方式存储在 `layer` 中，用于射线检测、物理碰撞和用户自定义脚本逻辑。每个节点可属于一个或多个层，可通过 “包含式” 或 “排除式” 两种检测器进行层检测。
- **使用场景**：在进行射线检测或物理碰撞检测时，可根据节点所在的层进行筛选，只检测特定层的节点。例如，在射击游戏中，可将敌人和障碍物设置在不同的层，射线检测时只检测敌人所在的层。

##### 7. LightProbeInfo
- **说明**：光照探针配置。
- **使用场景**：在需要精确控制场景光照的情况下使用，例如大型室外场景。光照探针可以收集周围的光照信息，并将其应用到场景中的物体上，使物体的光照效果更加真实。

##### 8. MountedChildrenInfo
暂未获取到详细说明，推测该类与节点挂载子节点的信息相关，可能用于存储和管理子节点的挂载状态、顺序等信息。

##### 9. MountedComponentsInfo
暂未获取到详细说明，推测该类与节点挂载组件的信息相关，可能用于存储和管理组件的挂载状态、配置等信息。

##### 10. Node
- **说明**：Cocos Creator 场景中的所有节点类。基本特性有：具有层级关系、持有各类组件、维护 3D 空间左边变换（坐标、旋转、缩放）信息。
- **使用场景**：在创建游戏场景时，所有的游戏对象都可以看作是节点，通过创建节点并设置其属性和挂载组件，可以构建出复杂的游戏场景。例如创建一个角色节点，设置其位置、旋转和缩放，再挂载动画组件、碰撞组件等，实现角色的各种功能。

##### 11. NodeActivator
- **说明**：用于执行节点和组件的激活和停用操作的管理器。
- **使用场景**：在需要动态控制节点和组件的显示与隐藏时使用。例如，在游戏中，当玩家触发某个事件时，使用 `NodeActivator` 激活或停用特定的节点或组件。

##### 12. OctreeInfo
- **说明**：基于八叉树的场景剔除配置。
- **使用场景**：在处理大型场景时使用，通过八叉树算法可以快速剔除不可见的物体，提高场景的渲染效率。例如，在开放世界游戏中，使用八叉树进行场景剔除，减少不必要的渲染计算。

##### 13. PostSettingsInfo
暂未获取到详细说明，推测该类与后期处理设置相关，可能用于配置场景的后期处理效果，如色调映射、抗锯齿等。

##### 14. Prefab
- **说明**：预制资源类。
- **使用场景**：在创建可复用的游戏对象时使用，例如创建多个相同的敌人、道具等。将这些对象制作成预制资源，在需要时可以快速实例化，提高开发效率。

##### 15. PrefabInfo
暂未获取到详细说明，推测该类与预制资源的信息相关，可能用于存储和管理预制资源的配置信息、依赖关系等。

##### 16. PrefabInstance
暂未获取到详细说明，推测该类与预制资源的实例化相关，可能用于创建预制资源的实例，并对实例进行管理。

##### 17. PrivateNode
暂未获取到详细说明，推测该类与私有节点相关，可能用于创建一些不希望被外部直接访问的节点。

##### 18. PropertyOverrideInfo
暂未获取到详细说明，推测该类与属性覆盖信息相关，可能用于在特定情况下覆盖节点或组件的默认属性。

##### 19. Scene
- **说明**：`Scene` 是 `Node` 的子类，由节点所构成，代表着游戏中可运行的某一个整体环境。它由 `Director` 管理，用户可以使用 `loadScene` 来切换场景。
- **使用场景**：在开发游戏时，不同的关卡、界面等都可以看作是一个场景。通过 `loadScene` 方法可以实现场景的切换，例如从主菜单场景切换到游戏关卡场景。

##### 20. SceneGlobals
- **说明**：各类场景级别的渲染参数，将影响全场景的所有物体。
- **使用场景**：在需要统一设置场景渲染参数时使用，例如设置场景的全局光照强度、阴影质量等参数，使整个场景的渲染效果保持一致。

##### 21. ShadowsInfo
- **说明**：场景级别阴影相关的配置。
- **使用场景**：在需要设置场景阴影效果时使用，例如配置阴影的类型、强度、分辨率等参数，使场景中的阴影更加真实。

##### 22. SkinInfo
- **说明**：渲染场景中的全局皮肤后处理设置。
- **使用场景**：在需要对场景中的物体进行皮肤后处理时使用，例如设置物体的材质、纹理等属性，使物体的外观更加丰富。

##### 23. SkyboxInfo
- **说明**：天空盒相关配置。
- **使用场景**：在创建场景时，使用天空盒可以营造出逼真的天空环境。通过 `SkyboxInfo` 类可以配置天空盒的材质、颜色等参数，使天空盒与场景更加融合。

##### 24. TargetInfo
暂未获取到详细说明，推测该类与目标信息相关，可能用于存储和管理目标的位置、状态等信息。

##### 25. TargetOverrideInfo
暂未获取到详细说明，推测该类与目标覆盖信息相关，可能用于在特定情况下覆盖目标的默认属性。

#### 三、接口
##### 1. ILightProbeNode
暂未获取到详细说明，推测该接口与光照探针节点相关，可能定义了光照探针节点的一些基本属性和方法。

##### 2. TargetMap
暂未获取到详细说明，推测该接口与目标映射相关，可能用于存储和管理目标与其他对象之间的映射关系。

#### 四、函数
##### 1. applyMountedChildren
暂未获取到详细说明，推测该函数用于应用挂载子节点的相关操作，可能会更新子节点的状态、位置等信息。

##### 2. applyMountedComponents
暂未获取到详细说明，推测该函数用于应用挂载组件的相关操作，可能会更新组件的状态、配置等信息。

##### 3. applyNodeAndComponentId
暂未获取到详细说明，推测该函数用于应用节点和组件的 ID 相关操作，可能会为节点和组件分配唯一的 ID 或更新其 ID。

##### 4. applyPropertyOverrides
暂未获取到详细说明，推测该函数用于应用属性覆盖操作，可能会根据特定的规则覆盖节点或组件的默认属性。

##### 5. applyRemovedComponents
暂未获取到详细说明，推测该函数用于应用移除组件的相关操作，可能会从节点上移除指定的组件并进行清理。

##### 6. applyTargetOverrides
暂未获取到详细说明，推测该函数用于应用目标覆盖操作，可能会根据特定的规则覆盖目标的默认属性。

##### 7. createNodeWithPrefab
暂未获取到详细说明，推测该函数用于使用预制资源创建节点，可能会根据预制资源的配置信息创建一个新的节点实例。

##### 8. expandNestedPrefabInstanceNode
暂未获取到详细说明，推测该函数用于展开嵌套的预制资源实例节点，可能会将嵌套的预制资源实例展开为具体的节点结构。

##### 9. expandPrefabInstanceNode
暂未获取到详细说明，推测该函数用于展开预制资源实例节点，可能会将预制资源实例展开为具体的节点结构。

##### 10. find
- **说明**：通过路径从节点树中查找节点的方法，路径是大小写敏感的，并且通过 `/` 来分隔节点层级。即使节点的状态是未启用的也可以找到，建议将结果缓存，而不是每次需要都去查找。
- **使用场景**：在需要查找特定节点时使用，例如在一个复杂的场景中，通过节点路径查找某个特定的道具节点。

##### 11. generateTargetMap
暂未获取到详细说明，推测该函数用于生成目标映射，可能会根据特定的规则生成目标与其他对象之间的映射关系。

##### 12. getTarget
暂未获取到详细说明，推测该函数用于获取目标对象，可能会根据特定的条件返回相应的目标对象。

#### 五、枚举
##### 1. NodeEventType
- **说明**：所有 `Node` 可能派发的事件类型。
- **使用场景**：在监听节点事件时使用，例如监听节点的点击事件、触摸事件等。可以根据 `NodeEventType` 枚举值来判断事件的类型，然后执行相应的处理逻辑。

##### 2. NodeSpace
- **说明**：节点的坐标空间。
- **使用场景**：在处理节点的位置和变换时使用，例如设置节点的位置、旋转和缩放时，需要指定是在本地坐标空间还是世界坐标空间进行操作。

##### 3. TransformBit
- **说明**：节点的空间变换位标记。
- **使用场景**：在需要对节点的空间变换进行精确控制时使用，例如只更新节点的位置或旋转，而不更新缩放。可以通过 `TransformBit` 枚举值来指定需要更新的变换位。

#### 六、变量
##### 1. DEFAULT_OCTREE_DEPTH
暂未获取到详细说明，推测该变量是八叉树的默认深度，用于控制八叉树的划分层次。

##### 2. DEFAULT_WORLD_MAX_POS
暂未获取到详细说明，推测该变量是世界坐标系的最大位置，用于定义世界的边界。

##### 3. DEFAULT_WORLD_MIN_POS
暂未获取到详细说明，推测该变量是世界坐标系的最小位置，用于定义世界的边界。

##### 4. MobilityMode
- **说明**：节点的移动性。
- **使用场景**：在需要控制节点的移动方式时使用，例如设置节点为静态节点、动态节点或可移动节点等。