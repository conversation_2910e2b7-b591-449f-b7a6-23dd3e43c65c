﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///战斗记录表
    ///</summary>
    [SugarTable("battle_records")]
    public partial class battle_records
    {
           public battle_records(){


           }
           /// <summary>
           /// Desc:战斗ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int battle_id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int pet_id {get;set;}

           /// <summary>
           /// Desc:怪物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int monster_id {get;set;}

           /// <summary>
           /// Desc:地图ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int map_id {get;set;}

           /// <summary>
           /// Desc:战斗结果
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string battle_result {get;set;}

           /// <summary>
           /// Desc:战斗回合数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? rounds {get;set;}

           /// <summary>
           /// Desc:造成伤害
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? damage_dealt {get;set;}

           /// <summary>
           /// Desc:受到伤害
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? damage_received {get;set;}

           /// <summary>
           /// Desc:获得经验
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? experience_gained {get;set;}

           /// <summary>
           /// Desc:获得金币
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? money_gained {get;set;}

           /// <summary>
           /// Desc:获得元宝
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? yuanbao_gained {get;set;}

           /// <summary>
           /// Desc:获得道具(JSON格式)
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(IsJson=true)]           
           public object items_gained {get;set;}

           /// <summary>
           /// Desc:战斗时长(毫秒)
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? battle_duration {get;set;}

           /// <summary>
           /// Desc:加深伤害
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? damage_amplified {get;set;}

           /// <summary>
           /// Desc:抵消伤害
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? damage_reduced {get;set;}

           /// <summary>
           /// Desc:吸血回复
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? hp_healed {get;set;}

           /// <summary>
           /// Desc:吸魔回复
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? mp_healed {get;set;}

           /// <summary>
           /// Desc:是否先手
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_first_strike {get;set;}

           /// <summary>
           /// Desc:反作弊评分
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? anti_cheat_score {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
