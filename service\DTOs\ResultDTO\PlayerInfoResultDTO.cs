namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 玩家信息结果DTO
    /// </summary>
    public class PlayerInfoResultDTO
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 返回消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 玩家信息
        /// </summary>
        public PlayerInfoDTO? PlayerInfo { get; set; }
    }

    /// <summary>
    /// 玩家信息DTO
    /// </summary>
    public class PlayerInfoDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string Account { get; set; } = string.Empty;

        /// <summary>
        /// 昵称
        /// </summary>
        public string Nickname { get; set; } = string.Empty;

        /// <summary>
        /// 等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        public long Experience { get; set; }

        /// <summary>
        /// 金币
        /// </summary>
        public long Gold { get; set; }

        /// <summary>
        /// 元宝
        /// </summary>
        public long Diamond { get; set; }

        /// <summary>
        /// 主战宠物ID
        /// </summary>
        public int? MainPetId { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime LastLoginTime { get; set; }
    }
} 