﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///道具脚本表
    ///</summary>
    [SugarTable("item_script")]
    public partial class item_script
    {
           public item_script(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:道具编号（关联item_config.item_no）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int item_no {get;set;}

           /// <summary>
           /// Desc:道具脚本内容
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? script {get;set;}

           /// <summary>
           /// Desc:道具脚本说明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? description {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
