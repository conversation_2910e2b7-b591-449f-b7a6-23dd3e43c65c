using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.DTOs.RequestDTO;

namespace WebApplication_HM.Controllers
{
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class PlayerController : ControllerBase
    {
        private readonly IPlayerService _playerService;
        public PlayerController(IPlayerService playerService)
        {
            _playerService = playerService;
        }

        /// <summary>
        /// 用户登录接口，校验账号密码，返回登录结果
        /// </summary>
        /// <param name="request">登录请求参数</param>
        /// <returns>登录结果</returns>
        [HttpPost]
        public ActionResult<LoginResultDTO> Login([FromBody] LoginRequestDTO request)
        {
            var result = _playerService.Login(request);
            return Ok(result);
        }

        /// <summary>
        /// 玩家注册
        /// </summary>
        /// <param name="request">注册请求参数</param>
        /// <returns>注册结果</returns>
        [HttpPost]
        public ActionResult<RegisterResultDTO> Register([FromBody] RegisterRequestDTO request)
        {
            var result = _playerService.Register(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取玩家基础信息
        /// </summary>
        /// <param name="request">玩家信息查询请求</param>
        /// <returns>玩家信息结果</returns>
        [HttpPost]
        public ActionResult<PlayerInfoResultDTO> GetPlayerInfo([FromBody] PlayerInfoRequestDTO request)
        {
            var result = _playerService.GetPlayerInfo(request);
            return Ok(result);
        }

        /// <summary>
        /// 领取战斗奖励
        /// </summary>
        /// <param name="request">领取奖励请求</param>
        /// <returns>奖励结果</returns>
        [HttpPost]
        public ActionResult<ClaimRewardResultDTO> ClaimBattleReward([FromBody] ClaimRewardRequestDTO request)
        {
            var result = _playerService.ClaimBattleReward(request);
            return Ok(result);
        }

        /// <summary>
        /// 玩家发起战斗，自动分配怪物并结算，返回战斗结果
        /// </summary>
        /// <param name="request">战斗请求参数</param>
        /// <returns>战斗结果</returns>
        [HttpPost]
        public ActionResult<BattleResultDTO> CalculateBattle([FromBody] BattleRequestDTO request)
        {
            var result = _playerService.BattleCalculate(request);
            return Ok(result);
        }

        #region 宠物核心系统接口

                /// <summary>
        /// 获取用户宠物列表 Player/GetUserPets
        /// </summary>
        /// <param name="request">宠物列表查询请求</param>
        /// <returns>宠物列表结果</returns>
        [HttpPost]
        public ActionResult<PetListResultDTO> GetUserPets([FromBody] PetListRequestDTO request)
        {
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 根据状态获取宠物列表 Player/GetUserPetsByStatus
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="status">宠物状态（可选：牧场、携带）</param>
        /// <param name="onlyMain">是否只查询主战宠物（可选）</param>
        /// <returns>宠物列表结果</returns>
        [HttpGet]
        public ActionResult<PetListResultDTO> GetUserPetsByStatus(int userId, [FromQuery] string? status = null, [FromQuery] bool? onlyMain = null)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                Status = status,
                OnlyMainPet = onlyMain
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取牧场宠物列表 Player/GetRanchPets
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>牧场宠物列表</returns>
        [HttpGet]
        public ActionResult<PetListResultDTO> GetRanchPets(int userId)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                Status = "牧场"
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取携带宠物列表 Player/GetCarryPets
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>携带宠物列表</returns>
        [HttpGet]
        public ActionResult<PetListResultDTO> GetCarryPets(int userId)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                Status = "携带"
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取主战宠物 Player/GetMainPet
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>主战宠物信息</returns>
        [HttpGet]
        public ActionResult<PetListResultDTO> GetMainPet(int userId)
        {
            var request = new PetListRequestDTO
            {
                UserId = userId,
                OnlyMainPet = true
            };
            var result = _playerService.GetUserPets(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取宠物详细信息
        /// </summary>
        /// <param name="request">宠物详情查询请求</param>
        /// <returns>宠物详情结果</returns>
        [HttpPost]
        public ActionResult<PetDetailResultDTO> GetPetDetail([FromBody] PetDetailRequestDTO request)
        {
            var result = _playerService.GetPetDetail(request);
            return Ok(result);
        }

        /// <summary>
        /// 设置主战宠物
        /// </summary>
        /// <param name="request">设置主战宠物请求</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public ActionResult<LoginResultDTO> SetMainPet([FromBody] SetMainPetRequestDTO request)
        {
            var result = _playerService.SetMainPet(request);
            return Ok(result);
        }

        /// <summary>
        /// 计算宠物最终属性
        /// </summary>
        /// <param name="request">属性计算请求</param>
        /// <returns>属性计算结果</returns>
        [HttpPost]
        public ActionResult<AttributeResultDTO> GetPetAttributes([FromBody] AttributeRequestDTO request)
        {
            var result = _playerService.GetPetAttributes(request);
            return Ok(result);
        }

        #endregion

        #region 地图战斗系统接口

        /// <summary>
        /// 获取地图列表
        /// </summary>
        /// <param name="request">地图列表查询请求</param>
        /// <returns>地图列表结果</returns>
        [HttpPost]
        public ActionResult<MapListResultDTO> GetMapList([FromBody] MapListRequestDTO request)
        {
            var result = _playerService.GetMapList(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取地图详细信息
        /// </summary>
        /// <param name="request">地图详情查询请求</param>
        /// <returns>地图详情结果</returns>
        [HttpPost]
        public ActionResult<MapDetailResultDTO> GetMapDetail([FromBody] MapDetailRequestDTO request)
        {
            var result = _playerService.GetMapDetail(request);
            return Ok(result);
        }

        #endregion

        #region 配置数据系统接口

        /// <summary>
        /// 获取游戏配置数据
        /// </summary>
        /// <param name="request">配置查询请求</param>
        /// <returns>游戏配置结果</returns>
        [HttpPost]
        public ActionResult<GameConfigResultDTO> GetGameConfig([FromBody] ConfigRequestDTO request)
        {
            var result = _playerService.GetGameConfig(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取宠物配置列表
        /// </summary>
        /// <returns>宠物配置结果</returns>
        [HttpGet]
        public ActionResult<GameConfigResultDTO> GetPetConfigs()
        {
            var result = _playerService.GetPetConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取技能配置列表
        /// </summary>
        /// <returns>技能配置结果</returns>
        [HttpGet]
        public ActionResult<GameConfigResultDTO> GetSkillConfigs()
        {
            var result = _playerService.GetSkillConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取道具配置列表
        /// </summary>
        /// <returns>道具配置结果</returns>
        [HttpGet]
        public ActionResult<GameConfigResultDTO> GetItemConfigs()
        {
            var result = _playerService.GetItemConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取怪物配置列表
        /// </summary>
        /// <returns>怪物配置结果</returns>
        [HttpGet]
        public ActionResult<GameConfigResultDTO> GetMonsterConfigs()
        {
            var result = _playerService.GetMonsterConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取境界配置列表
        /// </summary>
        /// <returns>境界配置结果</returns>
        [HttpGet]
        public ActionResult<GameConfigResultDTO> GetRealmConfigs()
        {
            var result = _playerService.GetRealmConfigs();
            return Ok(result);
        }

        /// <summary>
        /// 获取装备配置列表
        /// </summary>
        /// <returns>装备配置结果</returns>
        [HttpGet]
        public ActionResult<GameConfigResultDTO> GetEquipmentConfigs()
        {
            var result = _playerService.GetEquipmentConfigs();
            return Ok(result);
        }

        #endregion

        #region 装备系统接口

        /// <summary>
        /// 获取用户装备背包
        /// </summary>
        /// <param name="request">装备列表查询请求</param>
        /// <returns>装备列表结果</returns>
        [HttpPost]
        public ActionResult<EquipmentListResultDTO> GetUserEquipments([FromBody] EquipmentListRequestDTO request)
        {
            var result = _playerService.GetUserEquipments(request);
            return Ok(result);
        }

        /// <summary>
        /// 装备道具
        /// </summary>
        /// <param name="request">装备请求</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public ActionResult<LoginResultDTO> EquipItem([FromBody] EquipRequestDTO request)
        {
            var result = _playerService.EquipItem(request);
            return Ok(result);
        }

        /// <summary>
        /// 卸下装备
        /// </summary>
        /// <param name="request">卸装请求</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public ActionResult<LoginResultDTO> UnequipItem([FromBody] UnequipRequestDTO request)
        {
            var result = _playerService.UnequipItem(request);
            return Ok(result);
        }

        #endregion

        #region 境界系统接口



        #endregion

        #region 背包道具系统接口

        /// <summary>
        /// 获取背包物品列表
        /// </summary>
        /// <param name="request">背包查询请求</param>
        /// <returns>背包结果</returns>
        [HttpPost]
        public ActionResult<BagResultDTO> GetBag([FromBody] BagRequestDTO request)
        {
            var result = _playerService.GetBag(request);
            return Ok(result);
        }

        /// <summary>
        /// 使用道具
        /// </summary>
        /// <param name="request">使用道具请求</param>
        /// <returns>使用结果</returns>
        [HttpPost]
        public ActionResult<UseItemResultDTO> UseItem([FromBody] UseItemRequestDTO request)
        {
            var result = _playerService.UseItem(request);
            return Ok(result);
        }

        /// <summary>
        /// 出售道具
        /// </summary>
        /// <param name="request">出售道具请求</param>
        /// <returns>出售结果</returns>
        [HttpPost]
        public ActionResult<SellItemResultDTO> SellItem([FromBody] SellItemRequestDTO request)
        {
            var result = _playerService.SellItem(request);
            return Ok(result);
        }

        /// <summary>
        /// 整理背包
        /// </summary>
        /// <param name="request">整理背包请求</param>
        /// <returns>整理结果</returns>
        [HttpPost]
        public ActionResult<SortBagResultDTO> SortBag([FromBody] SortBagRequestDTO request)
        {
            var result = _playerService.SortBag(request);
            return Ok(result);
        }

        #endregion
    }
} 