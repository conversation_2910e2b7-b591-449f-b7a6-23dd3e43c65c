using Microsoft.Extensions.Logging;
using SqlSugar;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Services;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 装备分解服务
    /// 基于老项目EquipmentProcess.cs中的Resolve方法完整迁移
    /// </summary>
    public class EquipmentResolveService
    {
        private readonly ILogger<EquipmentResolveService> _logger;
        private readonly DbContext _dbContext;
        private readonly IEquipmentRepository _equipmentRepository;

        // 分解金币消耗 (基于老项目)
        private const long RESOLVE_COST = 200000;

        // 分解道具ID (基于老项目)
        private const string WX_POWER_ITEM_ID = "2016092501"; // 五行之力
        private const string BTS_ITEM_ID = "2016092502";      // 补天石

        // 套装分解奖励配置 (完全基于老项目FJBS字典)
        private static readonly Dictionary<string, int> SUIT_RESOLVE_REWARDS = new()
        {
            {"2017070101", 1000}, // 天魔
            {"2017063001", 1000}, // 自然
            {"20170612", 800},    // 黑白
            {"2016123001", 800},  // 盛世
            {"20161230", 50},     // 创世
            {"2016123101", 100},  // 七宗罪
            {"20170214", 200},    // 情人
            {"2017082201", 120},  // EFS
            {"2018032001", 1200}, // 神圣
            {"2018060401", 1200}, // 神圣卡牌
            {"2018092901", 800},  // 刀剑
            {"2019100101", 800},  // 魔童
            {"2018102701", 1400}, // 沉睡
            {"2018102702", 1300}, // 沉睡卡牌
            {"2019020101", 1200}, // 佩奇
            {"2019060801", 1600}, // 通天卡牌
            {"2019051901", 1600}, // 通天
            {"2019061501", 1800}, // 次元卡牌
            {"2019061502", 1800}, // 次元
            {"2020092801", 2000}, // 圣战卡牌
            {"2020090801", 2000}, // 圣战卡牌
            {"2020092802", 2000}  // 圣战
        };

        public EquipmentResolveService(
            ILogger<EquipmentResolveService> logger,
            DbContext dbContext,
            IEquipmentRepository equipmentRepository)
        {
            _logger = logger;
            _dbContext = dbContext;
            _equipmentRepository = equipmentRepository;
        }

        /// <summary>
        /// 装备分解
        /// 基于老项目EquipmentProcess.cs中的Resolve方法完整迁移
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        public async Task<ApiResult<ResolveResult>> ResolveEquipmentAsync(int userEquipmentId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备信息
                    var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                    if (equipment == null)
                        return ApiResult<ResolveResult>.CreateError("装备不存在");

                    // 2. 检查装备状态
                    if (equipment.is_equipped == true)
                        return ApiResult<ResolveResult>.CreateError("已装备的装备无法分解，请先卸下");

                    // 3. 检查用户金币是否足够
                    var hasEnoughGold = await CheckUserGoldAsync(equipment.user_id, RESOLVE_COST);
                    if (!hasEnoughGold)
                        return ApiResult<ResolveResult>.CreateError("金币不足，分解需要消耗20万金币");

                    // 4. 获取装备详细信息
                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                    if (equipDetail == null)
                        return ApiResult<ResolveResult>.CreateError("装备配置不存在");

                    // 5. 获取装备类型信息
                    var equipType = await _dbContext.Db.Queryable<equipment_type>()
                        .Where(x => x.equip_type_id == equipment.equip_type_id)
                        .FirstAsync();

                    // 6. 计算分解奖励 (基于老项目逻辑)
                    var resolveReward = CalculateResolveReward(equipment, equipDetail, equipType);

                    // 7. 扣除金币
                    await ConsumeGoldAsync(equipment.user_id, RESOLVE_COST);

                    // 8. 发放分解奖励
                    await GrantResolveRewardsAsync(equipment.user_id, resolveReward);

                    // 9. 删除装备
                    await _equipmentRepository.DeleteEquipmentAsync(userEquipmentId);

                    // 10. 记录操作日志
                    await LogResolveOperationAsync(equipment.user_id, userEquipmentId, resolveReward);

                    // 11. 生成成功消息 (基于老项目格式)
                    var message = $"分解成功，您获得了{resolveReward.WxPowerCount}个五行之力和{resolveReward.BtsCount}个补天石！";

                    var result = new ResolveResult
                    {
                        Success = true,
                        Message = message,
                        RewardItems = new Dictionary<string, int>
                        {
                            { WX_POWER_ITEM_ID, resolveReward.WxPowerCount },
                            { BTS_ITEM_ID, resolveReward.BtsCount }
                        },
                        RewardMoney = 0, // 分解不给金币，只消耗金币
                        CostMoney = RESOLVE_COST
                    };

                    return ApiResult<ResolveResult>.CreateSuccess(result, message);
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备分解失败，装备ID: {EquipmentId}", userEquipmentId);
                return ApiResult<ResolveResult>.CreateError("装备分解失败");
            }
        }

        /// <summary>
        /// 计算分解奖励
        /// 基于老项目的分解逻辑完整迁移
        /// </summary>
        private ResolveRewardInfo CalculateResolveReward(user_equipment equipment, equipment_detail equipDetail, equipment_type equipType)
        {
            var reward = new ResolveRewardInfo();

            // 判断是否为套装装备
            var suitId = DetermineSuitIdByEquipId(equipment.equip_id);
            
            if (!string.IsNullOrEmpty(suitId) && SUIT_RESOLVE_REWARDS.ContainsKey(suitId))
            {
                // 套装装备分解 (基于老项目FJBS字典)
                var rewardValue = SUIT_RESOLVE_REWARDS[suitId];
                var rewardArray = GenerateRandomReward(10, 10 * rewardValue + 1, 2);
                reward.WxPowerCount = rewardArray[0];
                reward.BtsCount = rewardArray[1];
            }
            else
            {
                // 普通装备分解 (基于老项目逻辑)
                var isSpecialType = equipType?.type_name == "灵饰" || equipType?.type_name == "法宝";
                var rewardArray = isSpecialType 
                    ? GenerateRandomReward(20, 1041, 2)  // 灵饰/法宝
                    : GenerateRandomReward(10, 1021, 2); // 普通装备
                
                reward.WxPowerCount = rewardArray[0];
                reward.BtsCount = rewardArray[1];
            }

            return reward;
        }

        /// <summary>
        /// 生成随机奖励
        /// 基于老项目DataProcess.Gdsjsz方法
        /// </summary>
        private int[] GenerateRandomReward(int minBase, int maxBase, int count)
        {
            var random = new Random();
            var result = new int[count];
            
            for (int i = 0; i < count; i++)
            {
                result[i] = random.Next(minBase, maxBase + 1);
            }
            
            return result;
        }

        /// <summary>
        /// 根据装备ID判断套装ID
        /// 基于老项目的套装判断逻辑
        /// </summary>
        private string? DetermineSuitIdByEquipId(string equipId)
        {
            foreach (var suitId in SUIT_RESOLVE_REWARDS.Keys)
            {
                if (equipId.StartsWith(suitId))
                {
                    return suitId;
                }
            }
            return null;
        }

        /// <summary>
        /// 检查用户金币是否足够
        /// </summary>
        private async Task<bool> CheckUserGoldAsync(int userId, long requiredGold)
        {
            var user = await _dbContext.Db.Queryable<user>()
                .Where(x => x.id == userId)
                .FirstAsync();
            
            if (user?.money == null) return false;
            
            return long.TryParse(user.money, out long currentGold) && currentGold >= requiredGold;
        }

        /// <summary>
        /// 扣除用户金币
        /// </summary>
        private async Task ConsumeGoldAsync(int userId, long goldAmount)
        {
            await _dbContext.Db.Updateable<user>()
                .SetColumns(x => x.money == (Convert.ToInt64(x.money) - goldAmount).ToString())
                .Where(x => x.id == userId)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 发放分解奖励
        /// </summary>
        private async Task GrantResolveRewardsAsync(int userId, ResolveRewardInfo reward)
        {
            // 发放五行之力
            if (reward.WxPowerCount > 0)
            {
                await AddUserItemAsync(userId, WX_POWER_ITEM_ID, reward.WxPowerCount);
            }

            // 发放补天石
            if (reward.BtsCount > 0)
            {
                await AddUserItemAsync(userId, BTS_ITEM_ID, reward.BtsCount);
            }
        }

        /// <summary>
        /// 添加用户道具
        /// </summary>
        private async Task AddUserItemAsync(int userId, string itemId, int count)
        {
            // 检查是否已有该道具
            var existingItem = await _dbContext.Db.Queryable<user_item>()
                .Where(x => x.user_id == userId && x.item_id == itemId)
                .FirstAsync();

            if (existingItem != null)
            {
                // 增加数量
                await _dbContext.Db.Updateable<user_item>()
                    .SetColumns(x => x.item_count == x.item_count + count)
                    .Where(x => x.id == existingItem.id)
                    .ExecuteCommandAsync();
            }
            else
            {
                // 新增道具
                await _dbContext.Db.Insertable(new user_item
                {
                    user_id = userId,
                    item_id = itemId,
                    item_count = count,
                    create_time = DateTime.Now
                }).ExecuteCommandAsync();
            }
        }

        /// <summary>
        /// 记录分解操作日志
        /// </summary>
        private async Task LogResolveOperationAsync(int userId, int userEquipmentId, ResolveRewardInfo reward)
        {
            try
            {
                var log = new equipment_operation_log
                {
                    user_id = userId,
                    user_equipment_id = userEquipmentId,
                    operation_type = "RESOLVE",
                    operation_data = $"分解获得五行之力{reward.WxPowerCount}个,补天石{reward.BtsCount}个",
                    result = "SUCCESS",
                    result_message = "装备分解成功",
                    cost_money = RESOLVE_COST,
                    create_time = DateTime.Now
                };

                await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "记录分解操作日志失败");
            }
        }
    }

    /// <summary>
    /// 分解奖励信息
    /// </summary>
    public class ResolveRewardInfo
    {
        public int WxPowerCount { get; set; } // 五行之力数量
        public int BtsCount { get; set; }     // 补天石数量
    }
}
