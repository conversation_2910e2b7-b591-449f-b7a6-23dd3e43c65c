using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Models;
using WebApplication_HM.Services;
using WebApplication_HM.Interface;
using WebApplication_HM.Services.PropScript;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 道具管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PropController : ControllerBase
    {
        private readonly PropService _propService;
        private readonly IPropRepository _propRepository;
        private readonly ILogger<PropController> _logger;

        public PropController(PropService propService, IPropRepository propRepository, ILogger<PropController> logger)
        {
            _propService = propService;
            _propRepository = propRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户所有道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>道具列表</returns>
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<List<PropInfo>>> GetUserItems(int userId)
        {
            try
            {
                var items = await _propService.GetPAPAsync(userId);
                return Ok(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具失败，用户ID: {UserId}", userId);
                return StatusCode(500, "获取道具列表失败");
            }
        }

        /// <summary>
        /// 按道具ID获取用户道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="itemId">道具ID</param>
        /// <returns>道具信息</returns>
        [HttpGet("user/{userId}/item/{itemId}")]
        public async Task<ActionResult<PropInfo>> GetUserItemById(int userId, string itemId)
        {
            try
            {
                var item = await _propService.GetAP_IDAsync(itemId, userId);
                if (item == null)
                {
                    return NotFound("道具不存在");
                }
                return Ok(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具失败，用户ID: {UserId}, 道具ID: {ItemId}", userId, itemId);
                return StatusCode(500, "获取道具信息失败");
            }
        }

        /// <summary>
        /// 按序号获取道具
        /// </summary>
        /// <param name="itemSeq">道具序号</param>
        /// <returns>道具信息</returns>
        [HttpGet("seq/{itemSeq}")]
        public async Task<ActionResult<PropInfo>> GetItemBySeq(string itemSeq)
        {
            try
            {
                var item = await _propService.GetAP_XHAsync(itemSeq);
                if (item == null)
                {
                    return NotFound("道具不存在");
                }
                return Ok(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按序号获取道具失败，序号: {ItemSeq}", itemSeq);
                return StatusCode(500, "获取道具信息失败");
            }
        }

        /// <summary>
        /// 获取用户指定位置的道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="position">位置（1=背包，2=仓库）</param>
        /// <returns>道具列表</returns>
        [HttpGet("user/{userId}/position/{position}")]
        public async Task<ActionResult<List<PropInfo>>> GetUserItemsByPosition(int userId, int position)
        {
            try
            {
                if (position != 1 && position != 2)
                {
                    return BadRequest("位置参数无效，1=背包，2=仓库");
                }

                var location = (PropLoaction)position;
                var items = await _propService.GetALPPAsync(userId, location);
                return Ok(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户指定位置道具失败，用户ID: {UserId}, 位置: {Position}", userId, position);
                return StatusCode(500, "获取道具列表失败");
            }
        }

        /// <summary>
        /// 添加道具到用户背包
        /// </summary>
        /// <param name="request">添加道具请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("add")]
        public async Task<ActionResult> AddUserItem([FromBody] AddItemRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.ItemId) || request.Count <= 0)
                {
                    return BadRequest("道具ID和数量不能为空或小于等于0");
                }

                var prop = new PropInfo
                {
                    ItemId = request.ItemId,
                    ItemCount = request.Count,
                    ItemPos = request.Position ?? 1
                };

                var success = await _propService.AddPlayerPropAsync(prop, request.UserId);
                if (success)
                {
                    return Ok(new { message = "道具添加成功" });
                }
                else
                {
                    return BadRequest("道具添加失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加道具失败，请求: {@Request}", request);
                return StatusCode(500, "添加道具失败");
            }
        }

        /// <summary>
        /// 使用道具 - 支持完整的道具脚本系统
        /// </summary>
        /// <param name="request">使用道具请求</param>
        /// <returns>操作结果</returns>
        /// <summary>
        /// 丢弃道具（真正删除并记录到丢弃表）
        /// </summary>
        [HttpPost("discard")]
        public async Task<ActionResult> DiscardItem([FromBody] DiscardItemRequest request)
        {
            try
            {
                var result = await _propService.DiscardItemAsync(request.UserId, request.ItemSeq, request.Reason);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "丢弃道具失败，请求: {@Request}", request);
                return StatusCode(500, new { success = false, message = "丢弃道具失败" });
            }
        }

        /// <summary>
        /// 获取用户丢弃记录
        /// </summary>
        [HttpGet("discard-logs/{userId}")]
        public async Task<ActionResult> GetDiscardLogs(int userId, [FromQuery] int page = 1, [FromQuery] int pageSize = 20)
        {
            try
            {
                var logs = await _propService.GetDiscardLogsAsync(userId, page, pageSize);
                return Ok(logs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取丢弃记录失败，用户ID: {UserId}", userId);
                return StatusCode(500, new { success = false, message = "获取丢弃记录失败" });
            }
        }

        /// <summary>
        /// 找回丢弃的道具
        /// </summary>
        [HttpPost("recover/{logId}")]
        public async Task<ActionResult> RecoverDiscardedItem(int logId, [FromBody] int userId)
        {
            try
            {
                var result = await _propService.RecoverDiscardedItemAsync(logId, userId);

                if (result.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = result.Message,
                        data = result.Data
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = result.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "找回道具失败，记录ID: {LogId}, 用户ID: {UserId}", logId, userId);
                return StatusCode(500, new { success = false, message = "找回道具失败" });
            }
        }

        /// <summary>
        /// 移动道具位置（放入仓库等）
        /// </summary>
        [HttpPost("move")]
        public async Task<ActionResult> MoveItem([FromBody] MoveItemRequest request)
        {
            try
            {
                var success = await _propService.MoveItemPositionAsync(request.UserId, request.ItemSeq, request.NewPosition);

                if (success)
                {
                    string positionName = request.NewPosition switch
                    {
                        1 => "背包",
                        2 => "仓库",
                        3 => "丢弃",
                        _ => "未知位置"
                    };

                    return Ok(new
                    {
                        success = true,
                        message = $"道具已移动到{positionName}",
                        newPosition = request.NewPosition
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "移动道具失败"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动道具失败，请求: {@Request}", request);
                return StatusCode(500, new { success = false, message = "移动道具失败" });
            }
        }

        [HttpPost("use")]
        public async Task<ActionResult> UseItem([FromBody] UseItemRequest request)
        {
            try
            {
                // 1. 获取道具信息
                var item = await _propRepository.GetItemBySeqAsync(request.ItemSeq);
                if (item == null)
                {
                    return NotFound(new { success = false, message = "道具不存在" });
                }

                // 2. 执行道具脚本逻辑
                var scriptRequest = new PropScriptExecuteRequest
                {
                    UserId = request.UserId,
                    ItemId = item.ItemId,
                    UseCount = 1
                };

                var scriptResult = _propService.UseItemWithScript(scriptRequest);

                // 3. 返回结果
                if (scriptResult.Success)
                {
                    return Ok(new
                    {
                        success = true,
                        message = scriptResult.Message,
                        data = scriptResult.ExtraData,
                        effectDescription = scriptResult.Message
                    });
                }
                else
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = scriptResult.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用道具失败，请求: {@Request}", request);
                return StatusCode(500, new { success = false, message = "使用道具失败" });
            }
        }

        /// <summary>
        /// 删除道具
        /// </summary>
        /// <param name="itemSeq">道具序号</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{itemSeq}")]
        public async Task<ActionResult> DeleteItem(int itemSeq)
        {
            try
            {
                var item = await _propRepository.GetItemBySeqAsync(itemSeq);
                if (item == null)
                {
                    return NotFound("道具不存在");
                }

                var success = await _propService.DeletePPAsync(item);
                if (success)
                {
                    return Ok(new { message = "道具删除成功" });
                }
                else
                {
                    return BadRequest("道具删除失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除道具失败，序号: {ItemSeq}", itemSeq);
                return StatusCode(500, "删除道具失败");
            }
        }

        /// <summary>
        /// 获取道具配置信息
        /// </summary>
        /// <param name="itemId">道具ID</param>
        /// <returns>道具配置</returns>
        [HttpGet("config/{itemId}")]
        public async Task<ActionResult<PropConfig>> GetItemConfig(string itemId)
        {
            try
            {
                var config = await _propService.GetPropConfigAsync(itemId);
                if (config == null)
                {
                    return NotFound("道具配置不存在");
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具配置失败，道具ID: {ItemId}", itemId);
                return StatusCode(500, "获取道具配置失败");
            }
        }

        /// <summary>
        /// 获取道具脚本信息
        /// </summary>
        /// <param name="itemId">道具ID</param>
        /// <returns>道具脚本</returns>
        [HttpGet("script/{itemId}")]
        public async Task<ActionResult<PropScriptInfo>> GetItemScript(string itemId)
        {
            try
            {
                var script = await _propService.GetPropScriptAsync(itemId);
                if (script == null)
                {
                    return NotFound("道具脚本不存在");
                }
                return Ok(script);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具脚本失败，道具ID: {ItemId}", itemId);
                return StatusCode(500, "获取道具脚本失败");
            }
        }

        /// <summary>
        /// 道具放入背包
        /// </summary>
        /// <param name="itemSeq">道具序号</param>
        /// <param name="userId">用户ID</param>
        /// <returns>操作结果</returns>
        [HttpPut("{itemSeq}/move-to-backpack")]
        public async Task<ActionResult> MoveToBackpack(string itemSeq, [FromQuery] int userId)
        {
            try
            {
                var success = await _propService.PAPTPAsync(itemSeq, userId);
                if (success)
                {
                    return Ok(new { message = "道具已放入背包" });
                }
                else
                {
                    return BadRequest("操作失败，可能是背包已满");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "道具放入背包失败，序号: {ItemSeq}, 用户ID: {UserId}", itemSeq, userId);
                return StatusCode(500, "操作失败");
            }
        }

        /// <summary>
        /// 检查用户是否拥有指定道具
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="itemId">道具ID</param>
        /// <returns>是否拥有</returns>
        [HttpGet("user/{userId}/has/{itemId}")]
        public async Task<ActionResult<bool>> HasItem(int userId, string itemId)
        {
            try
            {
                var hasItem = await _propService.OwnOrNot_PropIDAsync(itemId, userId);
                return Ok(hasItem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查道具拥有状态失败，用户ID: {UserId}, 道具ID: {ItemId}", userId, itemId);
                return StatusCode(500, "检查失败");
            }
        }
    }
}
