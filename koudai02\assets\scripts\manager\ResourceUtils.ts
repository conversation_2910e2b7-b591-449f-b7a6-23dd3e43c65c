import { ResourceManager } from './ResourceManager';
import { RemoteResourceManager } from './RemoteResourceManager';
import { MassiveResourceManager } from './MassiveResourceManager';

/**
 * 资源加载工具类 - 提供便捷的资源加载方法
 */
export class ResourceUtils {
    
    /**
     * 批量预加载宠物相关资源（使用新的MassiveResourceManager）
     */
    public static async preloadPetResources(petIds: number[]): Promise<void> {
        const massiveResourceManager = MassiveResourceManager.getInstance();
        const promises: Promise<any>[] = [];
        
        // 收集需要加载的图集
        const atlasesToLoad = new Set<string>();
        
        for (const petId of petIds) {
            const { atlas } = massiveResourceManager.getPetGroup(petId);
            atlasesToLoad.add(atlas);
            
            // 预加载宠物头像
            promises.push(
                massiveResourceManager.loadPetHead(petId)
                    .catch(err => console.warn(`⚠️ 宠物${petId}头像预加载失败:`, err))
            );
        }
        
        // 预加载所需的宠物图集
        for (const atlas of atlasesToLoad) {
            promises.push(
                massiveResourceManager.loadAtlas(atlas)
                    .catch(err => console.warn(`⚠️ 宠物图集${atlas}预加载失败:`, err))
            );
        }
        
        try {
            await Promise.all(promises);
            console.log('✅ 宠物资源预加载完成');
        } catch (error) {
            console.warn('⚠️ 部分宠物资源预加载失败:', error);
        }
    }

    /**
     * 批量预加载怪物相关资源（使用新的MassiveResourceManager）
     */
    public static async preloadMonsterResources(monsterIds: number[]): Promise<void> {
        const massiveResourceManager = MassiveResourceManager.getInstance();
        const promises: Promise<any>[] = [];
        
        // 收集需要加载的图集
        const atlasesToLoad = new Set<string>();
        
        for (const monsterId of monsterIds) {
            const { atlas } = massiveResourceManager.getMonsterGroup(monsterId);
            atlasesToLoad.add(atlas);
        }
        
        // 预加载所需的怪物图集
        for (const atlas of atlasesToLoad) {
            promises.push(
                massiveResourceManager.loadAtlas(atlas)
                    .catch(err => console.warn(`⚠️ 怪物图集${atlas}预加载失败:`, err))
            );
        }
        
        try {
            await Promise.all(promises);
            console.log('✅ 怪物资源预加载完成');
        } catch (error) {
            console.warn('⚠️ 部分怪物资源预加载失败:', error);
        }
    }

    /**
     * 预加载战斗场景所需的所有资源
     */
    public static async preloadBattleResources(petId: number, monsterId: number): Promise<void> {
        console.log('🎮 开始预加载战斗资源...');
        
        const promises = [
            this.preloadPetResources([petId]),
            this.preloadMonsterResources([monsterId])
        ];
        
        try {
            await Promise.all(promises);
            console.log('✅ 战斗资源预加载完成');
        } catch (error) {
            console.warn('⚠️ 部分战斗资源预加载失败:', error);
        }
    }

    /**
     * 获取资源加载统计信息
     */
    public static getResourceStats(): any {
        const resourceManager = ResourceManager.getInstance();
        const remoteResourceManager = RemoteResourceManager.getInstance();
        
        return {
            local: resourceManager.getLoadingStats(),
            remote: remoteResourceManager.getCacheStats(),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 清理所有资源缓存
     */
    public static clearAllCache(): void {
        const resourceManager = ResourceManager.getInstance();
        const remoteResourceManager = RemoteResourceManager.getInstance();
        
        resourceManager.releaseAll();
        remoteResourceManager.clearCache();
        
        console.log('🗑️ 所有资源缓存已清理');
    }

    /**
     * 检查资源是否已加载
     */
    public static isResourceLoaded(path: string): boolean {
        const resourceManager = ResourceManager.getInstance();
        return resourceManager.getCachedAsset(path) !== null;
    }

    /**
     * 安全加载资源（带重试机制）
     */
    public static async safeLoadResource<T>(
        loadFunction: () => Promise<T>, 
        maxRetries: number = 3,
        retryDelay: number = 1000
    ): Promise<T> {
        let lastError: any;
        
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await loadFunction();
            } catch (error) {
                lastError = error;
                console.warn(`⚠️ 资源加载失败，正在重试 (${i + 1}/${maxRetries}):`, error);
                
                if (i < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }
        
        throw lastError;
    }

    /**
     * 获取宠物指定类型的资源
     * 
     * 使用示例：
     * // 加载宠物1的头像
     * const head = await MassiveResourceManager.getInstance().loadPetHead(1);
     * 
     * // 加载宠物5的攻击资源
     * const attack = await MassiveResourceManager.getInstance().loadPetAttack(5, 'g');
     * 
     * // 加载宠物10的动态资源
     * const dynamic = await MassiveResourceManager.getInstance().loadPetDynamic(10);
     * 
     * // 加载宠物15的其他头像类型
     * const headK = await MassiveResourceManager.getInstance().loadPetHeadType(15, 'k');
     */
    public static async loadPetResourceExample(petId: number, resourceType: string): Promise<void> {
        const massiveResourceManager = MassiveResourceManager.getInstance();
        
        try {
            switch (resourceType) {
                case 't':
                case 'q':
                case 'k':
                    await massiveResourceManager.loadPetHeadType(petId, resourceType as 'q' | 'k' | 't');
                    break;
                case 'g':
                case 'S':
                    await massiveResourceManager.loadPetAttack(petId, resourceType as 'g' | 'S');
                    break;
                case 'z':
                    await massiveResourceManager.loadPetDynamic(petId);
                    break;
                default:
                    console.warn(`⚠️ 未知的资源类型: ${resourceType}`);
                    break;
            }
            console.log(`✅ 宠物${petId}的${resourceType}资源加载成功`);
        } catch (error) {
            console.error(`❌ 宠物${petId}的${resourceType}资源加载失败:`, error);
        }
    }

    /**
     * 图集启用状态管理示例
     * 
     * 使用示例：
     * // 检查图集是否启用
     * const isEnabled = MassiveResourceManager.getInstance().isAtlasGroupEnabled('pets', 'starter');
     * 
     * // 启用/禁用特定图集
     * MassiveResourceManager.getInstance().setAtlasGroupEnabled('pets', 'epic', false);
     * 
     * // 批量设置图集状态
     * MassiveResourceManager.getInstance().batchSetAtlasEnabled({
     *     pets: { starter: true, common: true, rare: false, epic: false },
     *     monsters: { normal: true, elite: false, boss: false }
     * });
     * 
     * // 获取当前启用的图集列表
     * const enabled = MassiveResourceManager.getInstance().getEnabledAtlases();
     */
    public static manageAtlasEnabled(): void {
        const massiveResourceManager = MassiveResourceManager.getInstance();
        
        console.log('📋 图集启用状态管理示例:');
        
        // 示例：只启用基础图集，禁用高级图集以节省内存
        massiveResourceManager.batchSetAtlasEnabled({
            pets: { 
                starter: true,   // 启用新手宠物
                common: true,    // 启用普通宠物
                rare: false,     // 禁用稀有宠物
                epic: false      // 禁用史诗宠物
            },
            monsters: { 
                normal: true,    // 启用普通怪物
                elite: false,    // 禁用精英怪物
                boss: false      // 禁用BOSS怪物
            }
        });
        
        // 获取并显示当前状态
        const enabledAtlases = massiveResourceManager.getEnabledAtlases();
        console.log('✅ 当前启用的图集:', enabledAtlases);
    }
}