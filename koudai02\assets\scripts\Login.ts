import { _decorator, Component, EditBox, Label, Node, director } from 'cc';
const { ccclass, property } = _decorator;
import { HttpRequest } from './tool/HttpRequest';
import { Global } from './Global';
import { SceneManager } from './manager/SceneManager';
//登录脚本
@ccclass('Login')
export class Login extends Component {

    @property(Node)
    LoginNode: Node = null;

    @property(Node)
    RegisterNode: Node = null;

    //账号
    @property(Node)
    account: Node = null;

    //密码
    @property(Node)
    password: Node = null;

    @property(Node)
    info: Node = null;

    start() {
        this.LoginNode.on(Node.EventType.MOUSE_DOWN, this.login, this);
    }

    update(deltaTime: number) {

    }

    public login() {

        HttpRequest.postConvertJson('Player/Login', { 
            number: this.account.getComponent(EditBox).string, 
            password: this.password.getComponent(EditBox).string 
        })
            .then(data => {
            
                if (data.success) {
                     //登录成功
                     Global.password = data;
                     Global.username = this.account.getComponent(EditBox).string;
                     Global.userid = data.userId;
                     console.log("✅ 登录成功，用户ID：" + Global.userid);
                     
                     // 使用SceneManager切换场景
                     SceneManager.loadScene("Friom").catch(error => {
                         console.error("❌ 场景切换失败:", error);
                         this.info.getComponent(Label).string = "场景加载失败，请重试";
                     });
                } else {
                       this.info.getComponent(Label).string = data.message;
                }

            })
            .catch(error => {
                this.info.getComponent(Label).string = error;
            });

    }
}


