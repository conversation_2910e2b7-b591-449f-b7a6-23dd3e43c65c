using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 转生计算服务
    /// </summary>
    public class NirvanaCalculationService : INirvanaCalculationService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<NirvanaCalculationService> _logger;

        public NirvanaCalculationService(ISqlSugarClient db, ILogger<NirvanaCalculationService> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// 计算成长获得 - 基于原系统算法
        /// </summary>
        public async Task<decimal> CalculateGrowthGainAsync(user_pet mainPet, user_pet subPet, decimal effectBonus, user user)
        {
            try
            {
                // 获取基础数据
                var mainGrowth = Convert.ToDecimal(mainPet.growth);
                var subGrowth = Convert.ToDecimal(subPet.growth);
                var mainLevel = Convert.ToInt32(mainPet.level);
                var subLevel = Convert.ToInt32(subPet.level);

                _logger.LogInformation("转生成长计算开始 - 主宠成长:{MainGrowth}, 副宠成长:{SubGrowth}", mainGrowth, subGrowth);

                // 1. 计算成长限制系数
                decimal growthLimitCoeff = CalculateGrowthLimitCoeff(mainGrowth);

                // 2. 计算等级加成系数
                decimal mainLevelBonus = (mainLevel - 60) / 240.0m;
                decimal subLevelBonus = 0.05m + (subLevel - 60) * 0.0012m;

                // 3. 计算基础获得成长
                decimal baseGrowthGain = (mainGrowth * mainLevelBonus + subGrowth * subLevelBonus) / growthLimitCoeff;

                // 4. 计算最终成长
                decimal finalGrowth = CalculateFinalGrowth(baseGrowthGain, effectBonus, user, mainGrowth);

                _logger.LogInformation("转生成长计算完成 - 基础成长:{BaseGrowth}, 最终成长:{FinalGrowth}", 
                    baseGrowthGain, finalGrowth);

                return Math.Max(0, finalGrowth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "转生成长计算失败");
                return 0;
            }
        }

        /// <summary>
        /// 计算成长限制系数
        /// </summary>
        private decimal CalculateGrowthLimitCoeff(decimal mainGrowth)
        {
            if (mainGrowth > 10)
            {
                return (20 - (decimal)Math.Log10((double)mainGrowth)) * 5 * mainGrowth;
            }
            return 200;
        }

        /// <summary>
        /// 计算最终成长
        /// </summary>
        private decimal CalculateFinalGrowth(decimal baseGrowth, decimal effectBonus, user user, decimal mainGrowth)
        {
            // 计算成长加成系数
            decimal growthAdditionCoeff = baseGrowth / mainGrowth;
            decimal methodA = (1 + growthAdditionCoeff) * (1 + effectBonus);
            decimal methodB = 1 + growthAdditionCoeff + effectBonus;

            // VIP加成
            decimal vipBonus = CalculateVipBonus(user);

            // 最终成长计算
            decimal finalGrowth = baseGrowth * Math.Min(methodA, methodB) * vipBonus;

            // 五行特殊加成（如果需要）
            // finalGrowth = ApplyElementBonus(finalGrowth, mainPet.Element);

            return finalGrowth;
        }

        /// <summary>
        /// 计算VIP加成
        /// </summary>
        public decimal CalculateVipBonus(user user)
        {
            try
            {
                var vipLevel = user.vip_level ?? 0;
                decimal vipBonus = 1 + 0.0025m * vipLevel;

                if (user.supreme_vip == true)
                {
                    vipBonus = 1 + 0.005m * vipLevel;
                }

                return vipBonus;
            }
            catch
            {
                return 1.0m; // 默认无加成
            }
        }

        /// <summary>
        /// 计算成功率
        /// </summary>
        public async Task<decimal> CalculateSuccessRateAsync(NirvanaRequestDto request, PetNirvanaConfig config)
        {
            try
            {
                decimal baseSuccessRate = config.BaseSuccessRate / 100m; // 转换为小数
                decimal propBonus = 0.0m;
                bool guaranteedSuccess = false;

                // 道具加成计算
                if (!string.IsNullOrEmpty(request.UsedItemId))
                {
                    var propEffect = await GetPropEffectAsync(request.UsedItemId);
                    if (propEffect.GuaranteedSuccess)
                        guaranteedSuccess = true;
                    propBonus += propEffect.SuccessRateBonus;
                }

                if (guaranteedSuccess) return 1.0m;

                // 最高95%成功率
                return Math.Min(baseSuccessRate + propBonus, 0.95m);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算成功率失败");
                return config.BaseSuccessRate / 100m;
            }
        }

        /// <summary>
        /// 验证转生条件
        /// </summary>
        public async Task<(bool isValid, List<string> errors)> ValidateNirvanaConditionsAsync(NirvanaRequestDto request)
        {
            var errors = new List<string>();

            try
            {
                // 1. 验证用户存在
                var user = await _db.Queryable<user>().FirstAsync(u => u.id == request.UserId);
                if (user == null)
                {
                    errors.Add("用户不存在");
                    return (false, errors);
                }

                // 2. 验证宠物存在且属于用户
                var mainPet = await _db.Queryable<user_pet>()
                    .FirstAsync(p => p.id == request.MainPetId && p.user_id == request.UserId);
                if (mainPet == null)
                {
                    errors.Add("主宠物不存在或不属于当前用户");
                }

                var subPet = await _db.Queryable<user_pet>()
                    .FirstAsync(p => p.id == request.SubPetId && p.user_id == request.UserId);
                if (subPet == null)
                {
                    errors.Add("副宠物不存在或不属于当前用户");
                }

                var nirvanaPet = await _db.Queryable<user_pet>()
                    .FirstAsync(p => p.id == request.NirvanaPetId && p.user_id == request.UserId);
                if (nirvanaPet == null)
                {
                    errors.Add("涅槃兽不存在或不属于当前用户");
                }

                if (errors.Any()) return (false, errors);

                // 3. 验证宠物不能相同
                if (request.MainPetId == request.SubPetId || 
                    request.MainPetId == request.NirvanaPetId || 
                    request.SubPetId == request.NirvanaPetId)
                {
                    errors.Add("转生宠物不能相同");
                }

                // 4. 验证转生配置存在
                var config = await _db.Queryable<PetNirvanaConfig>()
                    .FirstAsync(c => c.MainPetNo == mainPet.pet_no && 
                                   c.SubPetNo == subPet.pet_no && 
                                   c.NirvanaPetNo == nirvanaPet.pet_no && 
                                   c.IsActive == 1);
                if (config == null)
                {
                    errors.Add("不存在对应的转生配置");
                }

                // 5. 验证等级要求
                if (config != null)
                {
                    var mainLevel = Convert.ToInt32(mainPet.level);
                    var subLevel = Convert.ToInt32(subPet.level);
                    
                    if (mainLevel < config.RequiredLevel || subLevel < config.RequiredLevel)
                    {
                        errors.Add($"宠物等级不足，需要达到{config.RequiredLevel}级");
                    }
                }

                // 6. 验证金币是否足够
                if (config != null)
                {
                    var costGold = CalculateCostGold(config, user);
                    var userGold = user.gold ?? 0;
                    
                    if (userGold < costGold)
                    {
                        errors.Add($"金币不足，需要{costGold}金币");
                    }
                }

                return (errors.Count == 0, errors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证转生条件失败");
                errors.Add("验证转生条件时发生错误");
                return (false, errors);
            }
        }

        /// <summary>
        /// 计算转生消耗金币
        /// </summary>
        public long CalculateCostGold(PetNirvanaConfig config, user user)
        {
            // 基础消耗
            long baseCost = config.CostGold;

            // VIP折扣（如果有）
            if (user.supreme_vip == true)
            {
                baseCost = (long)(baseCost * 0.9m); // 至尊VIP 9折
            }
            else if ((user.vip_level ?? 0) > 0)
            {
                baseCost = (long)(baseCost * 0.95m); // 普通VIP 95折
            }

            return baseCost;
        }

        /// <summary>
        /// 获取道具效果
        /// </summary>
        private async Task<PropEffect> GetPropEffectAsync(string itemId)
        {
            try
            {
                // 这里应该查询道具配置表获取道具效果
                // 暂时返回默认值
                return new PropEffect
                {
                    SuccessRateBonus = 0.1m, // 10%成功率加成
                    GuaranteedSuccess = false
                };
            }
            catch
            {
                return new PropEffect();
            }
        }

        /// <summary>
        /// 道具效果类
        /// </summary>
        private class PropEffect
        {
            public decimal SuccessRateBonus { get; set; }
            public bool GuaranteedSuccess { get; set; }
        }
    }
}
