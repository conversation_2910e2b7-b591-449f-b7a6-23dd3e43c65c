using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Models;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 装备模块测试控制�?    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class EquipmentTestController : ControllerBase
    {
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly IGemstoneService _gemstoneService;
        private readonly ISuitService _suitService;
        private readonly ILogger<EquipmentTestController> _logger;

        public EquipmentTestController(
            IEquipmentRepository equipmentRepository,
            IGemstoneService gemstoneService,
            ISuitService suitService,
            ILogger<EquipmentTestController> logger)
        {
            _equipmentRepository = equipmentRepository;
            _gemstoneService = gemstoneService;
            _suitService = suitService;
            _logger = logger;
        }

        /// <summary>
        /// 测试数据库连�?        /// </summary>
        /// <returns>连接状�?/returns>
        [HttpGet("connection")]
        public async Task<ActionResult<ApiResult>> TestConnection()
        {
            try
            {
                var gemstones = await _equipmentRepository.GetAllGemstoneConfigsAsync();
                return Ok(ApiResult.CreateSuccess($"数据库连接正常，宝石配置数量: {gemstones.Count}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库连接测试失败");
                return StatusCode(500, ApiResult.CreateError($"数据库连接失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试宝石配置查询
        /// </summary>
        /// <returns>宝石配置列表</returns>
        [HttpGet("gemstones")]
        public async Task<ActionResult<ApiResult<List<GemstoneConfigDto>>>> TestGemstones()
        {
            try
            {
                var gemstones = await _gemstoneService.GetAllGemstonesAsync();
                return Ok(ApiResult<List<GemstoneConfigDto>>.CreateSuccess(gemstones));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宝石配置失败");
                return StatusCode(500, ApiResult<List<GemstoneConfigDto>>.CreateError($"获取宝石配置失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试套装配置查询
        /// </summary>
        /// <returns>套装配置列表</returns>
        [HttpGet("suits")]
        public async Task<ActionResult<ApiResult<List<SuitConfigDto>>>> TestSuits()
        {
            try
            {
                var suits = await _suitService.GetAllSuitsAsync();
                return Ok(ApiResult<List<SuitConfigDto>>.CreateSuccess(suits));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装配置失败");
                return StatusCode(500, ApiResult<List<SuitConfigDto>>.CreateError($"获取套装配置失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试创建用户装备
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="equipId">装备ID</param>
        /// <returns>创建结果</returns>
        [HttpPost("create-equipment/{userId}/{equipId}")]
        public async Task<ActionResult<ApiResult>> CreateTestEquipment(int userId, string equipId)
        {
            try
            {
                var equipment = new user_equipment
                {
                    user_id = userId,
                    equip_id = equipId,
                    name = $"测试装备_{equipId}",
                    icon = "test_icon.png",
                    equip_type_id = "1", // 武器
                    strengthen_level = 0,
                    slot = 1,
                    element = "金",
                    is_equipped = false,
                    create_time = DateTime.Now,
                    update_time = DateTime.Now
                };

                var success = await _equipmentRepository.AddEquipmentAsync(equipment);
                if (success)
                {
                    return Ok(ApiResult.CreateSuccess($"成功创建测试装备，用户ID: {userId}, 装备ID: {equipId}"));
                }

                return BadRequest(ApiResult.CreateError("创建测试装备失败"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建测试装备失败");
                return StatusCode(500, ApiResult.CreateError($"创建测试装备失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试宝石镶嵌检�?        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <param name="gemstoneTypeName">宝石类型名称</param>
        /// <param name="position">镶嵌位置</param>
        /// <returns>检查结�?/returns>
        [HttpGet("check-embed/{userEquipmentId}/{gemstoneTypeName}/{position}")]
        public async Task<ActionResult<ApiResult>> CheckGemstoneEmbed(int userEquipmentId, string gemstoneTypeName, int position)
        {
            try
            {
                var canEmbed = await _gemstoneService.CanEmbedGemstoneAsync(userEquipmentId, gemstoneTypeName, position);
                var message = canEmbed ? "可以镶嵌" : "不能镶嵌";
                return Ok(ApiResult.CreateSuccess(message, canEmbed));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查宝石镶嵌条件失败");
                return StatusCode(500, ApiResult.CreateError($"检查失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试添加宝石镶嵌
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <param name="gemstoneTypeName">宝石类型名称</param>
        /// <param name="position">镶嵌位置</param>
        /// <returns>镶嵌结果</returns>
        [HttpPost("embed-gemstone/{userEquipmentId}/{gemstoneTypeName}/{position}")]
        public async Task<ActionResult<ApiResult>> EmbedTestGemstone(int userEquipmentId, string gemstoneTypeName, int position)
        {
            try
            {
                var gemstone = new equipment_gemstone
                {
                    user_equipment_id = userEquipmentId,
                    gemstone_type_name = gemstoneTypeName,
                    position = position,
                    create_time = DateTime.Now
                };

                var success = await _equipmentRepository.AddGemstoneAsync(gemstone);
                if (success)
                {
                    return Ok(ApiResult.CreateSuccess($"成功镶嵌宝石: {gemstoneTypeName} 到位�?{position}"));
                }

                return BadRequest(ApiResult.CreateError("镶嵌宝石失败"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "镶嵌测试宝石失败");
                return StatusCode(500, ApiResult.CreateError($"镶嵌失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取装备的宝石信�?        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns>宝石信息</returns>
        [HttpGet("equipment-gemstones/{userEquipmentId}")]
        public async Task<ActionResult<ApiResult<List<GemstoneDto>>>> GetEquipmentGemstones(int userEquipmentId)
        {
            try
            {
                var gemstones = await _gemstoneService.GetEquipmentGemstonesAsync(userEquipmentId);
                return Ok(ApiResult<List<GemstoneDto>>.CreateSuccess(gemstones));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备宝石失败");
                return StatusCode(500, ApiResult<List<GemstoneDto>>.CreateError($"获取失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup/{userId}")]
        public async Task<ActionResult<ApiResult>> CleanupTestData(int userId)
        {
            try
            {
                // 获取用户的测试装备
                var equipments = await _equipmentRepository.GetUserEquipmentsAsync(userId);
                var testEquipments = equipments.Where(e => e.name.StartsWith("测试装备_")).ToList();

                int deletedCount = 0;
                foreach (var equipment in testEquipments)
                {
                    // 先清理宝石
                    await _equipmentRepository.ClearAllGemstonesAsync(equipment.id);
                    // 再删除装备
                    var deleted = await _equipmentRepository.DeleteEquipmentAsync(equipment.id);
                    if (deleted) deletedCount++;
                }

                return Ok(ApiResult.CreateSuccess($"清理完成，删除了 {deletedCount} 件测试装备"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理测试数据失败");
                return StatusCode(500, ApiResult.CreateError($"清理失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpGet("statistics")]
        public async Task<ActionResult<ApiResult>> GetStatistics()
        {
            try
            {
                var gemstoneConfigs = await _equipmentRepository.GetAllGemstoneConfigsAsync();
                var suitConfigs = await _equipmentRepository.GetAllSuitConfigsAsync();

                var stats = new
                {
                    GemstoneConfigs = gemstoneConfigs.Count,
                    SuitConfigs = suitConfigs.Count,
                    GemstonesByLevel = gemstoneConfigs.GroupBy(g => g.level)
                        .ToDictionary(g => g.Key, g => g.Count()),
                    SuitsByTotalPieces = suitConfigs.GroupBy(s => s.total_pieces)
                        .ToDictionary(g => g.Key, g => g.Count())
                };

                return Ok(ApiResult.CreateSuccess("统计信息", stats));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计信息失败");
                return StatusCode(500, ApiResult.CreateError($"获取统计信息失败: {ex.Message}"));
            }
        }
    }
}
