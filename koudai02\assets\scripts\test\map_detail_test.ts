import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

import { HttpRequest } from '../tool/HttpRequest';
import { Global } from '../Global';

/**
 * 地图详情接口测试脚本
 * 用于验证GetMapDetail接口的对接功能
 */
@ccclass('MapDetailTest')
export class MapDetailTest extends Component {

    start() {
        console.log("🧪 地图详情接口测试开始");
        this.testMapDetailInterface();
    }

    /**
     * 测试地图详情接口
     */
    private async testMapDetailInterface() {
        console.log("📋 测试步骤:");
        console.log("1. 当前Global.currentMapId =", Global.currentMapId);
        console.log("2. 当前Global.userid =", Global.userid);
        
        try {
            // 测试获取地图详情接口
            console.log("3. 测试获取地图详情接口...");
            
            const requestData = {
                mapId: Global.currentMapId,
                userId: Global.userid
            };
            
            console.log("📤 发送请求数据:", requestData);
            
            const response = await HttpRequest.postConvertJson("Player/GetMapDetail", requestData);
            console.log("✅ 接口响应:", response);
            
            if (response && response.success) {
                console.log("🎉 接口调用成功！");
                
                // 输出地图基础信息
                if (response.mapInfo) {
                    console.log("📍 地图基础信息:");
                    console.log(`   地图名称: ${response.mapInfo.mapName}`);
                    console.log(`   地图描述: ${response.mapInfo.mapDesc}`);
                    console.log(`   🖼️ 图集名称: ${response.mapInfo.atlastName || '无'}`);
                    console.log(`   🌄 背景图片: ${response.mapInfo.background || '无'}`);
                    console.log(`   推荐等级: ${response.mapInfo.recommendLevel}`);
                    console.log(`   解锁状态: ${response.mapInfo.isUnlocked}`);
                }
                
                // 输出地图详细配置
                if (response.detailConfig) {
                    console.log("⚙️ 地图详细配置:");
                    console.log(`   限制等级: ${response.detailConfig.limitLevel}`);
                    console.log(`   限制成长: ${response.detailConfig.limitGrowth}`);
                    console.log(`   需要钥匙: ${response.detailConfig.requireKey}`);
                    console.log(`   金币奖励: ${response.detailConfig.minGold} - ${response.detailConfig.maxGold}`);
                    console.log(`   元宝奖励: ${response.detailConfig.minYuanbao} - ${response.detailConfig.maxYuanbao}`);
                }
                
                // 输出地图怪物信息
                if (response.monsters && response.monsters.length > 0) {
                    console.log(`👹 地图怪物 (${response.monsters.length}只):`);
                    response.monsters.forEach((monster: any, index: number) => {
                        console.log(`   ${index + 1}. ${monster.monsterName} (ID: ${monster.monsterId})`);
                        console.log(`      等级范围: ${monster.levelRange}`);
                        console.log(`      属性: ${monster.element}`);
                        console.log(`      经验奖励: ${monster.expReward}`);
                    });
                } else {
                    console.log("👹 该地图没有怪物配置");
                }
                
                // 输出掉落配置信息
                if (response.drops && response.drops.length > 0) {
                    console.log(`💎 掉落配置 (${response.drops.length}项):`);
                    response.drops.forEach((drop: any, index: number) => {
                        console.log(`   ${index + 1}. ${drop.itemName} (ID: ${drop.itemId})`);
                        console.log(`      掉落类型: ${drop.dropType}`);
                        console.log(`      掉落概率: ${(drop.dropRate * 100).toFixed(2)}%`);
                        console.log(`      数量范围: ${drop.countRange}`);
                    });
                } else {
                    console.log("💎 该地图没有掉落配置");
                }
                
                // 测试不同地图ID
                console.log("4. 测试其他地图ID...");
                await this.testDifferentMapIds();
                
            } else {
                console.error("❌ 接口调用失败:", response?.message || "未知错误");
                console.error("📥 完整响应:", response);
            }
            
        } catch (error) {
            console.error("💥 测试过程中发生错误:", error);
        }
        
        console.log("🧪 地图详情接口测试完成");
    }

    /**
     * 测试不同的地图ID
     */
    private async testDifferentMapIds(): Promise<void> {
        const testMapIds = [1, 2, 3, 100]; // 测试不同的地图ID
        
        for (const mapId of testMapIds) {
            try {
                console.log(`🔍 测试地图ID: ${mapId}`);
                
                const requestData = {
                    mapId: mapId,
                    userId: Global.userid
                };
                
                const response = await HttpRequest.postConvertJson("Player/GetMapDetail", requestData);
                
                if (response && response.success) {
                    const mapName = response.mapInfo?.mapName || "未知地图";
                    const monsterCount = response.monsters?.length || 0;
                    const dropCount = response.drops?.length || 0;
                    console.log(`✅ 地图${mapId}: ${mapName} - 怪物:${monsterCount}只, 掉落:${dropCount}项`);
                } else {
                    console.log(`❌ 地图${mapId}: ${response?.message || "获取失败"}`);
                }
                
                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 200));
                
            } catch (error) {
                console.error(`💥 测试地图${mapId}时发生错误:`, error);
            }
        }
    }

    /**
     * 手动测试指定地图ID（供控制台调用）
     */
    public async testMapId(mapId: number): Promise<void> {
        console.log(`🔍 手动测试地图ID: ${mapId}`);
        
        try {
            const requestData = {
                mapId: mapId,
                userId: Global.userid
            };
            
            const response = await HttpRequest.postConvertJson("Player/GetMapDetail", requestData);
            console.log(`📥 地图${mapId}响应:`, response);
            
        } catch (error) {
            console.error(`💥 测试地图${mapId}失败:`, error);
        }
    }

    /**
     * 获取当前地图的战斗推荐信息
     */
    public async getBattleRecommendation(): Promise<void> {
        console.log("⚔️ 获取当前地图战斗推荐信息");
        
        try {
            const requestData = {
                mapId: Global.currentMapId,
                userId: Global.userid
            };
            
            const response = await HttpRequest.postConvertJson("Player/GetMapDetail", requestData);
            
            if (response && response.success) {
                console.log("🎯 战斗推荐信息:");
                console.log(`📍 地图: ${response.mapInfo?.mapName} (推荐等级: ${response.mapInfo?.recommendLevel})`);
                
                if (response.detailConfig) {
                    console.log(`⚠️ 进入要求: 等级≥${response.detailConfig.limitLevel}, 成长≥${response.detailConfig.limitGrowth}`);
                    console.log(`🔑 需要钥匙: ${response.detailConfig.requireKey ? '是' : '否'}`);
                }
                
                if (response.monsters && response.monsters.length > 0) {
                    console.log("👹 可能遇到的怪物:");
                    response.monsters.forEach((monster: any) => {
                        console.log(`   ${monster.monsterName} (等级${monster.levelRange}, ${monster.element}属性, 经验${monster.expReward})`);
                    });
                }
                
                if (response.drops && response.drops.length > 0) {
                    console.log("💎 可能获得的奖励:");
                    response.drops.forEach((drop: any) => {
                        console.log(`   ${drop.itemName} (${(drop.dropRate * 100).toFixed(1)}%概率, 数量${drop.countRange})`);
                    });
                }
                
            } else {
                console.error("❌ 获取战斗推荐信息失败:", response?.message);
            }
            
        } catch (error) {
            console.error("💥 获取战斗推荐信息时发生错误:", error);
        }
    }
} 