import { _decorator, Component, Node, Prefab, instantiate } from 'cc';
const { ccclass, property } = _decorator;
import { ScrollViewItem } from './ScrollViewItem';
import { HttpRequest } from '../tool/HttpRequest';
import { Global } from '../Global';

//背包Content脚本
@ccclass('ScrollViewContent')
export class ScrollViewContent extends Component {

    @property(Prefab)
    public pk_Itme: Prefab;
     
   

    public propId: number = 0;//道具序号
    

    //道具总数
    public propCount: number = 300;
    //道具当前数量
    public propNum: number = 0;

    private selectScrollViewItem: ScrollViewItem = null;

    start() {
        this.loadBackpackItems();
    }

    /**
     * 加载背包道具列表 - 使用RESTful API
     */
    private async loadBackpackItems() {
        try {
            // 使用标准RESTful API获取背包道具 (position=1表示背包)
            const apiUrl = `prop/user/${Global.userid}/position/1`;

            const data = await HttpRequest.getConvertJson1(apiUrl, {});

            // 处理返回的数据
            let items: any[] = [];
            if (typeof data === 'string') {
                items = JSON.parse(data);
            } else if (Array.isArray(data)) {
                items = data;
            } else {
                console.error('Unexpected data format:', data);
                return;
            }

            this.propNum = items.length; // 初始化当前数量
            console.log(`加载背包道具成功，共${items.length}个道具`);

            // 清空现有道具显示
            this.node.removeAllChildren();

            // 渲染道具列表
            items.forEach((item: any) => {
                this.createItemNode(item);
            });

            // 不再需要延迟初始化点击事件，因为在createItemNode中直接绑定了

        } catch (error) {
            console.error('加载背包道具失败:', error);
            // 可以显示错误提示给用户
        }
    }

    /**
     * 创建道具节点
     */
    private createItemNode(item: any) {
        if (this.pk_Itme) {
            // 实例化预制体
            const itemNode = instantiate(this.pk_Itme);
            // 获取预制体上的组件
            const scrollViewItem = itemNode.getComponent(ScrollViewItem);

            if (scrollViewItem) {
                // 转换数据格式以适配前端组件
                const itemData = this.convertItemData(item);
                scrollViewItem.setData(itemData);

                // 直接绑定点击事件，使用统一的选中逻辑
                itemNode.on(Node.EventType.MOUSE_DOWN, () => {
                    this.onItemSelected(scrollViewItem, itemData);
                });
            }
            // 将实例化后的节点添加到当前节点（content）下面
            this.node.addChild(itemNode);
        }
    }

    /**
     * 转换后端数据格式为前端期望的格式
     */
    private convertItemData(backendItem: any): any {
        return {
            id: backendItem.itemSeq || backendItem.ItemSeq,           // 道具序号
            itemId: backendItem.itemId || backendItem.ItemId,         // 道具ID
            name: backendItem.itemName || backendItem.ItemName,       // 道具名称
            count: backendItem.itemCount || backendItem.ItemCount,    // 道具数量
            icon: backendItem.itemIcon || backendItem.ItemIcon,       // 道具图标
            price: backendItem.itemPrice || backendItem.ItemPrice,    // 道具价格
            position: backendItem.itemPos || backendItem.ItemPos,     // 道具位置
            description: backendItem.description || backendItem.Description // 道具描述
        };
    }

    /**
     * 初始化道具点击事件 - 已废弃，现在直接在createItemNode中绑定事件
     */
    // private initItemClickEvents() {
    //     setTimeout(() => {
    //         //给每个子节点添加点击事件
    //         this.node.children.forEach(child => {
    //             let MYscrollViewItem = child.getComponent(ScrollViewItem);
    //             if (MYscrollViewItem) {
    //                 child.on(Node.EventType.MOUSE_DOWN, () => this.onItemClicked(MYscrollViewItem), this)
    //             }
    //         });
    //     }, 1000);
    // }

    // 点击背包物品 - 已废弃，现在使用统一的onItemSelected方法
    // onItemClicked(item: ScrollViewItem) {
    //     if (this.selectScrollViewItem) {
    //         this.selectScrollViewItem.reductionItemColor();
    //     }

    //     item.changeColor();

    //     this.selectScrollViewItem = item;

    //     this.propId = parseInt(this.selectScrollViewItem.id.string);  //获取道具序号
    // }

    /**
     * 设置道具数量减一 - 使用道具后调用
     */
    public setPropNum() {
        if (this.selectScrollViewItem) {
            const num = this.selectScrollViewItem.setNumber();
            if (num < 1) {
                //如果返回使用的道具数量小于1说明有一个道具被用完了
                this.propNum = this.propNum - 1;
                // 移除已用完的道具节点
                this.selectScrollViewItem.node.removeFromParent();
                this.selectScrollViewItem = null;
            }
        }
    }

    /**
     * 刷新背包道具列表
     */
    public refreshBackpack() {

         this.loadBackpackItems();
          console.log("refreshBackpack触发刷新道具列表");
    }

    /**
     * 获取当前选中的道具信息
     */
    public getCurrentSelectedItem(): any {
        if (this.selectScrollViewItem) {
            // 直接从UI组件中获取显示的数据
            return {
                itemName: this.selectScrollViewItem.articlename?.string || "道具",
                itemId: this.selectScrollViewItem.id?.string || "",
                itemCount: parseInt(this.selectScrollViewItem.number?.string || "0")
            };
        }
        return null;
    }

    update(deltaTime: number) {
        // 可以在这里添加定时刷新逻辑
    }

    /**
     * 更新物品列表（通用方法，支持道具和装备）
     * @param items 物品数据数组
     */
    public updateItemList(items: any[]) {
        try {
            console.log("开始更新物品列表，数据:", items);

            // 清空现有的子节点
            this.node.removeAllChildren();

            // 重置选中状态
            this.selectScrollViewItem = null;
            this.propId = 0;
            this.propNum = items.length;

            // 为每个物品创建节点（使用预制体）
            items.forEach((item, index) => {
                this.createItemNodeFromPrefab(item, index);
            });

            console.log(`物品列表更新完成，共${items.length}个物品`);
        } catch (error) {
            console.error("更新物品列表失败:", error);
        }
    }

    /**
     * 使用预制体创建物品节点
     * @param itemData 物品数据
     * @param index 索引
     */
    private createItemNodeFromPrefab(itemData: any, index: number) {
        try {
            if (this.pk_Itme) {
                console.log(`创建物品节点[${index}]:`, {
                    itemName: itemData.itemName,
                    displayName: itemData.displayName,
                    itemCount: itemData.itemCount,
                    itemSeq: itemData.itemSeq,
                    完整数据: itemData
                });

                // 实例化预制体
                const itemNode = instantiate(this.pk_Itme);
                console.log(`预制体实例化成功，节点名称: ${itemNode.name}`);
                console.log(`预制体子节点:`, itemNode.children.map(child => child.name));

                // 获取预制体上的ScrollViewItem组件
                const scrollViewItem = itemNode.getComponent(ScrollViewItem);

                if (scrollViewItem) {
                    console.log(`找到ScrollViewItem组件，开始设置数据`);

                    // 设置物品数据
                    scrollViewItem.setData(itemData);

                    // 绑定点击事件
                    itemNode.on(Node.EventType.MOUSE_DOWN, () => {
                        this.onItemSelected(scrollViewItem, itemData);
                    });

                    console.log(`物品节点[${index}]数据设置完成`);
                } else {
                    console.error("预制体上没有找到ScrollViewItem组件");
                    console.log("预制体上的所有组件:", itemNode.components.map(comp => comp.constructor.name));
                }

                // 将实例化后的节点添加到当前节点（content）下面
                this.node.addChild(itemNode);

                console.log(`物品节点[${index}]创建成功并添加到容器`);
            } else {
                console.error("pk_Itme预制体未设置，无法创建物品节点");
            }
        } catch (error) {
            console.error("创建物品节点失败:", error, itemData);
        }
    }

    /**
     * 处理物品选中事件
     * @param scrollViewItem 选中的ScrollViewItem
     * @param itemData 物品数据
     */
    private onItemSelected(scrollViewItem: ScrollViewItem, itemData: any) {
        // 先恢复之前选中项的颜色
        if (this.selectScrollViewItem) {
            this.selectScrollViewItem.reductionItemColor();
        }

        // 设置新选中项的颜色
        scrollViewItem.changeColor();

        // 更新选中状态
        this.selectScrollViewItem = scrollViewItem;

        // 更新propId（对于装备，使用装备实例ID）
        this.propId = itemData.itemSeq || itemData.id || 0;

        console.log("选中物品:", {
            name: itemData.itemName || itemData.displayName,
            id: this.propId,
            type: itemData.equipTypeId ? "装备" : "道具",
            选中效果: "已应用"
        });
    }

}


