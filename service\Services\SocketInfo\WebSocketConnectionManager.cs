using System.Collections.Concurrent;
using System.Net.WebSockets;

namespace WebApplication_HM.Services.SocketInfo;

//管理 WebSocket 连接
public class WebSocketConnectionManager
{
    // 定义一个ConcurrentDictionary类型的私有字段，用于存储WebSocket连接
    private readonly ConcurrentDictionary<string, WebSocketConnection> _sockets = new();

    // 添加一个WebSocket连接
    public string AddSocket(System.Net.WebSockets.WebSocket socket)
    {
        // 生成一个唯一的连接ID
        var connectionId = Guid.NewGuid().ToString();
        // 将连接ID和WebSocket连接添加到字典中
        _sockets.TryAdd(connectionId, new WebSocketConnection 
        { 
            Socket = socket, 
            ConnectionId = connectionId,
            LastActiveTime = DateTime.Now,
            IsConnected = true
        });
        // 返回连接ID
        return connectionId;
    }

    // 根据id获取WebSocket连接
    public WebSocketConnection GetSocketById(string id)
    {
        if (id == null || id.Length == 0) return null;

        // 尝试从_sockets字典中获取id对应的WebSocket连接
        return _sockets.TryGetValue(id, out var connection) ? connection : null;
    }

    // 获取所有WebSocket连接
    public ConcurrentDictionary<string, WebSocketConnection> GetAll()
    {
        // 返回所有WebSocket连接的字典
        return _sockets;
    }

    // 从_sockets字典中移除指定id的socket
    public void RemoveSocket(string id)
    {
        // 尝试从_sockets字典中移除指定id的socket，如果移除成功，则将socket赋值给out参数
        _sockets.TryRemove(id, out _);
    }

    // 根据玩家ID获取所有连接
    public IEnumerable<WebSocketConnection> GetConnectionsByPlayerId(int playerId)
    {
        return _sockets.Values.Where(x => x.PlayerId == playerId && x.IsConnected);
    }

    // 绑定玩家信息到连接
    public async Task BindPlayerToConnection(string connectionId, int playerId, string playerName)
    {
        if (_sockets.TryGetValue(connectionId, out var connection))
        {
            // 获取该玩家的其他连接
            var existingConnections = GetConnectionsByPlayerId(playerId).ToList();
            
            // 如果有其他连接，发送踢出消息并关闭连接
            foreach (var existingConnection in existingConnections)
            {
                if (existingConnection.ConnectionId != connectionId)
                {
                    try
                    {
                        var buffer = System.Text.Encoding.UTF8.GetBytes("您的账号在其他地方登录，当前连接已断开");
                        await existingConnection.Socket.SendAsync(
                            new ArraySegment<byte>(buffer),
                            WebSocketMessageType.Text,
                            true,
                            CancellationToken.None
                        );
                        await existingConnection.Socket.CloseAsync(
                            WebSocketCloseStatus.NormalClosure,
                            "Account logged in elsewhere",
                            CancellationToken.None
                        );
                        existingConnection.IsConnected = false;
                    }
                    catch
                    {
                        // 忽略关闭连接时的异常
                    }
                    finally
                    {
                        RemoveSocket(existingConnection.ConnectionId);
                    }
                }
            }

            // 更新当前连接的玩家信息
            connection.PlayerId = playerId;
            connection.PlayerName = playerName;
            connection.LastActiveTime = DateTime.Now;
        }
    }

    // 更新连接的最后活动时间
    public void UpdateLastActiveTime(string connectionId)
    {
        if (_sockets.TryGetValue(connectionId, out var connection))
        {
            connection.LastActiveTime = DateTime.Now;
        }
    }

}

// 定义一个WebSocket连接类
public class WebSocketConnection
{
    // 定义一个WebSocket对象
    public System.Net.WebSockets.WebSocket Socket { get; set; }
    // 定义一个连接ID
    public string ConnectionId { get; set; }
    // 定义一个用户ID
    public int PlayerId { get; set; }
    // 定义一个用户名
    public string PlayerName { get; set; }
    // 定义最后活动时间
    public DateTime LastActiveTime { get; set; }
    // 定义连接状态
    public bool IsConnected { get; set; }
} 