<% if(polyfillsBundleFile) { %>
// Polyfills bundle.
require("<%= polyfillsBundleFile %>");
<% } %>

// SystemJS support.
window.self = window;
require("<%= systemJsBundleFile %>");

const importMapJson = jsb.fileUtils.getStringFromFile("<%= importMapFile%>");
const importMap = JSON.parse(importMapJson);
System.warmup({
    importMap,
    importMapUrl: '<%= importMapFile%>',
    defaultHandler: (urlNoSchema) => {
        require(urlNoSchema.startsWith('/') ? urlNoSchema.substr(1) : urlNoSchema);
    },
});

System.import('<%= applicationJs %>')
.then(({ Application }) => {
    return new Application();
}).then((application) => {
    return System.import('cc').then((cc) => {
        require('jsb-adapter/engine-adapter.js');
        return application.init(cc);
    }).then(() => {
        return application.start();
    });
}).catch((err) => {
    console.error(err.toString() + ', stack: ' + err.stack);
});
