# 宠物动态选择功能说明

## 功能概述

本功能实现了在战斗选择界面动态获取和选择携带宠物的完整流程，包括：

1. **接口对接**：调用 `Player/pets` 接口获取携带状态的宠物
2. **动态显示**：根据宠物ID加载对应的图集和SpriteFrame
3. **交互选择**：支持点击选择宠物，主战宠物有默认选中效果
4. **全局同步**：选中的宠物ID会同步到 `Global.currentPetId`
5. **战斗集成**：战斗系统自动使用选中的宠物ID

## 实现的文件

### 1. fightState.ts (宠物选择界面)

**新增功能**：
- 🔗 对接 `Player/pets` 接口获取携带宠物
- 🎨 根据宠物ID动态加载图集和头像
- 🖱️ 实现宠物点击选择逻辑
- 🌟 主战宠物默认选中效果
- 🔄 全局状态同步

**核心方法**：
```typescript
// 获取携带宠物列表
private async loadCarryPets(): Promise<void>

// 设置宠物显示
private async setupPetDisplay(): Promise<void>

// 宠物点击处理
private onPetClicked(clickedPet: Sprite, petIndex: number)

// 设置默认选中宠物
private setDefaultSelectedPet(mainPetId: number): void
```

### 2. fightMian.ts (战斗主界面)

**修改内容**：
- 🔧 将 `PetId` 改为 getter 属性，动态获取 `Global.currentPetId`
- 📊 添加当前出战宠物ID的日志输出

**关键修改**：
```typescript
// 之前：固定宠物ID
private PetId: number = 2;

// 现在：动态获取宠物ID
private get PetId(): number {
    return Global.currentPetId;
}
```

### 3. Global.ts (全局配置)

**已有配置**：
```typescript
public static currentPetId: number = 3; // 当前出战宠物ID
```

## 使用流程

### 1. 进入宠物选择界面

当进入 `fightState` 界面时，系统会自动：

1. 调用接口获取携带状态的宠物（最多3只）
2. 为每只宠物加载对应的图集和头像
3. 显示宠物在 `pet1`、`pet2`、`pet3` 节点上
4. 自动选中主战宠物（有发光和缩放效果）

### 2. 选择宠物

玩家可以：

- 🖱️ **点击宠物**：选择不同的宠物出战
- 🌟 **视觉反馈**：选中的宠物有发光和缩放动画
- 🔄 **状态同步**：选中后 `Global.currentPetId` 自动更新

### 3. 进入战斗

点击"开始战斗"按钮后：

- 🎯 战斗系统使用 `Global.currentPetId` 作为出战宠物
- 🎬 加载对应宠物的战斗动画和资源
- 📊 显示选中宠物的属性和信息

## 接口说明

### 请求接口

```javascript
// 请求携带的宠物
POST /api/Player/pets
{
    "userId": 1,
    "status": "携带"
}
```

### 响应格式

```javascript
{
    "success": true,
    "message": "获取成功",
    "pets": [
        {
            "id": 1,
            "petNo": 32,           // 宠物编号（用于加载资源）
            "name": "火波姆",
            "element": "火",
            "level": 15,
            "isMainPet": true,     // 是否为主战宠物
            "status": "携带"
        }
    ],
    "mainPetId": 1,               // 主战宠物ID
    "totalCount": 3,
    "carryCount": 3
}
```

## 资源加载机制

### 1. 图集分组

系统根据宠物ID自动分配图集：
- **starter** (1-10): `pets/starter_atlas`
- **common** (11-110): `pets/starter_atlas`
- **rare** (111-310): `pets/rare_atlas`
- **epic** (311-500): `pets/epic_atlas`

### 2. 头像加载

使用 `MassiveResourceManager.loadPetHeadType(petNo, 't')` 加载宠物头像：
- **t类型**：用于界面显示的头像
- **k类型**：备用头像格式
- **q类型**：特殊头像格式

## 错误处理

### 1. 接口调用失败

如果接口调用失败，系统会：
- 📝 记录错误日志
- 🔄 使用默认宠物数据作为备用方案
- 🎯 确保功能正常运行

### 2. 资源加载失败

如果宠物资源加载失败，系统会：
- ⚠️ 显示警告信息
- 🔒 隐藏对应的宠物节点
- 🎮 不影响其他宠物的正常显示

### 3. 备用方案

```typescript
// 默认宠物数据
{
    id: 1,
    petNo: Global.currentPetId,
    name: "默认宠物",
    element: "火",
    level: 1,
    isMainPet: true,
    status: "携带"
}
```

## 调试信息

系统提供详细的调试日志：

```
🐾 开始获取携带的宠物列表...
✅ 成功获取3只携带宠物
🎨 开始设置宠物显示...
✅ 宠物1设置完成: 火波姆 (ID: 32)
🌟 设置主战宠物为默认选中: 火波姆
🎯 选择宠物: 水精灵 (ID: 45)
✅ 已更新Global.currentPetId = 45
🎯 当前出战宠物ID: 45 (来自Global.currentPetId)
```

## 扩展功能

### 1. 手动选择宠物

```typescript
// 通过代码选择宠物
fightState.selectPet(0); // 选择第一只宠物
fightState.selectPet(1); // 选择第二只宠物
fightState.selectPet(2); // 选择第三只宠物
```

### 2. 获取当前选中宠物

```typescript
// 获取选中的宠物精灵
const selectedSprite = fightState.getCurrentSelectedPet();

// 获取选中的宠物信息
const selectedPetInfo = fightState.getCurrentSelectedPetInfo();
```

### 3. 刷新宠物列表

```typescript
// 重新获取宠物列表
await fightState.refreshPets();
```

## 注意事项

1. **宠物数量限制**：最多显示3只携带宠物
2. **网络依赖**：需要服务器接口支持，有备用方案
3. **资源管理**：使用LRU缓存优化资源加载
4. **状态同步**：确保选择状态在不同界面间正确传递
5. **用户体验**：提供视觉反馈和错误提示

## 测试方法

使用提供的测试脚本验证功能：

```typescript
// 运行测试
const testComponent = new PetSelectionTest();
testComponent.testPetSelection();
```

测试内容包括：
- ✅ 接口调用
- ✅ 数据解析
- ✅ 状态更新
- ✅ 错误处理

---

**开发完成时间**：2024年
**版本**：v1.0
**状态**：✅ 已完成并测试 