using WebApplication_HM.Models;
using WebApplication_HM.DTOs;
using SqlSugar;
using Newtonsoft.Json;

namespace WebApplication_HM.Services.PropScript
{
    /// <summary>
    /// 多脚本选择服务
    /// </summary>
    public class MultiScriptService
    {
        private readonly ISqlSugarClient _db;
        private readonly SimplePropScriptEngine _scriptEngine;
        private readonly ILogger<MultiScriptService> _logger;

        // 缓存用户的多脚本选择状态
        private static readonly Dictionary<int, MultiScriptState> _userScriptStates = new();

        public MultiScriptService(
            ISqlSugarClient db,
            SimplePropScriptEngine scriptEngine,
            ILogger<MultiScriptService> logger)
        {
            _db = db;
            _scriptEngine = scriptEngine;
            _logger = logger;
        }

        /// <summary>
        /// 处理多脚本选择
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="itemId">道具ID</param>
        /// <param name="script">脚本内容</param>
        /// <returns>选择选项</returns>
        public MultiScriptOptions ParseMultiScript(int userId, string itemId, string script)
        {
            try
            {
                if (!script.Contains("多脚本选择"))
                {
                    return null;
                }

                string[] split = script.Split('!');
                if (split.Length < 2)
                {
                    return null;
                }

                var scriptMapping = JsonConvert.DeserializeObject<Dictionary<string, string>>(split[1]);
                var options = scriptMapping.Keys.ToList();

                // 缓存用户的选择状态
                var state = new MultiScriptState
                {
                    UserId = userId,
                    ItemId = itemId,
                    ScriptMapping = scriptMapping,
                    CreateTime = DateTime.Now
                };

                _userScriptStates[userId] = state;

                return new MultiScriptOptions
                {
                    Options = options,
                    ScriptMapping = scriptMapping,
                    RequiresUserSelection = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析多脚本失败: UserId={userId}, Script={script}");
                return null;
            }
        }

        /// <summary>
        /// 执行用户选择的脚本
        /// </summary>
        /// <param name="request">多脚本选择请求</param>
        /// <returns>执行结果</returns>
        public PropScriptResult ExecuteSelectedScript(MultiScriptSelectRequest request)
        {
            try
            {
                // 检查用户是否有待选择的脚本
                if (!_userScriptStates.ContainsKey(request.UserId))
                {
                    return PropScriptResult.CreateFailure("没有找到待选择的脚本");
                }

                var state = _userScriptStates[request.UserId];

                // 验证道具ID是否匹配
                if (state.ItemId != request.ItemId)
                {
                    return PropScriptResult.CreateFailure("道具ID不匹配");
                }

                // 检查选择是否有效
                if (!state.ScriptMapping.ContainsKey(request.SelectedOption))
                {
                    return PropScriptResult.CreateFailure("无效的选择选项");
                }

                // 获取选择的脚本
                string selectedScript = state.ScriptMapping[request.SelectedOption];

                // 获取道具信息
                var prop = GetPropInfo(request.ItemId);
                if (prop == null)
                {
                    return PropScriptResult.CreateFailure("道具信息获取失败");
                }

                // 执行选择的脚本
                var result = _scriptEngine.ExecuteScript(selectedScript, prop, request.UserId);

                // 清除用户的选择状态
                _userScriptStates.Remove(request.UserId);

                // 记录选择日志
                LogScriptSelection(request.UserId, request.ItemId, request.SelectedOption, selectedScript, result.Success);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"执行选择脚本失败: {JsonConvert.SerializeObject(request)}");
                return PropScriptResult.CreateFailure("执行选择脚本失败");
            }
        }

        /// <summary>
        /// 获取用户当前的多脚本选择状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>多脚本状态</returns>
        public MultiScriptState GetUserScriptState(int userId)
        {
            _userScriptStates.TryGetValue(userId, out var state);
            return state;
        }

        /// <summary>
        /// 清除用户的多脚本选择状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        public void ClearUserScriptState(int userId)
        {
            _userScriptStates.Remove(userId);
        }

        /// <summary>
        /// 清理过期的脚本状态（超过5分钟）
        /// </summary>
        public void CleanupExpiredStates()
        {
            var expiredTime = DateTime.Now.AddMinutes(-5);
            var expiredUsers = _userScriptStates
                .Where(kv => kv.Value.CreateTime < expiredTime)
                .Select(kv => kv.Key)
                .ToList();

            foreach (var userId in expiredUsers)
            {
                _userScriptStates.Remove(userId);
            }

            if (expiredUsers.Count > 0)
            {
                _logger.LogInformation($"清理了{expiredUsers.Count}个过期的多脚本状态");
            }
        }

        /// <summary>
        /// 验证多脚本格式
        /// </summary>
        /// <param name="script">脚本内容</param>
        /// <returns>验证结果</returns>
        public bool ValidateMultiScriptFormat(string script)
        {
            try
            {
                if (!script.Contains("多脚本选择"))
                {
                    return false;
                }

                string[] split = script.Split('!');
                if (split.Length < 2)
                {
                    return false;
                }

                var scriptMapping = JsonConvert.DeserializeObject<Dictionary<string, string>>(split[1]);
                return scriptMapping != null && scriptMapping.Count > 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取道具信息
        /// </summary>
        private PropInfo GetPropInfo(string itemId)
        {
            try
            {
                var itemConfig = _db.Queryable<item_config>()
                    .Where(i => i.item_no.ToString() == itemId)
                    .First();

                if (itemConfig == null) return null;

                return new PropInfo
                {
                    ItemId = itemId,
                    ItemName = itemConfig.name ?? "",
                    ItemType = itemConfig.type ?? "",
                    Script = itemConfig.description ?? "" // 使用description字段作为脚本
                };
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 记录脚本选择日志
        /// </summary>
        private void LogScriptSelection(int userId, string itemId, string selectedOption, string script, bool success)
        {
            try
            {
                var log = new script_selection_log
                {
                    user_id = userId,
                    item_id = itemId,
                    selected_option = selectedOption,
                    executed_script = script,
                    success = success,
                    create_time = DateTime.Now
                };

                _db.Insertable(log).ExecuteCommand();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录脚本选择日志失败");
            }
        }
    }

    /// <summary>
    /// 多脚本选择选项
    /// </summary>
    public class MultiScriptOptions
    {
        /// <summary>
        /// 选择选项列表
        /// </summary>
        public List<string> Options { get; set; } = new();

        /// <summary>
        /// 脚本映射
        /// </summary>
        public Dictionary<string, string> ScriptMapping { get; set; } = new();

        /// <summary>
        /// 是否需要用户选择
        /// </summary>
        public bool RequiresUserSelection { get; set; }

        /// <summary>
        /// 选择提示信息
        /// </summary>
        public string SelectionPrompt { get; set; } = "请选择要执行的效果：";
    }

    /// <summary>
    /// 多脚本状态
    /// </summary>
    public class MultiScriptState
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 脚本映射
        /// </summary>
        public Dictionary<string, string> ScriptMapping { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 是否已过期（超过5分钟）
        /// </summary>
        public bool IsExpired => DateTime.Now.Subtract(CreateTime).TotalMinutes > 5;
    }

    /// <summary>
    /// 脚本选择日志模型
    /// </summary>
    public class script_selection_log
    {
        public int id { get; set; }
        public int user_id { get; set; }
        public string item_id { get; set; } = string.Empty;
        public string selected_option { get; set; } = string.Empty;
        public string executed_script { get; set; } = string.Empty;
        public bool success { get; set; }
        public DateTime create_time { get; set; }
    }
}
