﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///用户灵饰表
    ///</summary>
    [SugarTable("user_accessory")]
    public partial class user_accessory
    {
           public user_accessory(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:灵饰ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string accessory_id {get;set;}

           /// <summary>
           /// Desc:灵饰等级
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public int? accessory_level {get;set;}

           /// <summary>
           /// Desc:是否装备（0-未装备，1-已装备）
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_equipped {get;set;}

           /// <summary>
           /// Desc:获得时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
