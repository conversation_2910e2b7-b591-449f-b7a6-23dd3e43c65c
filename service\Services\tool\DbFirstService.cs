using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using WebApplication_HM.DTOs;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using Microsoft.Extensions.Logging;
using NetTaste;
using SqlSugar;
using WebApplication_HM.IServices;
using System.Reflection;

namespace WebApplication_HM.Services.tool
{
    /// <summary>
    /// 用户服务实现类
    /// </summary>
    public class DbFirstService : IDbFirstService
    {
        private readonly DbContext _dbContext;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DbFirstService> _logger;
        private readonly ISqlSugarClient _db;
        private readonly IWebHostEnvironment _environment;

        public DbFirstService(
            DbContext dbContext,
            IConfiguration configuration,
            ILogger<DbFirstService> logger,
            ISqlSugarClient db,
            IWebHostEnvironment environment)
        {
            _dbContext = dbContext;
            _configuration = configuration;
            _logger = logger;
            _db = db;
            _environment = environment;
        }

        /// <summary>
        /// 生成数据库实体
        /// </summary>
        /// <returns>执行结果和消息</returns>
        public async Task<(bool success, string message)> GenerateEntity()
        {
            try
            {
                // 设置实体生成的路径和命名空间
                string outputPath = Path.Combine(_environment.ContentRootPath, "Models");
                string nameSpace = "WebApplication_HM.Models";

                // 确保输出目录存在
                if (!Directory.Exists(outputPath))
                {
                    Directory.CreateDirectory(outputPath);
                }

                // 使用SqlSugar的DbFirst功能生成实体类
                _db.DbFirst
                    .IsCreateAttribute() // 生成特性
                    .StringNullable() // 字符串可空
                    .CreateClassFile(outputPath, nameSpace); // 生成实体类文件

                _logger.LogInformation($"实体类已生成到路径: {outputPath}");

                return (true, $"成功生成实体类，输出路径: {outputPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成实体时发生错误");
                return (false, $"生成实体失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据实体生成数据库表或字段
        /// </summary>
        /// <returns>执行结果和消息</returns>
        public async Task<(bool success, string message)> GenerateDatabase()
        {
            try
            {
                var results = new List<string>();
                var errors = new List<string>();

                // 获取所有实体类型
                var entityTypes = GetAllEntityTypes();

                foreach (var entityType in entityTypes)
                {
                    try
                    {
                        var (success, message) = await GenerateDatabaseByType(entityType);
                        if (success)
                        {
                            results.Add($"{entityType.Name}: {message}");
                        }
                        else
                        {
                            errors.Add($"{entityType.Name}: {message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"{entityType.Name}: {ex.Message}");
                        _logger.LogError(ex, $"处理实体 {entityType.Name} 时发生错误");
                    }
                }

                var successMessage = $"处理完成。成功: {results.Count}, 失败: {errors.Count}";
                if (results.Any())
                {
                    successMessage += $"\n成功项:\n{string.Join("\n", results)}";
                }
                if (errors.Any())
                {
                    successMessage += $"\n失败项:\n{string.Join("\n", errors)}";
                }

                _logger.LogInformation(successMessage);
                return (errors.Count == 0, successMessage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成数据库结构时发生错误");
                return (false, $"生成数据库结构失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 根据指定实体类型生成数据库表或字段
        /// </summary>
        /// <param name="entityType">实体类型</param>
        /// <returns>执行结果和消息</returns>
        public async Task<(bool success, string message)> GenerateDatabaseByType(Type entityType)
        {
            try
            {
                var tableName = GetTableName(entityType);
                var isTableExists = _db.DbMaintenance.IsAnyTable(tableName);

                if (!isTableExists)
                {
                    // 表不存在，创建整个表
                    _db.CodeFirst.InitTables(entityType);
                    _logger.LogInformation($"已创建表: {tableName}");
                    return (true, $"已创建表: {tableName}");
                }
                else
                {
                    // 表存在，检查并添加缺失的字段
                    var addedColumns = await AddMissingColumns(entityType, tableName);
                    if (addedColumns.Any())
                    {
                        var message = $"已为表 {tableName} 添加字段: {string.Join(", ", addedColumns)}";
                        _logger.LogInformation(message);
                        return (true, message);
                    }
                    else
                    {
                        var message = $"表 {tableName} 结构已是最新";
                        return (true, message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理实体 {entityType.Name} 时发生错误");
                return (false, $"处理失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 同步指定实体的数据库结构
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>执行结果和消息</returns>
        public async Task<(bool success, string message)> SyncEntityStructure<T>() where T : class, new()
        {
            return await GenerateDatabaseByType(typeof(T));
        }

        #region 私有方法

        /// <summary>
        /// 获取所有实体类型
        /// </summary>
        /// <returns>实体类型列表</returns>
        private List<Type> GetAllEntityTypes()
        {
            var entityTypes = new List<Type>();

            try
            {
                // 获取Models命名空间下的所有类型
                var assembly = Assembly.GetExecutingAssembly();
                var types = assembly.GetTypes()
                    .Where(t => t.Namespace == "WebApplication_HM.Models" &&
                               t.IsClass &&
                               !t.IsAbstract &&
                               !t.Name.Contains("DTO") &&
                               !t.Name.Contains("Config") &&
                               HasSugarTableAttribute(t))
                    .ToList();

                entityTypes.AddRange(types);

                _logger.LogInformation($"找到 {entityTypes.Count} 个实体类型");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取实体类型时发生错误");
            }

            return entityTypes;
        }

        /// <summary>
        /// 检查类型是否有SugarTable特性
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否有SugarTable特性</returns>
        private bool HasSugarTableAttribute(Type type)
        {
            return type.GetCustomAttribute<SugarTable>() != null;
        }

        /// <summary>
        /// 获取表名
        /// </summary>
        /// <param name="entityType">实体类型</param>
        /// <returns>表名</returns>
        private string GetTableName(Type entityType)
        {
            var sugarTableAttr = entityType.GetCustomAttribute<SugarTable>();
            return sugarTableAttr?.TableName ?? entityType.Name.ToLower();
        }

        /// <summary>
        /// 添加缺失的字段
        /// </summary>
        /// <param name="entityType">实体类型</param>
        /// <param name="tableName">表名</param>
        /// <returns>添加的字段列表</returns>
        private async Task<List<string>> AddMissingColumns(Type entityType, string tableName)
        {
            var addedColumns = new List<string>();

            try
            {
                // 获取数据库中现有的字段
                var existingColumns = _db.DbMaintenance.GetColumnInfosByTableName(tableName);
                var existingColumnNames = existingColumns.Select(c => c.DbColumnName.ToLower()).ToHashSet();

                _logger.LogInformation($"表 {tableName} 现有字段: {string.Join(", ", existingColumnNames)}");

                // 获取实体中定义的所有属性
                var properties = entityType.GetProperties()
                    .Where(p => !IsIgnoredProperty(p))
                    .ToList();

                _logger.LogInformation($"实体 {entityType.Name} 属性数量: {properties.Count}");

                foreach (var property in properties)
                {
                    var columnName = GetColumnName(property);

                    _logger.LogInformation($"检查属性 {property.Name} -> 字段 {columnName}");

                    if (!existingColumnNames.Contains(columnName.ToLower()))
                    {
                        // 字段不存在，需要添加
                        _logger.LogInformation($"字段 {columnName} 不存在，准备添加");
                        await AddColumn(tableName, property);
                        addedColumns.Add(columnName);
                        _logger.LogInformation($"已为表 {tableName} 添加字段: {columnName}");
                    }
                    else
                    {
                        _logger.LogInformation($"字段 {columnName} 已存在");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"添加缺失字段时发生错误: {tableName}");
                throw;
            }

            return addedColumns;
        }

        /// <summary>
        /// 检查属性是否应该被忽略
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>是否忽略</returns>
        private bool IsIgnoredProperty(PropertyInfo property)
        {
            // 检查是否有SugarColumn特性且标记为忽略
            var sugarColumnAttr = property.GetCustomAttribute<SugarColumn>();
            if (sugarColumnAttr != null && sugarColumnAttr.IsIgnore)
            {
                return true;
            }

            // 检查是否有Navigate特性（导航属性）
            var navigateAttr = property.GetCustomAttribute<Navigate>();
            if (navigateAttr != null)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取字段名
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>字段名</returns>
        private string GetColumnName(PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SugarColumn>();
            return sugarColumnAttr?.ColumnName ?? property.Name.ToLower();
        }

        /// <summary>
        /// 添加字段到表
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <param name="property">属性信息</param>
        private async Task AddColumn(string tableName, PropertyInfo property)
        {
            try
            {
                var columnName = GetColumnName(property);
                var columnType = GetSqlType(property);
                var isNullable = IsNullableProperty(property);
                var defaultValue = GetDefaultValue(property);

                var sql = $"ALTER TABLE `{tableName}` ADD COLUMN `{columnName}` {columnType}";

                if (!isNullable)
                {
                    sql += " NOT NULL";
                }

                if (!string.IsNullOrEmpty(defaultValue))
                {
                    sql += $" DEFAULT {defaultValue}";
                }

                await _db.Ado.ExecuteCommandAsync(sql);
                _logger.LogInformation($"执行SQL: {sql}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"添加字段失败: {tableName}.{property.Name}");
                throw;
            }
        }

        /// <summary>
        /// 获取SQL数据类型
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>SQL数据类型</returns>
        private string GetSqlType(PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SugarColumn>();
            if (sugarColumnAttr != null && !string.IsNullOrEmpty(sugarColumnAttr.ColumnDataType))
            {
                return sugarColumnAttr.ColumnDataType;
            }

            var propertyType = property.PropertyType;
            var underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

            return underlyingType.Name.ToLower() switch
            {
                "int32" => "int(11)",
                "int64" => "bigint(20)",
                "decimal" => "decimal(20,6)",
                "double" => "double",
                "float" => "float",
                "boolean" => "tinyint(1)",
                "datetime" => "datetime",
                "string" => "varchar(255)",
                _ => "varchar(255)"
            };
        }

        /// <summary>
        /// 检查属性是否可空
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>是否可空</returns>
        private bool IsNullableProperty(PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SugarColumn>();
            if (sugarColumnAttr != null)
            {
                return sugarColumnAttr.IsNullable;
            }

            return Nullable.GetUnderlyingType(property.PropertyType) != null ||
                   property.PropertyType == typeof(string);
        }

        /// <summary>
        /// 获取默认值
        /// </summary>
        /// <param name="property">属性信息</param>
        /// <returns>默认值</returns>
        private string GetDefaultValue(PropertyInfo property)
        {
            var sugarColumnAttr = property.GetCustomAttribute<SugarColumn>();
            if (sugarColumnAttr != null && !string.IsNullOrEmpty(sugarColumnAttr.DefaultValue))
            {
                return sugarColumnAttr.DefaultValue;
            }

            var propertyType = property.PropertyType;
            var underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

            // 对于非空类型，提供默认值
            if (Nullable.GetUnderlyingType(propertyType) == null && propertyType != typeof(string))
            {
                return underlyingType.Name.ToLower() switch
                {
                    "int32" => "0",
                    "int64" => "0",
                    "decimal" => "0.000000",
                    "double" => "0",
                    "float" => "0",
                    "boolean" => "0",
                    "datetime" => "CURRENT_TIMESTAMP",
                    _ => null
                };
            }

            return null;
        }

        #endregion
    }
}