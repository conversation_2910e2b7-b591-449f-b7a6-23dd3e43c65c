﻿namespace WebApplication_HM.DTOs.RequestDTO
{
    public class ChatMessageDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 玩家Id
        /// </summary>
        public int playerId { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string number { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string password { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; }
    }
}
