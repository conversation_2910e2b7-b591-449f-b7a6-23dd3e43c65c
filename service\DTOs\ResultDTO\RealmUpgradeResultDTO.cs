namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 境界提升结果DTO
    /// </summary>
    public class RealmUpgradeResultDTO
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 返回消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 新的境界名称
        /// </summary>
        public string NewRealmName { get; set; } = string.Empty;

        /// <summary>
        /// 新的境界等级
        /// </summary>
        public int? NewRealmLevel { get; set; }

        /// <summary>
        /// 新的境界ID
        /// </summary>
        public int NewRealmId { get; set; }

        /// <summary>
        /// 属性提升信息
        /// </summary>
        public string AttributeBonus { get; set; } = string.Empty;

        /// <summary>
        /// 消耗的资源信息
        /// </summary>
        public string CostInfo { get; set; } = string.Empty;

        /// <summary>
        /// 升级前境界名称
        /// </summary>
        public string? OldRealmName { get; set; }

        /// <summary>
        /// 升级前境界等级
        /// </summary>
        public int? OldRealmLevel { get; set; }
    }
} 