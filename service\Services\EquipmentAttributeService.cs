using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 装备属性计算服务实现
    /// </summary>
    public class EquipmentAttributeService : IEquipmentAttributeService
    {
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly IGemstoneService _gemstoneService;
        private readonly ISuitService _suitService;
        private readonly DbContext _dbContext;
        private readonly ILogger<EquipmentAttributeService> _logger;

        // 五行相生关系（用于属性加成计算）
        private readonly Dictionary<string, string> _elementGeneration = new()
        {
            {"生命", "金"}, {"魔法", "木"}, {"攻击", "火"}, {"防御", "土"},
            {"命中", "雷"}, {"闪避", "水"}, {"速度", "风"}
        };

        // 五行相克关系
        private readonly Dictionary<string, string> _elementRestraint = new()
        {
            {"生命", "火"}, {"魔法", "金"}, {"攻击", "水"}, {"防御", "木"},
            {"命中", "风"}, {"闪避", "土"}, {"速度", "雷"}
        };

        public EquipmentAttributeService(
            IEquipmentRepository equipmentRepository,
            IGemstoneService gemstoneService,
            ISuitService suitService,
            DbContext dbContext,
            ILogger<EquipmentAttributeService> logger)
        {
            _equipmentRepository = equipmentRepository;
            _gemstoneService = gemstoneService;
            _suitService = suitService;
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 计算宠物的装备属性加成（兼容现有战斗系统）
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>装备属性加成字典</returns>
        public async Task<Dictionary<string, double>> CalculateEquipmentAttributesAsync(int petId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"命中", 0}, {"防御", 0}, {"速度", 0},
                {"闪避", 0}, {"生命", 0}, {"魔法", 0}, {"加深", 0},
                {"抵消", 0}, {"吸血", 0}, {"吸魔", 0}
            };

            try
            {
                // 1. 获取宠物装备
                var equipments = await _dbContext.Db.Queryable<user_equipment>()
                    .Where(x => x.pet_id == petId && x.is_equipped == true)
                    .ToListAsync();

                // 2. 计算每件装备的属性加成
                foreach (var equipment in equipments)
                {
                    var equipAttr = await CalculateEquipmentAttributeAsync(equipment.id);
                    foreach (var attr in equipAttr)
                    {
                        if (result.ContainsKey(attr.Key))
                            result[attr.Key] += attr.Value;
                    }
                }

                // 3. 计算套装属性加成
                var suitAttributes = await _suitService.CalculateSuitAttributesAsync(petId);
                foreach (var attr in suitAttributes)
                {
                    if (result.ContainsKey(attr.Key))
                        result[attr.Key] += attr.Value;
                }

                // 4. 计算宝石属性加成
                var gemstoneAttributes = await CalculateGemstoneAttributesAsync(petId);
                foreach (var attr in gemstoneAttributes)
                {
                    if (result.ContainsKey(attr.Key))
                        result[attr.Key] += attr.Value;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算装备属性失败，宠物ID: {PetId}", petId);
                throw;
            }
        }

        /// <summary>
        /// 计算单件装备的属性加成
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns>属性加成字典</returns>
        public async Task<Dictionary<string, double>> CalculateEquipmentAttributeAsync(int userEquipmentId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"命中", 0}, {"防御", 0}, {"速度", 0},
                {"闪避", 0}, {"生命", 0}, {"魔法", 0}, {"加深", 0},
                {"抵消", 0}, {"吸血", 0}, {"吸魔", 0}
            };

            try
            {
                // 1. 获取装备信息
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null) return result;

                // 2. 获取装备详情
                var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                if (equipDetail == null) return result;

                // 3. 计算装备基础属性
                var baseAttributes = new Dictionary<string, double>
                {
                    {"攻击", (double)(equipDetail.atk ?? 0)},
                    {"命中", (double)(equipDetail.hit ?? 0)},
                    {"防御", (double)(equipDetail.def ?? 0)},
                    {"速度", (double)(equipDetail.spd ?? 0)},
                    {"闪避", (double)(equipDetail.dodge ?? 0)},
                    {"生命", (double)(equipDetail.hp ?? 0)},
                    {"魔法", (double)(equipDetail.mp ?? 0)},
                    {"加深", (double)(equipDetail.deepen ?? 0)},
                    {"抵消", (double)(equipDetail.offset ?? 0)},
                    {"吸血", (double)(equipDetail.vamp ?? 0)},
                    {"吸魔", (double)(equipDetail.vamp_mp ?? 0)}
                };

                // 4. 应用强化和五行加成
                string mainAttr = equipDetail.main_attr ?? "攻击";
                int strengthenLevel = equipment.strengthen_level ?? 0;
                string equipElement = equipment.element ?? "无";

                foreach (var attr in baseAttributes)
                {
                    if (attr.Value == 0) continue;

                    bool isMainAttribute = attr.Key == mainAttr;
                    int elementBonus = CalculateElementBonus(attr.Key, equipElement);
                    
                    result[attr.Key] = CalculateEquipmentValue(attr.Value.ToString(), 
                        strengthenLevel, elementBonus, isMainAttribute);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算装备属性失败，装备ID: {EquipmentId}", userEquipmentId);
                throw;
            }
        }

        /// <summary>
        /// 计算装备属性值（基于原系统公式）
        /// </summary>
        /// <param name="baseValue">基础值</param>
        /// <param name="strengthenLevel">强化等级</param>
        /// <param name="elementBonus">五行加成类型</param>
        /// <param name="isMainAttribute">是否主属性</param>
        /// <returns>最终属性值</returns>
        public double CalculateEquipmentValue(string baseValue, int strengthenLevel, 
            int elementBonus, bool isMainAttribute)
        {
            if (string.IsNullOrEmpty(baseValue) || baseValue == "0") return 0;

            double value = Convert.ToDouble(baseValue);

            // 只有主属性才受强化等级和五行影响
            if (!isMainAttribute) return value;

            // 根据五行加成类型计算
            if (baseValue.Contains(".")) // 百分比型
            {
                return CalculateEquipmentPercentage(value, strengthenLevel, elementBonus);
            }
            else // 数值型
            {
                return CalculateEquipmentNumber(value, strengthenLevel, elementBonus);
            }
        }

        /// <summary>
        /// 获取详细的属性计算结果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>详细属性计算结果</returns>
        public async Task<AttributeCalculationResult> GetDetailedAttributeCalculationAsync(int petId)
        {
            var result = new AttributeCalculationResult();

            try
            {
                // 1. 获取基础属性（从现有系统）
                var pet = await _dbContext.Db.Queryable<user_pet>()
                    .Where(x => x.id == petId)
                    .FirstAsync();

                if (pet != null)
                {
                    result.BaseAttributes = new Dictionary<string, double>
                    {
                        {"攻击", (double)pet.atk}, {"命中", (double)pet.hit},
                        {"防御", (double)pet.def}, {"速度", (double)pet.spd},
                        {"闪避", (double)pet.dodge}, {"生命", (double)pet.hp},
                        {"魔法", (double)pet.mp}
                    };
                }

                // 2. 计算装备属性
                result.EquipmentAttributes = await CalculateEquipmentAttributesAsync(petId);

                // 3. 计算套装属性
                result.SuitAttributes = await _suitService.CalculateSuitAttributesAsync(petId);

                // 4. 计算宝石属性
                result.GemstoneAttributes = await CalculateGemstoneAttributesAsync(petId);

                // 5. 计算总属性
                result.TotalAttributes = new Dictionary<string, double>();
                var allKeys = result.BaseAttributes.Keys
                    .Union(result.EquipmentAttributes.Keys)
                    .Union(result.SuitAttributes.Keys)
                    .Union(result.GemstoneAttributes.Keys);

                foreach (var key in allKeys)
                {
                    result.TotalAttributes[key] = 
                        result.BaseAttributes.GetValueOrDefault(key, 0) +
                        result.EquipmentAttributes.GetValueOrDefault(key, 0) +
                        result.SuitAttributes.GetValueOrDefault(key, 0) +
                        result.GemstoneAttributes.GetValueOrDefault(key, 0);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取详细属性计算失败，宠物ID: {PetId}", petId);
                throw;
            }
        }

        #region 私有辅助方法

        /// <summary>
        /// 计算宝石属性加成
        /// </summary>
        private async Task<Dictionary<string, double>> CalculateGemstoneAttributesAsync(int petId)
        {
            var result = new Dictionary<string, double>
            {
                {"攻击", 0}, {"命中", 0}, {"防御", 0}, {"速度", 0},
                {"闪避", 0}, {"生命", 0}, {"魔法", 0}
            };

            try
            {
                var equipments = await _dbContext.Db.Queryable<user_equipment>()
                    .Where(x => x.pet_id == petId && x.is_equipped == true)
                    .ToListAsync();

                foreach (var equipment in equipments)
                {
                    var gemstones = await _gemstoneService.GetEquipmentGemstonesAsync(equipment.id);
                    foreach (var gemstone in gemstones)
                    {
                        if (result.ContainsKey(gemstone.UpType))
                        {
                            result[gemstone.UpType] += (double)gemstone.UpNum;
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算宝石属性失败，宠物ID: {PetId}", petId);
                return result;
            }
        }

        /// <summary>
        /// 计算五行加成类型
        /// </summary>
        private int CalculateElementBonus(string attributeType, string equipElement)
        {
            if (string.IsNullOrEmpty(equipElement) || equipElement == "无") return 0;

            // 相生：+1，相克：-1，普通：0
            if (_elementGeneration.ContainsKey(attributeType) && 
                _elementGeneration[attributeType] == equipElement)
                return 1; // 相生

            if (_elementRestraint.ContainsKey(attributeType) && 
                _elementRestraint[attributeType] == equipElement)
                return -1; // 相克

            return 0; // 普通
        }

        /// <summary>
        /// 计算数值型装备属性（基于原系统CalcEquipment_L）
        /// </summary>
        private double CalculateEquipmentNumber(double baseValue, int strengthenLevel, int elementBonus)
        {
            double multiplier;

            // 根据五行加成类型确定倍率
            switch (elementBonus)
            {
                case 1: // 相生
                    multiplier = strengthenLevel <= 10 ? 
                        (1.15 + 0.05 * strengthenLevel) : 
                        (0.65 + 0.1 * strengthenLevel);
                    break;
                case -1: // 相克
                    multiplier = strengthenLevel <= 10 ? 
                        (0.85 + 0.05 * strengthenLevel) : 
                        (0.35 + 0.1 * strengthenLevel);
                    break;
                default: // 普通
                    multiplier = strengthenLevel <= 10 ? 
                        (1.0 + 0.05 * strengthenLevel) : 
                        (0.5 + 0.1 * strengthenLevel);
                    break;
            }

            return baseValue * multiplier;
        }

        /// <summary>
        /// 计算百分比型装备属性（基于原系统CalcEquipment_D）
        /// </summary>
        private double CalculateEquipmentPercentage(double baseValue, int strengthenLevel, int elementBonus)
        {
            double bonus;

            // 根据五行加成类型确定加成
            switch (elementBonus)
            {
                case 1: // 相生
                    bonus = strengthenLevel <= 10 ? 
                        (0.01 * strengthenLevel + 0.03) : 
                        (0.02 * strengthenLevel - 0.07);
                    break;
                case -1: // 相克
                    bonus = strengthenLevel <= 10 ? 
                        (0.01 * strengthenLevel - 0.03) : 
                        (0.02 * strengthenLevel - 0.13);
                    break;
                default: // 普通
                    bonus = strengthenLevel <= 10 ? 
                        (0.01 * strengthenLevel) : 
                        (0.02 * strengthenLevel - 0.1);
                    break;
            }

            return baseValue + bonus;
        }

        #endregion
    }
}
