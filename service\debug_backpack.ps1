# Debug backpack API
$baseUrl = "http://localhost:5000/api"

Write-Host "=== Debugging Backpack API ==="

# Test different API endpoints
Write-Host "1. Testing /api/prop/user/1 (all items):"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/user/1" -Method GET
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Content: $($response.Content)"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}

Write-Host "`n2. Testing /api/prop/user/1/position/1 (backpack items):"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/user/1/position/1" -Method GET
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Content: $($response.Content)"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}

Write-Host "`n3. Testing /api/prop/user/1/position/2 (warehouse items):"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/user/1/position/2" -Method GET
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Content: $($response.Content)"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}

Write-Host "`n4. Adding another item with ID 2:"
$body = @{
    UserId = 1
    ItemId = "2"
    Count = 5
    Position = 1
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/add" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Add Status: $($response.StatusCode)"
    Write-Host "Add Response: $($response.Content)"
}
catch {
    Write-Host "Add Error: $($_.Exception.Message)"
}

Write-Host "`n5. Checking backpack again:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/user/1/position/1" -Method GET
    Write-Host "Status: $($response.StatusCode)"
    Write-Host "Content: $($response.Content)"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}
