<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>10-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{792,1254},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>11-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{1,1},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>12-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{791,1},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>15-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{1,314},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>16-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{791,314},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>18-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{1581,1},{788,311}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>21-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{1,627},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>22-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{788,311}</string>
                <key>spriteSourceSize</key>
                <string>{788,311}</string>
                <key>textureRect</key>
                <string>{{791,627},{788,311}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{1,940},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{792,940},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>9-0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{1,1254},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>map_atlas-2.png</string>
            <key>size</key>
            <string>{1893,1567}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:d4017e87b3a68bcffc1d90d3b6ee1833:07fbf581f2476ddf0f3607bf00146ce6:75ae273111d95dc9dde1c904a9ab475b$</string>
            <key>textureFileName</key>
            <string>map_atlas-2.png</string>
        </dict>
    </dict>
</plist>
