# PowerShell脚本：修复字符串错误
# 修复被截断的字符串和ApiResult调用

$files = @(
    "Controllers/EquipmentController.cs",
    "Controllers/EquipmentTestController.cs", 
    "Controllers/EquipmentAttributeController.cs"
)

foreach ($file in $files) {
    $filePath = Join-Path $PSScriptRoot "..\$file"
    if (Test-Path $filePath) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $filePath -Raw
        
        # 修复被截断的字符串和ApiResult调用
        $content = $content -replace 'ApiResult<([^>]+)>\.Success\(', 'ApiResult<$1>.CreateSuccess('
        $content = $content -replace 'ApiResult<([^>]+)>\.Error\(', 'ApiResult<$1>.CreateError('
        $content = $content -replace 'ApiResult\.Success\(', 'ApiResult.CreateSuccess('
        $content = $content -replace 'ApiResult\.Error\(', 'ApiResult.CreateError('
        
        # 修复被截断的中文字符串
        $content = $content -replace '"获取装备属性失�?\)', '"获取装备属性失败")'
        $content = $content -replace '"获取详细属性失�?\)', '"获取详细属性失败")'
        $content = $content -replace '"获取增强属性失�?\)', '"获取增强属性失败")'
        $content = $content -replace '"增强战斗计算失�?\)', '"增强战斗计算失败")'
        $content = $content -replace '"计算失�?\)', '"计算失败")'
        $content = $content -replace '"属性对比失�?\)', '"属性对比失败")'
        $content = $content -replace '"装备强化失�?\)', '"装备强化失败")'
        $content = $content -replace '"宝石镶嵌失�?\)', '"宝石镶嵌失败")'
        $content = $content -replace '"五行点化失�?\)', '"五行点化失败")'
        $content = $content -replace '"装备分解失�?\)', '"装备分解失败")'
        $content = $content -replace '"操作失�?\)', '"操作失败")'
        $content = $content -replace '"测试失�?\)', '"测试失败")'
        $content = $content -replace '"添加装备失�?\)', '"添加装备失败")'
        $content = $content -replace '"删除装备失�?\)', '"删除装备失败")'
        $content = $content -replace '"装备穿戴失�?\)', '"装备穿戴失败")'
        $content = $content -replace '"卸下装备失�?\)', '"卸下装备失败")'
        
        # 修复其他可能的字符串问题
        $content = $content -replace '失�?\)', '失败")'
        $content = $content -replace '成功�?\)', '成功")'
        
        Set-Content $filePath $content -NoNewline
        Write-Host "Completed $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "All files processed!"
