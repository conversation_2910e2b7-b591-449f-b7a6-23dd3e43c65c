# PowerShell脚本：修复所有字符串错误
# 修复被截断的中文字符串

$files = @(
    "Controllers/EquipmentController.cs",
    "Controllers/EquipmentTestController.cs", 
    "Controllers/EquipmentAttributeController.cs"
)

foreach ($file in $files) {
    $filePath = Join-Path $PSScriptRoot "..\$file"
    if (Test-Path $filePath) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $filePath -Raw
        
        # 修复所有被截断的中文字符串
        $content = $content -replace '"[^"]*失�?\)', '"操作失败")'
        $content = $content -replace '"[^"]*成功�?\)', '"操作成功")'
        $content = $content -replace '"[^"]*不存�?\)', '"不存在")'
        $content = $content -replace '"[^"]*失败�?\)', '"失败")'
        
        # 修复具体的错误消息
        $content = $content -replace '"获取装备属性失�?\)', '"获取装备属性失败")'
        $content = $content -replace '"获取详细属性失�?\)', '"获取详细属性失败")'
        $content = $content -replace '"获取增强属性失�?\)', '"获取增强属性失败")'
        $content = $content -replace '"增强战斗计算失�?\)', '"增强战斗计算失败")'
        $content = $content -replace '"计算装备属性值失�?\)', '"计算装备属性值失败")'
        $content = $content -replace '"属性对比失�?\)', '"属性对比失败")'
        $content = $content -replace '"装备强化失�?\)', '"装备强化失败")'
        $content = $content -replace '"宝石镶嵌失�?\)', '"宝石镶嵌失败")'
        $content = $content -replace '"五行点化失�?\)', '"五行点化失败")'
        $content = $content -replace '"装备分解失�?\)', '"装备分解失败")'
        $content = $content -replace '"测试失�?\)', '"测试失败")'
        $content = $content -replace '"添加装备失�?\)', '"添加装备失败")'
        $content = $content -replace '"删除装备失�?\)', '"删除装备失败")'
        $content = $content -replace '"装备穿戴失�?\)', '"装备穿戴失败")'
        $content = $content -replace '"卸下装备失�?\)', '"卸下装备失败")'
        $content = $content -replace '"装备不存�?\)', '"装备不存在")'
        $content = $content -replace '"计算失�?\)', '"计算失败")'
        
        # 修复日志消息
        $content = $content -replace '"获取增强属性失�?\);', '"获取增强属性失败");'
        $content = $content -replace '"计算装备属性值失�?\);', '"计算装备属性值失败");'
        $content = $content -replace '"属性对比失�?\);', '"属性对比失败");'
        
        # 修复注释中的中文
        $content = $content -replace '// 先清理宝�?', '// 先清理宝石'
        $content = $content -replace '// 再删除装�?', '// 再删除装备'
        $content = $content -replace '"清理完成，删除了 \{deletedCount\} 件测试装�?\)', '"清理完成，删除了 {deletedCount} 件测试装备")'
        
        Set-Content $filePath $content -NoNewline -Encoding UTF8
        Write-Host "Completed $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "All files processed!"
