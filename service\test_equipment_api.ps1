# Test equipment API
$baseUrl = "http://localhost:5000/api"

Write-Host "=== 测试装备API接口 ==="

# 1. 测试获取用户装备列表
Write-Host "`n1. 测试获取用户装备列表:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/equipment/user/1" -Method GET
    Write-Host "✅ 获取装备列表成功: $($response.StatusCode)"
    $equipments = $response.Content | ConvertFrom-Json
    Write-Host "装备数据结构:"
    Write-Host $response.Content
    
    if ($equipments.data -and $equipments.data.Count -gt 0) {
        Write-Host "装备数量: $($equipments.data.Count)"
        $firstEquip = $equipments.data[0]
        Write-Host "第一个装备:"
        Write-Host "   - ID: $($firstEquip.id)"
        Write-Host "   - 装备ID: $($firstEquip.equipId)"
        Write-Host "   - 装备名称: $($firstEquip.equipName)"
        Write-Host "   - 强化等级: $($firstEquip.strengthenLevel)"
        Write-Host "   - 是否已装备: $($firstEquip.isEquipped)"
        Write-Host "   - 五行属性: $($firstEquip.element)"
    } else {
        Write-Host "用户暂无装备"
    }
}
catch {
    Write-Host "❌ 获取装备列表失败: $($_.Exception.Message)"
}

# 2. 测试获取未使用装备
Write-Host "`n2. 测试获取未使用装备:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/equipment/user/1/unused" -Method GET
    Write-Host "✅ 获取未使用装备成功: $($response.StatusCode)"
    $unusedEquipments = $response.Content | ConvertFrom-Json
    
    if ($unusedEquipments.data) {
        Write-Host "未使用装备数量: $($unusedEquipments.data.Count)"
    }
}
catch {
    Write-Host "❌ 获取未使用装备失败: $($_.Exception.Message)"
}

# 3. 测试创建装备（如果没有装备的话）
Write-Host "`n3. 测试创建装备:"
$createBody = @{
    UserId = 1
    EquipId = "1001"  # 假设的装备ID
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/equipment/create" -Method POST -Body $createBody -ContentType "application/json"
    Write-Host "✅ 创建装备成功: $($response.StatusCode)"
    $result = $response.Content | ConvertFrom-Json
    Write-Host "创建结果: $($result.message)"
}
catch {
    Write-Host "❌ 创建装备失败: $($_.Exception.Message)"
    Write-Host "这可能是正常的，如果装备ID不存在或其他原因"
}

# 4. 再次检查装备列表
Write-Host "`n4. 再次检查装备列表:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/equipment/user/1" -Method GET
    $equipments = $response.Content | ConvertFrom-Json
    
    if ($equipments.data) {
        Write-Host "当前装备数量: $($equipments.data.Count)"
        foreach ($equip in $equipments.data) {
            Write-Host "   - $($equip.equipName) (ID: $($equip.id), 强化: +$($equip.strengthenLevel))"
        }
    } else {
        Write-Host "用户仍然没有装备"
    }
}
catch {
    Write-Host "❌ 再次获取装备列表失败: $($_.Exception.Message)"
}

Write-Host "`n=== 装备API测试完成 ==="
Write-Host "如果用户没有装备，前端应该显示空列表"
Write-Host "如果有装备，前端应该正确显示装备信息"
