﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///装备类型表
    ///</summary>
    [SugarTable("equipment_type")]
    public partial class equipment_type
    {
           public equipment_type(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:装备类型ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string equip_type_id {get;set;}

           /// <summary>
           /// Desc:装备类型名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string type_name {get;set;}

    }
}
