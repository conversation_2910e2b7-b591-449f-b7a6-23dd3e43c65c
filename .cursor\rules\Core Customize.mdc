---
description: 
globs: 
alwaysApply: true
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to

- 你是一名Cocos Creator专家，当前项目koudai02用的版本是 Cocos Creator 3.8.3

-  WebApplication_HM项目是后端管理系统，前端cocos请求接口来源于该项目

- 使用的脚本语言TypeScript

- Cocos Creator相关的api地址：https://docs.cocos.com/creator/3.8/api/zh/

- Cocos Creator技术文档地址：https://docs.cocos.com/creator/3.8/manual/zh/


- 以下是WebApplication_HM项目的规则，不适用于koudai02项目

- 在控制器中创建的接口如果是HttpPost类型，那参数就必须在DTOs文件夹下创建对应的DTO实体，并且接口接收参数的格式要用FromBody。

- 添加的类、方法、代码段、字段、等必须加备注

- 项目中使用的数据库增删改查必须要用SqlSugar

- 新增任何方法，必须按照项目的架构规范来实现这个功能

- 接口响应的自定义实体创建时要写入到DTOs下的RequestDTO文件夹中,并且名称的结尾要加DTO

- 接口请求的自定义实体创建时要写入到DTOs下的ResultDTO文件夹中,并且名称的结尾要加DTO

- 不能随意去修改我写好的方法

- 每个功能都必须实现 接口方法、服务方法、控制器方法

- 需要保持一致的返回格式

- 玩家相关的都写到PlayerService中 接口和控制器也如此，如果要新增服务需要询问我

- 我手动修改的类名称，不要帮我改回原来的

- 接口定义只存在于 WebApplication_HM/Interface/

- 服务实现类在下,不用再建文件夹 WebApplication_HM/Services/

- WebSocket服务实现类在下 WebApplication_HM/Services/SocketInfo

- WebSocket相关的功能必须放在文件夹下 WebApplication_HM/Services/SocketInfo 

- 完整的功能，接口定义、接口服务实现、控制器、服务注册

- 创建查询功能的时候先判断对应的实体有没该字段如果没有则不创建该功能

- 添加DTOs相关类的时候必须添加对应字段的备注

- 写Service函数的时候，必须对代码进行备注

- SqlSugar技术文档地址：https://www.donet5.com/Doc/1/1180

