using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using WebApplication_HM.Models;

namespace WebApplication_HM.DTOs
{
    /// <summary>
    /// 转生请求DTO
    /// </summary>
    public class NirvanaRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 主宠物ID
        /// </summary>
        [Required]
        public int MainPetId { get; set; }

        /// <summary>
        /// 副宠物ID
        /// </summary>
        [Required]
        public int SubPetId { get; set; }

        /// <summary>
        /// 涅槃兽ID
        /// </summary>
        [Required]
        public int NirvanaPetId { get; set; }

        /// <summary>
        /// 使用的辅助道具ID
        /// </summary>
        public string UsedItemId { get; set; }

        /// <summary>
        /// 转生类型
        /// </summary>
        public NirvanaTypeEnum NirvanaType { get; set; } = NirvanaTypeEnum.NORMAL;
    }

    /// <summary>
    /// 转生结果DTO
    /// </summary>
    public class NirvanaResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 转生结果成长
        /// </summary>
        public decimal? ResultGrowth { get; set; }

        /// <summary>
        /// 转生结果宠物编号
        /// </summary>
        public int? ResultPetNo { get; set; }

        /// <summary>
        /// 转生后的宠物信息
        /// </summary>
        public user_pet ResultPet { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// VIP加成
        /// </summary>
        public decimal VipBonus { get; set; }

        /// <summary>
        /// 实际成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 转生类型
        /// </summary>
        public string NirvanaType { get; set; }
    }

    /// <summary>
    /// 变脸请求DTO
    /// </summary>
    public class FaceChangeRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 主宠物ID
        /// </summary>
        [Required]
        public int MainPetId { get; set; }

        /// <summary>
        /// 副宠物ID
        /// </summary>
        [Required]
        public int SubPetId { get; set; }

        /// <summary>
        /// 涅槃兽ID
        /// </summary>
        [Required]
        public int NirvanaPetId { get; set; }

        /// <summary>
        /// 是否转移非天赋技能
        /// </summary>
        public bool TransferNonTalentSkills { get; set; } = false;

        /// <summary>
        /// 使用的辅助道具ID
        /// </summary>
        public string UsedItemId { get; set; }
    }

    /// <summary>
    /// 变脸结果DTO
    /// </summary>
    public class FaceChangeResultDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 变脸后的宠物信息
        /// </summary>
        public user_pet ResultPet { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 属性继承比例
        /// </summary>
        public decimal AttributeRatio { get; set; }

        /// <summary>
        /// 转移的技能数量
        /// </summary>
        public int TransferredSkillCount { get; set; }
    }

    /// <summary>
    /// 转生预览请求DTO
    /// </summary>
    public class NirvanaPreviewRequestDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        public int UserId { get; set; }

        /// <summary>
        /// 主宠物ID
        /// </summary>
        [Required]
        public int MainPetId { get; set; }

        /// <summary>
        /// 副宠物ID
        /// </summary>
        [Required]
        public int SubPetId { get; set; }

        /// <summary>
        /// 涅槃兽ID
        /// </summary>
        [Required]
        public int NirvanaPetId { get; set; }

        /// <summary>
        /// 使用的辅助道具ID
        /// </summary>
        public string UsedItemId { get; set; }
    }

    /// <summary>
    /// 转生预览结果DTO
    /// </summary>
    public class NirvanaPreviewDto
    {
        /// <summary>
        /// 是否可以转生
        /// </summary>
        public bool CanNirvana { get; set; }

        /// <summary>
        /// 预计成功率
        /// </summary>
        public decimal EstimatedSuccessRate { get; set; }

        /// <summary>
        /// 预计消耗金币
        /// </summary>
        public long EstimatedCostGold { get; set; }

        /// <summary>
        /// 预计获得成长范围
        /// </summary>
        public string EstimatedGrowthRange { get; set; }

        /// <summary>
        /// VIP加成
        /// </summary>
        public decimal VipBonus { get; set; }

        /// <summary>
        /// 转生配置信息
        /// </summary>
        public PetNirvanaConfigDto ConfigInfo { get; set; }

        /// <summary>
        /// 验证失败原因
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new List<string>();
    }

    /// <summary>
    /// 转生配置DTO
    /// </summary>
    public class PetNirvanaConfigDto
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 主宠物编号
        /// </summary>
        public int MainPetNo { get; set; }

        /// <summary>
        /// 副宠物编号
        /// </summary>
        public int SubPetNo { get; set; }

        /// <summary>
        /// 涅槃兽编号
        /// </summary>
        public int NirvanaPetNo { get; set; }

        /// <summary>
        /// 转生结果宠物编号
        /// </summary>
        public int ResultPetNo { get; set; }

        /// <summary>
        /// 所需等级
        /// </summary>
        public int RequiredLevel { get; set; }

        /// <summary>
        /// 基础成功率
        /// </summary>
        public decimal BaseSuccessRate { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// 主宠成长继承比例
        /// </summary>
        public decimal MainGrowthInherit { get; set; }

        /// <summary>
        /// 副宠成长继承比例
        /// </summary>
        public decimal SubGrowthInherit { get; set; }

        /// <summary>
        /// 特殊规则
        /// </summary>
        public string SpecialRule { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 转生记录DTO
    /// </summary>
    public class NirvanaRecordDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 主宠物ID
        /// </summary>
        public int MainPetId { get; set; }

        /// <summary>
        /// 副宠物ID
        /// </summary>
        public int SubPetId { get; set; }

        /// <summary>
        /// 涅槃兽ID
        /// </summary>
        public int NirvanaPetId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 转生结果成长
        /// </summary>
        public decimal? ResultGrowth { get; set; }

        /// <summary>
        /// 消耗金币
        /// </summary>
        public long CostGold { get; set; }

        /// <summary>
        /// VIP加成
        /// </summary>
        public decimal VipBonus { get; set; }

        /// <summary>
        /// 转生类型
        /// </summary>
        public string NirvanaType { get; set; }

        /// <summary>
        /// 转生时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 转生统计DTO
    /// </summary>
    public class NirvanaStatisticsDto
    {
        /// <summary>
        /// 总转生次数
        /// </summary>
        public int TotalAttempts { get; set; }

        /// <summary>
        /// 成功次数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }

        /// <summary>
        /// 总消耗金币
        /// </summary>
        public long TotalCostGold { get; set; }

        /// <summary>
        /// 平均获得成长
        /// </summary>
        public decimal AverageGrowthGained { get; set; }

        /// <summary>
        /// 变脸次数
        /// </summary>
        public int FaceChangeCount { get; set; }

        /// <summary>
        /// 统计时间范围
        /// </summary>
        public string TimeRange { get; set; }
    }
}
