﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///道具表
    ///</summary>
    [SugarTable("item_config")]
    public partial class item_config
    {
           public item_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int id {get;set;}

           /// <summary>
           /// Desc:道具编号（业务唯一标识）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int item_no {get;set;}

           /// <summary>
           /// Desc:道具名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? name {get;set;}

           /// <summary>
           /// Desc:道具类型（消耗品/装备/材料/特殊等）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? type {get;set;}

           /// <summary>
           /// Desc:道具描述说明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? description {get;set;}

           /// <summary>
           /// Desc:道具品质（普通/稀有/史诗/传说等）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? quality {get;set;}

           /// <summary>
           /// Desc:道具图标文件名
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? icon {get;set;}

           /// <summary>
           /// Desc:道具价格（金币）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? price {get;set;}

           /// <summary>
           /// Desc:使用限制条件
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? use_limit {get;set;}

           /// <summary>
           /// Desc:扩展信息（JSON格式存储额外属性）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? extra {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:道具脚本
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? script {get;set;}

           /// <summary>
           /// Desc:是否启用
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_active {get;set;}

    }
}
