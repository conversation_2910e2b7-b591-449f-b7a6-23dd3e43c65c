import { _decorator, Component } from 'cc';
const { ccclass } = _decorator;

import { Global } from '../Global';

/**
 * 🧪 流程控制测试脚本
 * 用于验证 panel_field -> fightState -> fightMian 的控制逻辑
 */
@ccclass('FlowControlTest')
export class FlowControlTest extends Component {

    start() {
        console.log("🧪 流程控制测试开始");
        this.runTests();
    }

    /**
     * 运行测试用例
     */
    private runTests(): void {
        console.log("=== 流程控制测试用例 ===");
        
        // 测试1：验证初始状态
        this.testInitialState();
        
        // 测试2：验证宠物选择前的状态
        this.testBeforePetSelection();
        
        // 测试3：模拟宠物选择后的状态
        this.testAfterPetSelection();
        
        console.log("🏁 流程控制测试完成");
    }

    /**
     * 测试1：验证初始状态
     */
    private testInitialState(): void {
        console.log("📋 测试1：验证初始状态");
        
        const initialPetId = Global.currentPetId;
        console.log(`- 初始Global.currentPetId: ${initialPetId}`);
        
        if (initialPetId === 0) {
            console.log("✅ 初始状态正确：未选择宠物");
        } else {
            console.warn("⚠️ 初始状态异常：宠物ID应该为0");
        }
    }

    /**
     * 测试2：验证宠物选择前的状态
     */
    private testBeforePetSelection(): void {
        console.log("📋 测试2：验证宠物选择前的状态");
        
        // 模拟用户尝试开始战斗但未选择宠物的情况
        const canStartBattle = this.simulateStartBattleValidation();
        
        if (!canStartBattle) {
            console.log("✅ 正确阻止了未选择宠物的战斗开始");
        } else {
            console.warn("⚠️ 错误：应该阻止未选择宠物的战斗");
        }
    }

    /**
     * 测试3：模拟宠物选择后的状态
     */
    private testAfterPetSelection(): void {
        console.log("📋 测试3：模拟宠物选择后的状态");
        
        // 模拟用户选择宠物
        Global.currentPetId = 1;
        console.log(`- 模拟选择宠物ID: ${Global.currentPetId}`);
        
        // 验证现在可以开始战斗
        const canStartBattle = this.simulateStartBattleValidation();
        
        if (canStartBattle) {
            console.log("✅ 正确允许选择宠物后开始战斗");
        } else {
            console.warn("⚠️ 错误：选择宠物后应该允许战斗");
        }
        
        // 重置状态
        Global.currentPetId = 0;
        console.log("🔄 已重置测试状态");
    }

    /**
     * 模拟战斗开始验证逻辑
     */
    private simulateStartBattleValidation(): boolean {
        // 模拟 fightState.validatePetSelection() 的逻辑
        if (!Global.currentPetId || Global.currentPetId <= 0) {
            console.log("- 验证结果：宠物未选择，阻止战斗");
            return false;
        }
        
        console.log("- 验证结果：宠物已选择，允许战斗");
        return true;
    }

    /**
     * 打印当前流程状态
     */
    public printCurrentState(): void {
        console.log("=== 当前流程状态 ===");
        console.log(`Global.currentPetId: ${Global.currentPetId}`);
        console.log(`Global.currentMapId: ${Global.currentMapId}`);
        console.log(`Global.userid: ${Global.userid}`);
    }
} 