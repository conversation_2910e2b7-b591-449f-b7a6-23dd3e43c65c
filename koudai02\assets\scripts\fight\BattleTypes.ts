// 战斗相关的数据类型定义

// 简化的战斗请求参数
export interface BattleRequestDTO {
    MapId: number;          // 地图ID
    UserId: number;         // 玩家用户ID  
    PetId: number;          // 玩家宠物ID
    SkillId: string;        // 技能ID
    // 移除会话管理相关字段
}

// 战斗回合数据
export interface BattleRoundDTO {
    round: number;           // 回合数
    attackerType: string;    // 攻击者类型（Player/Monster）
    damage: number;          // 伤害值
    isCritical: boolean;     // 是否暴击
    isHit: boolean;          // 是否命中
    skillId: string;         // 使用的技能ID
    description: string;     // 回合描述
    playerHpAfter: number;   // 攻击后玩家血量
    monsterHpAfter: number;  // 攻击后怪物血量
}

// 战斗结果响应
export interface BattleResultDTO {
    isWin: boolean;              // 是否胜利
    isBattleEnd: boolean;        // 战斗是否结束
    currentRound: number;        // 当前回合数
    battleRounds: BattleRoundDTO[]; // 所有回合的战斗记录
    playerCurrentHp: number;     // 玩家当前血量
    monsterCurrentHp: number;    // 怪物当前血量
    playerMaxHp: number;         // 玩家最大血量
    monsterMaxHp: number;        // 怪物最大血量
    exp: number;                 // 获得的经验
    dropItems: string[];         // 掉落物品
    message: string;             // 结果描述信息
    battleEndFlag: number;       // 战斗结束标志
    isDead: number;              // 是否死亡
    yuanbaoGained: number;       // 获得元宝
    goldGained: number;          // 获得金币
    experienceGained: number;    // 获得经验
    totalDamageDealt: number;    // 总伤害输出
    totalDamageReceived: number; // 总受到伤害
    damageAmplified: number;     // 伤害加深
    damageReduced: number;       // 伤害抵消
    lifeSteal: number;           // 吸血效果
    manaSteal: number;           // 吸魔效果
    remainingMp: number;         // 剩余魔法值
    isFirstStrike: number;       // 先手攻击标识
    autoBattleStatus: number;    // 自动战斗状态
    playerRemainingHp: number;   // 玩家剩余血量
    monsterRemainingHp: number;  // 怪物剩余血量
    itemsGainedString: string;   // 物品获得详情
}

// 掉落物品
export interface DropItemDTO {
    ItemId: string;    // 道具ID
    Name: string;      // 道具名称
    Count: number;     // 数量
}

// 怪物信息
export interface MonsterInfoDTO {
    MonsterId: number;  // 怪物ID
    Name: string;       // 怪物名称
    Level: number;      // 等级
    Attribute: string;  // 属性
}

// 属性结果
export interface AttributeResultDTO {
    Atk: number;    // 攻击力
    Def: number;    // 防御力
    Hit: number;    // 命中
    Dodge: number;  // 闪避
    Spd: number;    // 速度
    Hp: number;     // 生命值
    Mp: number;     // 魔法值
} 
