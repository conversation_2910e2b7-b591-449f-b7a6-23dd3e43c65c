import { _decorator, Component } from 'cc';
const { ccclass, property } = _decorator;

import { Global } from '../Global';

/**
 * 地图数据传递测试脚本
 * 用于验证从fightState到fightMian的地图数据传递功能
 */
@ccclass('MapDataTransferTest')
export class MapDataTransferTest extends Component {

    start() {
        console.log("🧪 地图数据传递测试开始");
        this.testDataTransfer();
    }

    /**
     * 测试数据传递功能
     */
    private testDataTransfer() {
        console.log("📋 测试步骤:");
        
        // 步骤1：检查Global中的地图数据
        console.log("1. 检查Global中的地图数据:");
        console.log(`   - Global.currentMapId: ${Global.currentMapId}`);
        console.log(`   - Global.currentMapMonsters.length: ${Global.currentMapMonsters.length}`);
        console.log(`   - Global.currentMapDrops.length: ${Global.currentMapDrops.length}`);
        console.log(`   - Global.selectedMonsterId: ${Global.selectedMonsterId}`);
        console.log(`   - Global.currentMapInfo:`, Global.currentMapInfo);
        
        // 步骤2：模拟fightState设置数据
        console.log("2. 模拟fightState设置地图数据:");
        this.simulateFightStateData();
        
        // 步骤3：验证数据设置
        console.log("3. 验证数据设置结果:");
        this.verifyDataTransfer();
        
        // 步骤4：模拟fightMian读取数据
        console.log("4. 模拟fightMian读取数据:");
        this.simulateFightMianRead();
        
        console.log("🧪 地图数据传递测试完成");
    }

    /**
     * 模拟fightState设置数据
     */
    private simulateFightStateData(): void {
        // 模拟地图怪物数据
        const mockMonsters = [
            {
                monsterId: 1,
                monsterName: "史莱姆",
                levelRange: "1-3",
                element: "水",
                expReward: 50
            },
            {
                monsterId: 2,
                monsterName: "哥布林",
                levelRange: "2-4",
                element: "土",
                expReward: 80
            },
            {
                monsterId: 3,
                monsterName: "骷髅兵",
                levelRange: "3-5",
                element: "暗",
                expReward: 120
            }
        ];

        // 模拟地图掉落数据
        const mockDrops = [
            {
                itemId: "1001",
                itemName: "生命药水",
                dropType: "地图掉落",
                dropRate: 0.15,
                countRange: "1-2"
            },
            {
                itemId: "1002",
                itemName: "魔法药水",
                dropType: "地图掉落",
                dropRate: 0.10,
                countRange: "1-1"
            },
            {
                itemId: "2001",
                itemName: "铁剑",
                dropType: "怪物掉落",
                dropRate: 0.05,
                countRange: "1-1"
            }
        ];

        // 模拟地图信息
        const mockMapInfo = {
            mapId: 1,
            mapName: "新手村",
            mapDesc: "适合新手冒险的地图",
            mapType: 1,
            icon: "",
            isUnlocked: true,
            recommendLevel: 3
        };

        // 设置到Global中（模拟fightState的行为）
        Global.currentMapMonsters = [...mockMonsters];
        Global.currentMapDrops = [...mockDrops];
        Global.currentMapInfo = mockMapInfo;
        
        // 随机选择一个怪物
        const randomIndex = Math.floor(Math.random() * mockMonsters.length);
        Global.selectedMonsterId = mockMonsters[randomIndex].monsterId;
        
        console.log(`✅ 已设置模拟数据，选中怪物ID: ${Global.selectedMonsterId}`);
    }

    /**
     * 验证数据传递
     */
    private verifyDataTransfer(): void {
        console.log("🔍 验证数据传递结果:");
        
        // 验证怪物数据
        if (Global.currentMapMonsters.length > 0) {
            console.log(`✅ 怪物数据传递成功，数量: ${Global.currentMapMonsters.length}`);
            Global.currentMapMonsters.forEach((monster: any, index: number) => {
                console.log(`   ${index + 1}. ${monster.monsterName} (ID: ${monster.monsterId})`);
            });
        } else {
            console.error("❌ 怪物数据传递失败");
        }
        
        // 验证掉落数据
        if (Global.currentMapDrops.length > 0) {
            console.log(`✅ 掉落数据传递成功，数量: ${Global.currentMapDrops.length}`);
            Global.currentMapDrops.forEach((drop: any, index: number) => {
                console.log(`   ${index + 1}. ${drop.itemName} (概率: ${(drop.dropRate * 100).toFixed(1)}%)`);
            });
        } else {
            console.error("❌ 掉落数据传递失败");
        }
        
        // 验证地图信息
        if (Global.currentMapInfo) {
            console.log(`✅ 地图信息传递成功: ${Global.currentMapInfo.mapName}`);
        } else {
            console.error("❌ 地图信息传递失败");
        }
        
        // 验证选中怪物
        if (Global.selectedMonsterId > 0) {
            const selectedMonster = Global.currentMapMonsters.find((m: any) => m.monsterId === Global.selectedMonsterId);
            if (selectedMonster) {
                console.log(`✅ 选中怪物传递成功: ${selectedMonster.monsterName} (ID: ${Global.selectedMonsterId})`);
            } else {
                console.error(`❌ 选中怪物ID ${Global.selectedMonsterId} 在怪物列表中未找到`);
            }
        } else {
            console.error("❌ 选中怪物ID传递失败");
        }
    }

    /**
     * 模拟fightMian读取数据
     */
    private simulateFightMianRead(): void {
        console.log("📖 模拟fightMian读取数据:");
        
        // 模拟fightMian的数据访问器
        const mapMonsters = Global.currentMapMonsters || [];
        const mapDrops = Global.currentMapDrops || [];
        const mapInfo = Global.currentMapInfo;
        const selectedMonsterId = Global.selectedMonsterId;
        
        console.log("🗺️ ===== fightMian获取的地图数据 =====");
        
        if (mapInfo) {
            console.log(`📍 地图名称: ${mapInfo.mapName} (ID: ${mapInfo.mapId})`);
            console.log(`📝 地图描述: ${mapInfo.mapDesc}`);
            console.log(`🎯 推荐等级: ${mapInfo.recommendLevel}`);
        }
        
        console.log(`👹 可战斗怪物数量: ${mapMonsters.length}`);
        if (mapMonsters.length > 0) {
            console.log("👹 怪物列表:");
            mapMonsters.forEach((monster: any, index: number) => {
                const isSelected = monster.monsterId === selectedMonsterId;
                const marker = isSelected ? "🎯" : "  ";
                console.log(`${marker} ${index + 1}. ${monster.monsterName} (ID: ${monster.monsterId})`);
                console.log(`      等级: ${monster.levelRange}, 属性: ${monster.element}, 经验: ${monster.expReward}`);
            });
        }
        
        console.log(`💎 掉落配置数量: ${mapDrops.length}`);
        if (mapDrops.length > 0) {
            console.log("💎 可能掉落:");
            mapDrops.forEach((drop: any, index: number) => {
                console.log(`   ${index + 1}. ${drop.itemName} (概率: ${(drop.dropRate * 100).toFixed(1)}%)`);
            });
        }
        
        // 模拟获取当前怪物信息
        const currentMonster = mapMonsters.find((monster: any) => monster.monsterId === selectedMonsterId);
        if (currentMonster) {
            console.log(`🎯 当前战斗怪物: ${currentMonster.monsterName}`);
            console.log(`   等级范围: ${currentMonster.levelRange}`);
            console.log(`   属性: ${currentMonster.element}`);
            console.log(`   经验奖励: ${currentMonster.expReward}`);
        }
        
        console.log("🗺️ ==============================");
    }

    /**
     * 清理测试数据
     */
    public clearTestData(): void {
        console.log("🧹 清理测试数据");
        Global.currentMapMonsters = [];
        Global.currentMapDrops = [];
        Global.currentMapInfo = null;
        Global.selectedMonsterId = 0;
        console.log("✅ 测试数据已清理");
    }

    /**
     * 手动测试数据访问（供控制台调用）
     */
    public testDataAccess(): void {
        console.log("🔍 手动测试数据访问:");
        console.log("Global.currentMapMonsters:", Global.currentMapMonsters);
        console.log("Global.currentMapDrops:", Global.currentMapDrops);
        console.log("Global.currentMapInfo:", Global.currentMapInfo);
        console.log("Global.selectedMonsterId:", Global.selectedMonsterId);
    }
} 