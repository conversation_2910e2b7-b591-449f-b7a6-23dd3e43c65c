namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 系统公告DTO
    /// </summary>
    public class SystemNoticeDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "system_notice";

        /// <summary>
        /// 公告ID
        /// </summary>
        public string NoticeId { get; set; }

        /// <summary>
        /// 公告标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 公告内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 公告类型 (maintenance/update/event/warning/info)
        /// </summary>
        public string NoticeType { get; set; }

        /// <summary>
        /// 优先级 (high/medium/low)
        /// </summary>
        public string Priority { get; set; }

        /// <summary>
        /// 是否弹窗显示
        /// </summary>
        public bool ShowPopup { get; set; }

        /// <summary>
        /// 发送者（管理员名称）
        /// </summary>
        public string Sender { get; set; }

        /// <summary>
        /// 目标玩家ID列表（空表示全服）
        /// </summary>
        public List<int> TargetPlayerIds { get; set; } = new List<int>();

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime SendTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpireTime { get; set; }
    }
} 