//全局静态变量
export class Global {
    //接口地址
    public static Url: string = "http://localhost:5000/api/";

    //WebSocket地址
    public static WebSocketUrl: string = "ws://localhost:5000/ws/universal";
    
    //账号
    public static username: string = "HM";

    //密码
    public static password: string = "123456";

    //用户ID
    public static userid: number = 1;
    
    // 战斗相关配置
    public static currentMapId: number = 100;        // 当前地图ID
    public static currentPetId: number = 3;        // 当前出战宠物ID
    public static currentSkillId: string = "1";  // 当前技能ID

    //宠物名称
    public static petName: string = "";
    //宠物等级
    public static petLevel: number = 0;
    
    // 🆕 地图数据存储（从fightState传递给fightMian）
    public static currentMapMonsters: any[] = [];   // 当前地图怪物列表
    public static currentMapDrops: any[] = [];      // 当前地图掉落配置
    public static currentMapInfo: any = null;       // 当前地图基础信息
    public static selectedMonsterId: number = 0;    // 当前选中的怪物ID（用于战斗）

    public static atlastName: string = "";          // 地图图集名称
    public static background: string = "";          // 地图背景图名称
}


