namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 地图详情结果DTO
    /// </summary>
    public class MapDetailResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 地图基础信息
        /// </summary>
        public MapInfoDTO? MapInfo { get; set; }

        /// <summary>
        /// 地图详细配置
        /// </summary>
        public MapDetailConfigDTO? DetailConfig { get; set; }

        /// <summary>
        /// 地图怪物列表
        /// </summary>
        public List<MapMonsterDTO> Monsters { get; set; } = new List<MapMonsterDTO>();

        /// <summary>
        /// 地图掉落配置
        /// </summary>
        public List<MapDropDTO> Drops { get; set; } = new List<MapDropDTO>();
    }

    /// <summary>
    /// 地图详细配置DTO
    /// </summary>
    public class MapDetailConfigDTO
    {
        /// <summary>
        /// 限制等级
        /// </summary>
        public int LimitLevel { get; set; }

        /// <summary>
        /// 限制成长
        /// </summary>
        public decimal LimitGrowth { get; set; }

        /// <summary>
        /// 是否需要钥匙
        /// </summary>
        public bool RequireKey { get; set; }

        /// <summary>
        /// 最小金币奖励
        /// </summary>
        public long MinGold { get; set; }

        /// <summary>
        /// 最大金币奖励
        /// </summary>
        public long MaxGold { get; set; }

        /// <summary>
        /// 最小元宝奖励
        /// </summary>
        public int MinYuanbao { get; set; }

        /// <summary>
        /// 最大元宝奖励
        /// </summary>
        public int MaxYuanbao { get; set; }
    }

    /// <summary>
    /// 地图怪物DTO
    /// </summary>
    public class MapMonsterDTO
    {
        /// <summary>
        /// 怪物ID
        /// </summary>
        public int MonsterId { get; set; }

        /// <summary>
        /// 怪物名称
        /// </summary>
        public string MonsterName { get; set; } = string.Empty;

        /// <summary>
        /// 怪物等级范围
        /// </summary>
        public string LevelRange { get; set; } = string.Empty;

        /// <summary>
        /// 怪物五行属性
        /// </summary>
        public string Element { get; set; } = string.Empty;

        /// <summary>
        /// 经验奖励
        /// </summary>
        public long ExpReward { get; set; }
    }

    /// <summary>
    /// 地图掉落DTO
    /// </summary>
    public class MapDropDTO
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// 道具名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 掉落类型
        /// </summary>
        public string DropType { get; set; } = string.Empty;

        /// <summary>
        /// 掉落概率
        /// </summary>
        public decimal DropRate { get; set; }

        /// <summary>
        /// 数量范围
        /// </summary>
        public string CountRange { get; set; } = string.Empty;
    }
} 