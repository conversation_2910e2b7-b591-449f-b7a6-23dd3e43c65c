# Cocos Creator 3.8 核心模块相关接口规则文档

## 一、命名空间
### 1. array
暂未获取到详细说明，推测该命名空间包含了一些与数组操作相关的工具函数，例如数组的创建、遍历、排序等。在处理游戏中的数组数据时可能会用到。
### 2. CCObject
作为大部分对象的基类命名空间，可能提供了对象的基本属性和方法，如对象的创建、销毁、属性设置等。在创建自定义对象时可能会继承该命名空间下的类。
### 3. distance
推测该命名空间与距离计算相关，可能包含了计算两点之间距离、物体之间距离等函数，在处理游戏中的位置和距离关系时会用到。
### 4. easing
包含了各种缓动函数，用于实现动画的缓动效果，如线性缓动、弹性缓动等。在创建动画时可以使用这些缓动函数来实现不同的动画效果。
### 5. geometry
该命名空间包含了基础几何相关的类和函数，如 AABB、Capsule、Frustum 等，用于处理游戏中的几何形状和碰撞检测等问题。
### 6. js
可能提供了一些 JavaScript 相关的工具函数，如对象的拷贝、属性的设置和获取等，用于简化 JavaScript 代码的编写。
### 7. memop
推测与内存管理相关，可能包含了一些对象池、内存分配和释放等功能，用于优化游戏的内存使用。
### 8. misc
包含了一些杂项工具函数，可能是一些不适合归类到其他命名空间的功能，如文件路径处理、字符串格式化等。
### 9. path
提供了文件路径处理的相关函数，如获取文件名、目录名、扩展名等，在处理游戏资源的路径时会用到。
### 10. Settings
用于获取 `settings.json` 配置文件中的配置信息，同时可以覆盖一些配置从而影响引擎的启动和运行。通过 `settings` 可以访问此模块单例。
### 11. sys
包含了一系列系统相关环境变量，如操作系统信息、设备信息等，在处理不同平台的兼容性问题时会用到。
### 12. _decorator
提供了一些装饰器函数，用于装饰类和属性，如 `ccclass`、`property` 等，用于简化类和属性的定义。

## 二、类
### 1. AABB
#### 说明
基础几何：轴对齐包围盒，使用中心点和半长宽高的结构。这是检查两个 3D 对象是否相交的一种在计算和内存上效率都相当高的方法。
#### 使用方式
```typescript
import { AABB } from 'cc';
// 创建一个 AABB 实例
const aabb = new AABB();
// 设置中心点和半长宽高
aabb.center.set(0, 0, 0);
aabb.halfExtents.set(1, 1, 1);
```
### 2. aabb
`AABB` 的别名类，使用方式与 `AABB` 相同。
### 3. AlphaKey
暂未获取到详细说明，推测与颜色的透明度关键帧相关，用于实现颜色透明度的渐变效果。
### 4. AnimationCurve
#### 说明
描述一条曲线，其中每个相邻关键帧采用三次 hermite 插值计算。用于实现动画的曲线效果，如物体的移动轨迹、颜色的渐变等。
#### 使用方式
```typescript
import { AnimationCurve, Keyframe } from 'cc';
// 创建一个动画曲线实例
const curve = new AnimationCurve();
// 添加关键帧
const keyframe1 = new Keyframe(0, 0);
const keyframe2 = new Keyframe(1, 10);
curve.addKey(keyframe1);
curve.addKey(keyframe2);
```
### 5. AsyncDelegate
#### 说明
用于支持异步回调的代理，你可以新建一个异步代理，并注册异步回调，等到对应的时机触发代理事件。
#### 使用方式
```typescript
import { AsyncDelegate } from 'cc';
// 创建一个异步代理实例
const asyncDelegate = new AsyncDelegate();
// 注册异步回调
asyncDelegate.on('event', (param) => { 
    console.log('异步回调触发:', param); 
}); 
// 触发代理事件
asyncDelegate.emit('event', '参数'); 
```
### 6. CachedArray
#### 说明
适用于对象缓存的数组类型封装，一般用于不易被移除的常驻数据。它的内部数组长度会持续增长，不会减少。
#### 使用方式
```typescript
import { CachedArray } from 'cc';
// 创建一个 CachedArray 实例
const cachedArray = new CachedArray();
// 添加元素
cachedArray.push(1);
cachedArray.push(2);
```
### 7. Capsule
#### 说明
基础几何，胶囊体。用于处理游戏中的胶囊体形状和碰撞检测等问题。
#### 使用方式
```typescript
import { Capsule } from 'cc';
// 创建一个 Capsule 实例
const capsule = new Capsule();
// 设置胶囊体的参数
capsule.radius = 1; 
capsule.height = 2; 
```
### 8. capsule
`Capsule` 的别名类，使用方式与 `Capsule` 相同。
### 9. CCObject
#### 说明
大部分对象的基类，提供了对象的基本属性和方法，如对象的创建、销毁、属性设置等。
#### 使用方式
```typescript
import { CCObject } from 'cc';
// 创建一个 CCObject 实例
const ccObject = new CCObject();
// 设置对象的属性
ccObject.name = 'myObject'; 
```
### 10. ColorKey
暂未获取到详细说明，推测与颜色的关键帧相关，用于实现颜色的渐变效果。
### 11. CompactValueTypeArray
暂未获取到详细说明，推测与值类型数组的压缩存储相关，用于优化内存使用。
### 12. Frustum
#### 说明
基础几何：视锥体。用于处理游戏中的视锥体相关问题，如相机的视锥体裁剪等。
#### 使用方式
```typescript
import { Frustum } from 'cc';
// 创建一个 Frustum 实例
const frustum = new Frustum();
// 设置视锥体的参数
frustum.setFromMatrix(matrix); 
```
### 13. frustum
`Frustum` 的别名类，使用方式与 `Frustum` 相同。
### 14. Gradient
#### 说明
渐变曲线控件包含了颜色关键帧和透明度关键帧，在关键帧中进行插值渐变返回最终的颜色值。用于实现颜色的渐变效果，如粒子系统的颜色渐变等。
#### 使用方式
```typescript
import { Gradient } from 'cc';
// 创建一个 Gradient 实例
const gradient = new Gradient();
// 添加颜色关键帧
gradient.addColorKey(0, new Color(255, 0, 0)); 
gradient.addColorKey(1, new Color(0, 255, 0)); 
```
### 15. IDGenerator
#### 说明
运行时 ID 生成器，用于生成唯一的 ID。在处理游戏中的对象标识时会用到。
#### 使用方式
```typescript
import { IDGenerator } from 'cc';
// 创建一个 IDGenerator 实例
const idGenerator = new IDGenerator();
// 生成一个唯一的 ID
const id = idGenerator.getNewId(); 
```
### 16. Keyframe
#### 说明
曲线中的一个关键帧，包含了时间和值两个属性。用于定义动画曲线的关键帧。
#### 使用方式
```typescript
import { Keyframe } from 'cc';
// 创建一个 Keyframe 实例
const keyframe = new Keyframe(0, 0); 
```
### 17. Line
#### 说明
基础几何：直线。用于处理游戏中的直线相关问题，如射线检测、碰撞检测等。
#### 使用方式
```typescript
import { Line } from 'cc';
// 创建一个 Line 实例
const line = new Line();
// 设置直线的起点和终点
line.start.set(0, 0, 0); 
line.end.set(1, 1, 1); 
```
### 18. line
`Line` 的别名类，使用方式与 `Line` 相同。
### 19. MutableForwardIterator
暂未获取到详细说明，推测与可变的前向迭代器相关，用于遍历数组或集合等数据结构。
### 20. OBB
#### 说明
基础几何：方向包围盒。用于处理游戏中的方向包围盒相关问题，如碰撞检测等。
#### 使用方式
```typescript
import { OBB } from 'cc';
// 创建一个 OBB 实例
const obb = new OBB();
// 设置 OBB 的参数
obb.center.set(0, 0, 0); 
obb.halfExtents.set(1, 1, 1); 
obb.rotation.set(0, 0, 0, 1); 
```
### 21. obb
`OBB` 的别名类，使用方式与 `OBB` 相同。
### 22. ObjectCurve
暂未获取到详细说明，推测与对象的动画曲线相关，用于实现对象属性的动画效果。
### 23. Plane
#### 说明
基础几何：平面。平面方程: a*x + b*y + c*z - d = 0。用于处理游戏中的平面相关问题，如碰撞检测、射线检测等。
#### 使用方式
```typescript
import { Plane } from 'cc';
// 创建一个 Plane 实例
const plane = new Plane();
// 设置平面的参数
plane.normal.set(0, 1, 0); 
plane.d = 0; 
```
### 24. plane
`Plane` 的别名类，使用方式与 `Plane` 相同。
### 25. Pool
#### 说明
支持类型的对象池。这是一个传统设计的对象池，你可以从对象池中取出对象或是放回不再需要对象来复用。
#### 使用方式
```typescript
import { Pool } from 'cc';
// 创建一个对象池实例
const pool = new Pool(() => new MyObject(), 10);
// 从对象池中取出一个对象
const obj = pool.get();
// 使用对象
// ...
// 将对象放回对象池
pool.put(obj);
```
### 26. QuatCurve
#### 说明
四元数曲线，用于实现四元数的动画效果，如物体的旋转动画等。
#### 使用方式
```typescript
import { QuatCurve, QuatKeyframeValue } from 'cc';
// 创建一个 QuatCurve 实例
const quatCurve = new QuatCurve();
// 添加关键帧
const keyframeValue = new QuatKeyframeValue();
keyframeValue.value.set(0, 0, 0, 1);
quatCurve.addKey(0, keyframeValue);
```
### 27. QuatKeyframeValue
#### 说明
四元数关键帧值的视图。注意，该视图可能因关键帧的添加、改变、移除而失效。用于定义四元数曲线的关键帧值。
#### 使用方式
```typescript
import { QuatKeyframeValue } from 'cc';
// 创建一个 QuatKeyframeValue 实例
const quatKeyframeValue = new QuatKeyframeValue();
// 设置四元数的值
quatKeyframeValue.value.set(0, 0, 0, 1);
```
### 28. Ray
#### 说明
基础几何：射线。用于处理游戏中的射线检测问题，如射线与物体的碰撞检测等。
#### 使用方式
```typescript
import { Ray } from 'cc';
// 创建一个 Ray 实例
const ray = new Ray();
// 设置射线的起点和方向
ray.origin.set(0, 0, 0); 
ray.direction.set(0, 0, 1); 
```
### 29. ray
`Ray` 的别名类，使用方式与 `Ray` 相同。
### 30. RealCurve
#### 说明
实数曲线。实数曲线是关键帧曲线的一种。在求值实数曲线时，根据输入的时间进行插值计算。用于实现实数属性的动画效果，如物体的位置、大小等。
#### 使用方式
```typescript
import { RealCurve, RealKeyframeValue } from 'cc';
// 创建一个 RealCurve 实例
const realCurve = new RealCurve();
// 添加关键帧
const keyframeValue = new RealKeyframeValue();
keyframeValue.value = 0;
realCurve.addKey(0, keyframeValue);
```
### 31. RealKeyframeValue
#### 说明
实数帧值的视图。注意，该视图可能因关键帧的添加、改变、移除而失效。用于定义实数曲线的关键帧值。
#### 使用方式
```typescript
import { RealKeyframeValue } from 'cc';
// 创建一个 RealKeyframeValue 实例
const realKeyframeValue = new RealKeyframeValue();
// 设置实数的值
realKeyframeValue.value = 0;
```
### 32. RecyclePool
#### 说明
循环对象池。这种池子被设计为每次使用都完整复用。它没有回收和提取的函数，通过获取 `data` 可以获取池子中所有元素，全部都应该被当做新对象来使用。开发者不应该在相互交叉的不同逻辑中同时使用同一个循环对象池。池子尺寸可以在池子满时自动扩充，也可以手动调整。
#### 使用方式
```typescript
import { RecyclePool } from 'cc';
// 创建一个 RecyclePool 实例
const recyclePool = new RecyclePool(() => new MyObject(), 10);
// 获取池子中的所有元素
const elements = recyclePool.data;
// 使用元素
// ...
```
### 33. Scheduler
#### 说明
负责触发回调函数的类。通常情况下，建议使用 `director.getScheduler()` 来获取系统定时器。有两种不同类型的定时器：`update` 定时器和自定义定时器。
#### 使用方式
```typescript
import { director } from 'cc';
// 获取系统定时器
const scheduler = director.getScheduler();
// 添加一个 update 定时器
scheduler.scheduleUpdate(() => { 
    console.log('update 定时器触发'); 
}, this, 0, false); 
// 添加一个自定义定时器
scheduler.schedule(() => { 
    console.log('自定义定时器触发'); 
}, this, 1, false); 
```
### 34. Screen
`screen` 单例对象提供简单的方法来做屏幕管理相关的工作，如获取屏幕的尺寸、设置屏幕的方向等。
### 35. Settings
#### 说明
配置模块用于获取 `settings.json` 配置文件中的配置信息，同时你可以覆盖一些配置从而影响引擎的启动和运行。
#### 使用方式
```typescript
import { settings } from 'cc';
// 获取配置信息
const config = settings.getSettings();
// 修改配置信息
settings.setSettings({ key: 'value' });
```
### 36. Sphere
#### 说明
基础几何：球。用于处理游戏中的球相关问题，如碰撞检测等。
#### 使用方式
```typescript
import { Sphere } from 'cc';
// 创建一个 Sphere 实例
const sphere = new Sphere();
// 设置球的参数
sphere.center.set(0, 0, 0); 
sphere.radius = 1; 
```
### 37. sphere
`Sphere` 的别名类，使用方式与 `Sphere` 相同。
### 38. Spline
#### 说明
基础几何：Spline。用于处理游戏中的样条曲线相关问题，如物体的移动轨迹等。
#### 使用方式
```typescript
import { Spline } from 'cc';
// 创建一个 Spline 实例
const spline = new Spline();
// 设置样条曲线的参数
// ...
```
### 39. System
#### 说明
功能系统的基类，由 `Director` 管理。可以继承该类来创建自定义的功能系统。
#### 使用方式
```typescript
import { System } from 'cc';
// 创建一个自定义的功能系统
class MySystem extends System { 
    constructor() { 
        super(); 
    } 
    update(dt) { 
        // 系统更新逻辑
    } 
} 
```
### 40. Triangle
#### 说明
基础几何：三角形。用于处理游戏中的三角形相关问题，如碰撞检测等。
#### 使用方式
```typescript
import { Triangle } from 'cc';
// 创建一个 Triangle 实例
const triangle = new Triangle();
// 设置三角形的顶点
    triangle.a.set(0, 0, 0); 
    triangle.b.set(1, 0, 0); 
    triangle.c.set(0, 1, 0); 
```
### 41. triangle
`Triangle` 的别名类，使用方式与 `Triangle` 相同。
### 42. ValueType
#### 说明
所有值类型的基类，提供了值类型的基本属性和方法，如复制、比较等。
#### 使用方式
```typescript
import { ValueType } from 'cc';
// 创建一个自定义的值类型
class MyValueType extends ValueType { 
    constructor() { 
        super(); 
    } 
    // 实现复制方法
    clone() { 
        return new MyValueType(); 
    } 
    // 实现比较方法
    equals(other) { 
        return true; 
    } 
} 
```

## 三、接口
### 1. CustomSerializable
暂未获取到详细说明，推测该接口用于定义自定义的序列化规则，用于对象的序列化和反序列化操作。
### 2. IRayMeshOptions
`rayMesh` 的可选参数结构，用于指定射线与网格的检测选项，如是否检测背面、是否返回所有交点等。
### 3. IRaySubMeshOptions
`raySubMesh` 的可选参数结构，用于指定射线与子网格的检测选项，与 `IRayMeshOptions` 类似。
### 4. IRaySubMeshResult
射线检测结果的存储结构，用于存储射线与子网格的检测结果，如交点位置、法线等。
### 5. ISchedulable
暂未获取到详细说明，推测该接口用于定义可调度的对象，实现该接口的对象可以被 `Scheduler` 调度。
### 6. Macro
预定义常量的接口声明，实际使用应该直接导入 `macro`。用于定义一些全局的常量，如调试模式、版本号等。
### 7. Modifiable
暂未获取到详细说明，推测该接口用于定义可修改的对象，实现该接口的对象可以被修改属性。
### 8. SerializationInput
暂未获取到详细说明，推测该接口用于定义序列化输入的规则，用于对象的反序列化操作。
### 9. SerializationMetadata
暂未获取到详细说明，推测该接口用于定义序列化的元数据，如对象的类型、版本号等。
### 10. SerializationOutput
暂未获取到详细说明，推测该接口用于定义序列化输出的规则，用于对象的序列化操作。

## 四、函数
### 1. addon
#### 说明
将 "sources" 中的所有没在 object 定义的属性从 "sources" 复制到 "object"。
#### 使用方式
```typescript
import { addon } from 'cc';
const object = { a: 1 }; 
const sources = { b: 2 }; 
addon(object, sources); 
console.log(object); // { a: 1, b: 2 } 
```
### 2. appendObjectsAt
#### 说明
在数组的指定索引上插入对象。
#### 使用方式
```typescript
import { appendObjectsAt } from 'cc';
const array = [1, 2, 3]; 
appendObjectsAt(array, [4, 5], 1); 
console.log(array); // [1, 4, 5, 2, 3] 
```
### 3. assert
#### 说明
对检查测试条件进行检查，如果条件不为 true 则输出错误消息。
#### 使用方式
```typescript
import { assert } from 'cc';
const condition = false; 
assert(condition, '条件不满足'); 
```
### 4. assertID
暂未获取到详细说明，推测与断言 ID 相关，用于检查 ID 的有效性。
### 5. backIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时会有一个向后的回弹效果。
### 6. backInOut
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时都会有一个向后的回弹效果。
### 7. backOut
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画结束时会有一个向后的回弹效果。
### 8. backOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的回弹效果与 `backInOut` 相反。
### 9. basename
#### 说明
获取文件路径的文件名，不包含扩展名和目录名。
#### 使用方式
```typescript
import { basename } from 'cc';
const path = '/path/to/file.txt'; 
const name = basename(path); 
console.log(name); // 'file' 
```
### 10. bezier
#### 说明
计算贝塞尔曲线的值。
#### 使用方式
```typescript
import { bezier } from 'cc';
const t = 0.5; 
const p0 = 0; 
const p1 = 1; 
const p2 = 2; 
const p3 = 3; 
const value = bezier(t, p0, p1, p2, p3); 
console.log(value); 
```
### 11. bezierByTime
暂未获取到详细说明，推测与根据时间计算贝塞尔曲线的值相关。
### 12. BitMask
#### 说明
定义一个位掩码类型。编辑器会根据这个数据类型显示不同的显示界面。它可能会在对象添加新属性。新属性的 key 是原来的整型 value，value 是对应的 key。
#### 使用方式
```typescript
import { BitMask } from 'cc';
const bitMask = BitMask(0b1010); 
console.log(bitMask); 
```
### 13. boolean
#### 说明
将该属性标记为布尔值。
#### 使用方式
```typescript
import { boolean } from 'cc';
class MyClass { 
    @boolean 
    public myProperty: boolean = false; 
} 
```
### 14. bounceIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时会有一个弹跳效果。
### 15. bounceInOut
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时都会有一个弹跳效果。
### 16. bounceOut
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画结束时会有一个弹跳效果。
### 17. bounceOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的弹跳效果与 `bounceInOut` 相反。
### 18. callInNextTick
#### 说明
在下一帧调用回调函数。
#### 使用方式
```typescript
import { callInNextTick } from 'cc';
callInNextTick(() => { 
    console.log('下一帧调用'); 
}); 
```
### 19. ccclass
#### 说明
将标准写法的类声明为 CC 类，具体用法请参阅[类型定义](https://docs.cocos.com/creator3d/manual/zh/scripting/ccclass.html)。用于装饰类，使其成为 Cocos Creator 中的类。
#### 使用方式
```typescript
import { ccclass } from 'cc';
@ccclass 
class MyClass { 
    constructor() { 
    } 
} 
```
### 20. CCClass
暂未获取到详细说明，推测与 `ccclass` 类似，用于声明 Cocos Creator 中的类。
### 21. ccenum
#### 说明
枚举类型，例如 TypeScript 中定义的类型。用于定义枚举类型。
#### 使用方式
```typescript
import { ccenum } from 'cc';
const MyEnum = ccenum({ 
    VALUE1: 0, 
    VALUE2: 1 
}); 
```
### 22. changeBasename
#### 说明
更改文件路径的文件名。
#### 使用方式
```typescript
import { changeBasename } from 'cc';
const path = '/path/to/file.txt'; 
const newPath = changeBasename(path, 'newFile'); 
console.log(newPath); // '/path/to/newFile.txt' 
```
### 23. changeExtname
#### 说明
更改文件的扩展名。
#### 使用方式
```typescript
import { changeExtname } from 'cc';
const path = '/path/to/file.txt'; 
const newPath = changeExtname(path, '.jpg'); 
console.log(newPath); // '/path/to/file.jpg' 
```
### 24. circIn
#### 说明
启动慢，加速快。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较慢，然后逐渐加速。
#### 使用方式
```typescript
import { circIn } from 'cc';
const t = 0.5; 
const value = circIn(t); 
console.log(value); 
```
### 25. circInOut
#### 说明
在开始时加速动画，在结束时减慢动画的速度。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时逐渐加速，结束时逐渐减速。
#### 使用方式
```typescript
import { circInOut } from 'cc';
const t = 0.5; 
const value = circInOut(t); 
console.log(value); 
```
### 26. circOut
#### 说明
起动迅速，减速慢。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较快，然后逐渐减速。
#### 使用方式
```typescript
import { circOut } from 'cc';
const t = 0.5; 
const value = circOut(t); 
console.log(value); 
```
### 27. circOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的速度变化与 `circInOut` 相反。
### 28. clampf
#### 说明
限定浮点数的最大最小值。如果数值大于 `max_inclusive` 则返回 `max_inclusive`；如果数值小于 `min_inclusive` 则返回 `min_inclusive`；否则返回自身。
#### 使用方式
```typescript
import { clampf } from 'cc';
const value = 5; 
const min = 0; 
const max = 10; 
const clampedValue = clampf(value, min, max); 
console.log(clampedValue); // 5 
```
### 29. clear
#### 说明
移除对象中所有可枚举属性。
#### 使用方式
```typescript
import { clear } from 'cc';
const object = { a: 1, b: 2 }; 
clear(object); 
console.log(object); // {} 
```
### 30. constant
#### 说明
没有任何缓动效果。用于实现没有缓动的动画效果，即线性变化。
#### 使用方式
```typescript
import { constant } from 'cc';
const t = 0.5; 
const value = constant(t); 
console.log(value); // 0.5 
```
### 31. contains
#### 说明
返回数组是否包含指定的元素。
#### 使用方式
```typescript
import { contains } from 'cc';
const array = [1, 2, 3]; 
const result = contains(array, 2); 
console.log(result); // true 
```
### 32. copy
#### 说明
拷贝数组。
#### 使用方式
```typescript
import { copy } from 'cc';
const array = [1, 2, 3]; 
const copiedArray = copy(array); 
console.log(copiedArray); // [1, 2, 3] 
```
### 33. copyAllProperties
#### 说明
把 `source` 的所有属性，除了那些定义在 `excepts` 的属性，拷贝到 `target` 。
#### 使用方式
```typescript
import { copyAllProperties } from 'cc';
const source = { a: 1, b: 2 }; 
const target = {}; 
const excepts = ['b']; 
copyAllProperties(target, source, excepts); 
console.log(target); // { a: 1 } 
```
### 34. createMap
#### 说明
该方法是对 `Object.create(null)` 的简单封装。用于创建无 `prototype` （也就无继承）的空对象。这样在该对象上查找属性时，就不用进行 `hasOwnProperty` 判断，此时对性能提升有帮助。
#### 使用方式
```typescript
import { createMap } from 'cc';
const map = createMap(); 
map.key = 'value'; 
console.log(map); // { key: 'value' } 
```
### 35. cubicIn
#### 说明
启动慢，加速快。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较慢，然后逐渐加速。
#### 使用方式
```typescript
import { cubicIn } from 'cc';
const t = 0.5; 
const value = cubicIn(t); 
console.log(value); 
```
### 36. cubicInOut
#### 说明
在开始时加速动画，在结束时减慢动画的速度。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时逐渐加速，结束时逐渐减速。
#### 使用方式
```typescript
import { cubicInOut } from 'cc';
const t = 0.5; 
const value = cubicInOut(t); 
console.log(value); 
```
### 37. cubicOut
#### 说明
起动迅速，减速慢。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较快，然后逐渐减速。
#### 使用方式
```typescript
import { cubicOut } from 'cc';
const t = 0.5; 
const value = cubicOut(t); 
console.log(value); 
```
### 38. cubicOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的速度变化与 `cubicInOut` 相反。
### 39. debug
#### 说明
输出一条“调试”日志等级的消息。
#### 使用方式
```typescript
import { debug } from 'cc';
debug('这是一条调试日志'); 
```
### 40. debugID
暂未获取到详细说明，推测与调试 ID 相关，用于输出带有 ID 的调试日志。
### 41. degreesToRadians
#### 说明
将度数转换为弧度。
#### 使用方式
```typescript
import { degreesToRadians } from 'cc';
const degrees = 90; 
const radians = degreesToRadians(degrees); 
console.log(radians); // Math.PI / 2 
```
### 42. dirname
#### 说明
获取文件路径的目录名。
#### 使用方式
```typescript
import { dirname } from 'cc';
const path = '/path/to/file.txt'; 
const dir = dirname(path); 
console.log(dir); // '/path/to' 
```
### 43. disallowMultiple
#### 说明
防止多个相同类型（或子类型）的组件被添加到同一个节点。
#### 使用方式
```typescript
import { disallowMultiple } from 'cc';
@disallowMultiple 
class MyComponent { 
    constructor() { 
    } 
} 
```
### 44. elasticIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时会有一个弹性的回弹效果。
### 45. elasticInOut
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时都会有一个弹性的回弹效果。
### 46. elasticOut
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画结束时会有一个弹性的回弹效果。
### 47. elasticOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的弹性回弹效果与 `elasticInOut` 相反。
### 48. Enum
#### 说明
定义一个枚举类型。用户可以把枚举值设为任意的整数，如果设为 -1，系统将会分配为上一个枚举值 + 1。
#### 使用方式
```typescript
import { Enum } from 'cc';
const MyEnum = Enum({ 
    VALUE1: 0, 
    VALUE2: -1 
}); 
console.log(MyEnum.VALUE2); // 1 
```
### 49. error
#### 说明
向控制台输出一条错误信息。这条信息可能是单个字符串（包括可选的替代字符串），也可能是一个或多个对象。
#### 使用方式
```typescript
import { error } from 'cc';
error('这是一条错误信息'); 
```
### 50. errorID
暂未获取到详细说明，推测与错误 ID 相关，用于输出带有 ID 的错误信息。
#### 
```

```