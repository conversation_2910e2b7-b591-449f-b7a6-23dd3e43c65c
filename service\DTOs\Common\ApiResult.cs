namespace WebApplication_HM.DTOs.Common
{
    /// <summary>
    /// API统一返回格式
    /// </summary>
    public class ApiResult
    {
        public bool Success { get; set; }
        public bool IsSuccess => Success; // 添加IsSuccess属性作为Success的别名
        public string Message { get; set; } = "";
        public object? Data { get; set; }

        public static ApiResult CreateSuccess(string message = "操作成功", object? data = null)
        {
            return new ApiResult { Success = true, Message = message, Data = data };
        }

        public static ApiResult CreateError(string message = "操作失败")
        {
            return new ApiResult { Success = false, Message = message };
        }
    }

    /// <summary>
    /// 泛型API返回格式
    /// </summary>
    public class ApiResult<T> : ApiResult
    {
        public new T? Data { get; set; }

        public static ApiResult<T> CreateSuccess(T data, string message = "操作成功")
        {
            return new ApiResult<T> { Success = true, Message = message, Data = data };
        }

        public static new ApiResult<T> CreateError(string message = "操作失败")
        {
            return new ApiResult<T> { Success = false, Message = message };
        }
    }
}
