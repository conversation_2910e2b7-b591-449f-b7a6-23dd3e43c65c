using System.Security.Cryptography;
using System.Text;

namespace WebApplication_HM.Utils
{
    /// <summary>
    /// AES加密解密工具类
    /// </summary>
    public static class CryptoHelper
    {
        /// <summary>
        /// AES加密
        /// </summary>
        /// <param name="keyHex">16进制密钥</param>
        /// <param name="data">待加密数据</param>
        /// <returns>Base64编码的加密结果</returns>
        public static string EncryptAES(string keyHex, byte[] data)
        {
            try
            {
                // 将16进制密钥转换为字节数组
                byte[] key = Convert.FromHexString(keyHex);

                // 生成随机IV
                byte[] iv = new byte[16];
                using (var rng = RandomNumberGenerator.Create())
                {
                    rng.GetBytes(iv);
                }

                // 创建AES加密对象
                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                // 加密数据
                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                using (var bwEncrypt = new BinaryWriter(csEncrypt))
                {
                    bwEncrypt.Write(data);
                }

                // 组合IV和加密数据
                byte[] encryptedData = msEncrypt.ToArray();
                byte[] combinedData = new byte[iv.Length + encryptedData.Length];
                Buffer.BlockCopy(iv, 0, combinedData, 0, iv.Length);
                Buffer.BlockCopy(encryptedData, 0, combinedData, iv.Length, encryptedData.Length);

                // 返回Base64编码结果
                return Convert.ToBase64String(combinedData);
            }
            catch (Exception ex)
            {
                throw new Exception("AES加密失败", ex);
            }
        }

        /// <summary>
        /// AES解密
        /// </summary>
        /// <param name="keyHex">16进制密钥</param>
        /// <param name="base64Ciphertext">Base64编码的密文</param>
        /// <returns>解密后的字节数组</returns>
        public static byte[] DecryptAES(string keyHex, string base64Ciphertext)
        {
            try
            {
                // 解码Base64密文
                byte[] combinedData = Convert.FromBase64String(base64Ciphertext);
                if (combinedData.Length < 16)
                {
                    throw new Exception("密文长度不足，无法提取IV");
                }

                // 提取IV和密文
                byte[] iv = new byte[16];
                byte[] ciphertext = new byte[combinedData.Length - 16];
                Buffer.BlockCopy(combinedData, 0, iv, 0, 16);
                Buffer.BlockCopy(combinedData, 16, ciphertext, 0, ciphertext.Length);

                // 将16进制密钥转换为字节数组
                byte[] key = Convert.FromHexString(keyHex);

                // 创建AES解密对象
                using var aes = Aes.Create();
                aes.Key = key;
                aes.IV = iv;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                // 解密数据
                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(ciphertext);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var msPlaintext = new MemoryStream();
                csDecrypt.CopyTo(msPlaintext);

                return msPlaintext.ToArray();
            }
            catch (Exception ex)
            {
                throw new Exception("AES解密失败", ex);
            }
        }

        /// <summary>
        /// AES加密字符串
        /// </summary>
        /// <param name="keyHex">16进制密钥</param>
        /// <param name="plaintext">待加密字符串</param>
        /// <returns>Base64编码的加密结果</returns>
        public static string EncryptString(string keyHex, string plaintext)
        {
            byte[] data = Encoding.UTF8.GetBytes(plaintext);
            return EncryptAES(keyHex, data);
        }

        /// <summary>
        /// AES解密字符串
        /// </summary>
        /// <param name="keyHex">16进制密钥</param>
        /// <param name="base64Ciphertext">Base64编码的密文</param>
        /// <returns>解密后的字符串</returns>
        public static string DecryptString(string keyHex, string base64Ciphertext)
        {
            byte[] decryptedData = DecryptAES(keyHex, base64Ciphertext);
            return Encoding.UTF8.GetString(decryptedData);
        }

        #region RSA配置
        /// <summary>
        /// RSA密钥大小
        /// </summary>
        private const int RSA_KEY_SIZE = 2048;

        /// <summary>
        /// RSA私钥文件路径
        /// </summary>
        private static readonly string RSA_PRIVATE_KEY_PATH = Path.Combine("keys", "private_key.pem");

        /// <summary>
        /// RSA公钥文件路径
        /// </summary>
        private static readonly string RSA_PUBLIC_KEY_PATH = Path.Combine("keys", "public_key.pem");
        #endregion

        #region RSA密钥生成和管理
        /// <summary>
        /// 检查RSA密钥文件是否存在，不存在则生成新的密钥对
        /// </summary>
        public static void GenerateOrLoadRSAKeys()
        {
            if (!File.Exists(RSA_PRIVATE_KEY_PATH))
            {
                Console.WriteLine("RSA密钥文件已生成。");
                GenerateRSAKeys();
            }
            else
            {
                Console.WriteLine("RSA密钥文件已存在。");
            }
        }

        /// <summary>
        /// 生成RSA密钥对并保存到文件
        /// </summary>
        public static void GenerateRSAKeys()
        {
            try
            {
                // 创建密钥目录
                Directory.CreateDirectory(Path.GetDirectoryName(RSA_PRIVATE_KEY_PATH));

                // 生成RSA密钥对
                using var rsa = RSA.Create(RSA_KEY_SIZE);

                // 导出私钥
                var privateKey = rsa.ExportPkcs8PrivateKey();
                var privateKeyPem = new StringBuilder();
                privateKeyPem.AppendLine("-----BEGIN PRIVATE KEY-----");
                privateKeyPem.AppendLine(Convert.ToBase64String(privateKey, Base64FormattingOptions.InsertLineBreaks));
                privateKeyPem.AppendLine("-----END PRIVATE KEY-----");
                File.WriteAllText(RSA_PRIVATE_KEY_PATH, privateKeyPem.ToString());

                // 导出公钥
                var publicKey = rsa.ExportSubjectPublicKeyInfo();
                var publicKeyPem = new StringBuilder();
                publicKeyPem.AppendLine("-----BEGIN PUBLIC KEY-----");
                publicKeyPem.AppendLine(Convert.ToBase64String(publicKey, Base64FormattingOptions.InsertLineBreaks));
                publicKeyPem.AppendLine("-----END PUBLIC KEY-----");
                File.WriteAllText(RSA_PUBLIC_KEY_PATH, publicKeyPem.ToString());
            }
            catch (Exception ex)
            {
                throw new Exception("生成RSA密钥对失败", ex);
            }
        }

        /// <summary>
        /// 获取公钥内容
        /// </summary>
        public static string GetRsaPublicKey()
        {
            try
            {
                return File.ReadAllText(RSA_PUBLIC_KEY_PATH);
            }
            catch (Exception ex)
            {
                throw new Exception("读取RSA公钥失败", ex);
            }
        }
        #endregion

        #region RSA加密解密
        /// <summary>
        /// 使用RSA公钥加密数据
        /// </summary>
        /// <param name="data">待加密数据</param>
        /// <returns>加密后的数据</returns>
        public static byte[] EncryptWithPublicKey(byte[] data)
        {
            try
            {
                // 读取公钥
                string publicKeyPem = File.ReadAllText(RSA_PUBLIC_KEY_PATH);

                // 移除PEM头尾和换行
                string publicKeyBase64 = publicKeyPem
                    .Replace("-----BEGIN PUBLIC KEY-----", "")
                    .Replace("-----END PUBLIC KEY-----", "")
                    .Replace("\n", "")
                    .Replace("\r", "");

                // 解码公钥
                byte[] publicKeyBytes = Convert.FromBase64String(publicKeyBase64);

                // 创建RSA对象并导入公钥
                using var rsa = RSA.Create();
                rsa.ImportSubjectPublicKeyInfo(publicKeyBytes, out _);

                // 加密数据
                return rsa.Encrypt(data, RSAEncryptionPadding.Pkcs1);
            }
            catch (Exception ex)
            {
                throw new Exception("RSA加密失败", ex);
            }
        }

        /// <summary>
        /// 使用RSA私钥解密数据(PKCS1填充)
        /// </summary>
        /// <param name="encryptedData">加密数据</param>
        /// <returns>解密后的数据</returns>
        public static byte[] DecryptWithPrivateKey_PKCS1(byte[] encryptedData)
        {
            try
            {
                // 读取私钥
                string privateKeyPem = File.ReadAllText(RSA_PRIVATE_KEY_PATH);

                // 移除PEM头尾和换行
                string privateKeyBase64 = privateKeyPem
                    .Replace("-----BEGIN PRIVATE KEY-----", "")
                    .Replace("-----END PRIVATE KEY-----", "")
                    .Replace("\n", "")
                    .Replace("\r", "");

                // 解码私钥
                byte[] privateKeyBytes = Convert.FromBase64String(privateKeyBase64);

                // 创建RSA对象并导入私钥
                using var rsa = RSA.Create();
                rsa.ImportPkcs8PrivateKey(privateKeyBytes, out _);

                // 解密数据
                return rsa.Decrypt(encryptedData, RSAEncryptionPadding.Pkcs1);
            }
            catch (Exception ex)
            {
                throw new Exception("RSA解密失败", ex);
            }
        }

        /// <summary>
        /// 使用RSA私钥解密数据(OAEP填充)
        /// </summary>
        /// <param name="encryptedData">加密数据</param>
        /// <returns>解密后的数据</returns>
        public static byte[] DecryptWithPrivateKey_OAEP(byte[] encryptedData)
        {
            try
            {
                // 读取私钥
                string privateKeyPem = File.ReadAllText(RSA_PRIVATE_KEY_PATH);

                // 移除PEM头尾和换行
                string privateKeyBase64 = privateKeyPem
                    .Replace("-----BEGIN PRIVATE KEY-----", "")
                    .Replace("-----END PRIVATE KEY-----", "")
                    .Replace("\n", "")
                    .Replace("\r", "");

                // 解码私钥
                byte[] privateKeyBytes = Convert.FromBase64String(privateKeyBase64);

                // 创建RSA对象并导入私钥
                using var rsa = RSA.Create();
                rsa.ImportPkcs8PrivateKey(privateKeyBytes, out _);

                // 使用OAEP填充方式解密数据
                return rsa.Decrypt(encryptedData, RSAEncryptionPadding.OaepSHA256);
            }
            catch (Exception ex)
            {
                throw new Exception("RSA解密失败", ex);
            }
        }

        /// <summary>
        /// 使用RSA公钥加密字符串
        /// </summary>
        /// <param name="plaintext">待加密字符串</param>
        /// <returns>Base64编码的加密结果</returns>
        public static string EncryptStringWithRSA(string plaintext)
        {
            byte[] data = Encoding.UTF8.GetBytes(plaintext);
            byte[] encryptedData = EncryptWithPublicKey(data);
            return Convert.ToBase64String(encryptedData);
        }

        /// <summary>
        /// 使用RSA私钥解密字符串(PKCS1填充)
        /// </summary>
        /// <param name="base64Ciphertext">Base64编码的密文</param>
        /// <returns>解密后的字符串</returns>
        public static string DecryptStringWithRSA_PKCS1(string base64Ciphertext)
        {
            byte[] encryptedData = Convert.FromBase64String(base64Ciphertext);
            byte[] decryptedData = DecryptWithPrivateKey_PKCS1(encryptedData);
            return Encoding.UTF8.GetString(decryptedData);
        }

        /// <summary>
        /// 使用RSA私钥解密字符串(OAEP填充)
        /// </summary>
        /// <param name="base64Ciphertext">Base64编码的密文</param>
        /// <returns>解密后的字符串</returns>
        public static string DecryptStringWithRSA_OAEP(string base64Ciphertext)
        {
            byte[] encryptedData = Convert.FromBase64String(base64Ciphertext);
            byte[] decryptedData = DecryptWithPrivateKey_OAEP(encryptedData);
            return Encoding.UTF8.GetString(decryptedData);
        }
        #endregion
    }
}