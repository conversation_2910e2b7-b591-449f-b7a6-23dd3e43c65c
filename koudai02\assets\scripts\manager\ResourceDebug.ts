import { _decorator, Component, Label, Node } from 'cc';
import { ResourceManager } from './ResourceManager';
import { RemoteResourceManager } from './RemoteResourceManager';
import { ResourceUtils } from './ResourceUtils';

const { ccclass, property } = _decorator;

/**
 * 资源调试组件 - 显示资源加载状态（开发时使用）
 */
@ccclass('ResourceDebug')
export class ResourceDebug extends Component {
    
    @property(Label)
    debugLabel: Label = null;

    @property(Boolean)
    enableDebug: boolean = false; // 在发布版本中设为false

    private updateInterval: number = 0;

    start() {
        if (this.enableDebug && this.debugLabel) {
            this.updateInterval = setInterval(() => {
                this.updateDebugInfo();
            }, 1000); // 每秒更新一次
        }
    }

    onDestroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }

    private updateDebugInfo(): void {
        if (!this.debugLabel) return;

        const stats = ResourceUtils.getResourceStats();
        
        const debugText = `资源状态调试面板
        
本地资源:
- 已加载: ${stats.local.loaded}
- 加载中: ${stats.local.loading}

远程资源:
- 已缓存: ${stats.remote.cached}  
- 加载中: ${stats.remote.loading}

更新时间: ${new Date().toLocaleTimeString()}`;

        this.debugLabel.string = debugText;
    }

    /**
     * 手动触发资源统计
     */
    public showResourceStats(): void {
        const stats = ResourceUtils.getResourceStats();
        console.log('📊 资源加载统计:', stats);
    }

    /**
     * 清理所有缓存
     */
    public clearAllCaches(): void {
        ResourceUtils.clearAllCache();
        console.log('🗑️ 手动清理所有资源缓存');
    }
}