﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///经验转移记录表
    ///</summary>
    [SugarTable("exp_transfer_log")]
    public partial class exp_transfer_log
    {
           public exp_transfer_log(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:源宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int source_pet_id {get;set;}

           /// <summary>
           /// Desc:目标宠物ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int target_pet_id {get;set;}

           /// <summary>
           /// Desc:转移经验值
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long transfer_exp {get;set;}

           /// <summary>
           /// Desc:转移时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? transfer_time {get;set;}

    }
}
