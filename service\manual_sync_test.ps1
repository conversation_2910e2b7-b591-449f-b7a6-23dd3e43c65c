# 手动测试同步功能

Write-Host "=== Manual Sync Test ===" -ForegroundColor Green

Write-Host "Please manually start the project with: dotnet run --project WebApplication_HM.csproj" -ForegroundColor Yellow
Write-Host "Then press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

$baseUrl = "http://localhost:5000"

# 测试连接
Write-Host "`nTesting connection..." -ForegroundColor Cyan
$maxRetries = 5
$retryCount = 0

do {
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/api/DbFirst/entities" -Method GET -TimeoutSec 5
        Write-Host "✅ Connection successful!" -ForegroundColor Green
        $connected = $true
        break
    } catch {
        $retryCount++
        Write-Host "❌ Connection attempt $retryCount failed, retrying..." -ForegroundColor Yellow
        Start-Sleep -Seconds 2
    }
} while ($retryCount -lt $maxRetries)

if (-not $connected) {
    Write-Host "❌ Could not connect to the service. Please ensure the project is running." -ForegroundColor Red
    exit 1
}

# 获取实体信息
Write-Host "`nGetting entity information..." -ForegroundColor Cyan
try {
    $entitiesResponse = Invoke-WebRequest -Uri "$baseUrl/api/DbFirst/entities" -Method GET -TimeoutSec 10
    $entities = $entitiesResponse.Content | ConvertFrom-Json
    
    $userEntity = $entities.data | Where-Object { $_.Name -eq "user" }
    if ($userEntity) {
        Write-Host "✅ Found user entity" -ForegroundColor Green
        Write-Host "Entity properties:" -ForegroundColor White
        $userEntity.Properties | ForEach-Object {
            Write-Host "  - $($_.Name) -> $($_.ColumnName) ($($_.Type))" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ User entity not found" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Failed to get entities: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 检查当前表结构
Write-Host "`nChecking current table structure..." -ForegroundColor Cyan
try {
    $tableResponse = Invoke-WebRequest -Uri "$baseUrl/api/DbFirst/check-table/user" -Method GET -TimeoutSec 10
    $tableInfo = $tableResponse.Content | ConvertFrom-Json
    
    if ($tableInfo.tableExists) {
        Write-Host "✅ User table exists" -ForegroundColor Green
        Write-Host "Current columns ($($tableInfo.columnCount)):" -ForegroundColor White
        $currentColumns = @()
        $tableInfo.columns | ForEach-Object {
            $currentColumns += $_.ColumnName
            Write-Host "  - $($_.ColumnName) ($($_.DataType))" -ForegroundColor Gray
        }
        
        # 分析缺失字段
        Write-Host "`nAnalyzing missing fields..." -ForegroundColor Yellow
        $entityColumns = $userEntity.Properties | ForEach-Object { $_.ColumnName }
        $missingFields = $entityColumns | Where-Object { $currentColumns -notcontains $_ }
        
        if ($missingFields.Count -gt 0) {
            Write-Host "❌ Missing fields found:" -ForegroundColor Red
            $missingFields | ForEach-Object {
                Write-Host "  - $_" -ForegroundColor Red
            }
        } else {
            Write-Host "✅ No missing fields" -ForegroundColor Green
        }
        
    } else {
        Write-Host "❌ User table does not exist" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Failed to check table: $($_.Exception.Message)" -ForegroundColor Red
}

# 执行同步
Write-Host "`nExecuting sync..." -ForegroundColor Cyan
try {
    $syncResponse = Invoke-WebRequest -Uri "$baseUrl/api/DbFirst/sync-user" -Method POST -TimeoutSec 30
    $syncResult = $syncResponse.Content | ConvertFrom-Json
    
    Write-Host "Sync result:" -ForegroundColor White
    Write-Host "  Success: $($syncResult.success)" -ForegroundColor $(if($syncResult.success) { "Green" } else { "Red" })
    Write-Host "  Message: $($syncResult.message)" -ForegroundColor White
    
} catch {
    Write-Host "❌ Sync failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 再次检查表结构
Write-Host "`nChecking table structure after sync..." -ForegroundColor Cyan
try {
    $tableResponseAfter = Invoke-WebRequest -Uri "$baseUrl/api/DbFirst/check-table/user" -Method GET -TimeoutSec 10
    $tableInfoAfter = $tableResponseAfter.Content | ConvertFrom-Json
    
    if ($tableInfoAfter.tableExists) {
        Write-Host "Column count after sync: $($tableInfoAfter.columnCount)" -ForegroundColor White
        
        $columnsAfter = $tableInfoAfter.columns | ForEach-Object { $_.ColumnName }
        $newColumns = $columnsAfter | Where-Object { $currentColumns -notcontains $_ }
        
        if ($newColumns.Count -gt 0) {
            Write-Host "✅ New columns added:" -ForegroundColor Green
            $newColumns | ForEach-Object {
                Write-Host "  + $_" -ForegroundColor Green
            }
        } else {
            Write-Host "ℹ️ No new columns were added" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "❌ Failed to check table after sync: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
