using SqlSugar;
using System;

namespace WebApplication_HM.Models
{
    /// <summary>
    /// 道具丢弃记录表
    /// </summary>
    [SugarTable("item_discard_log")]
    public class item_discard_log
    {
        /// <summary>
        /// 记录ID（主键，自增）
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int log_id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int user_id { get; set; }

        /// <summary>
        /// 原道具序号
        /// </summary>
        public int original_item_seq { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string item_id { get; set; }

        /// <summary>
        /// 道具名称（冗余存储，方便查询）
        /// </summary>
        public string item_name { get; set; }

        /// <summary>
        /// 丢弃的道具数量
        /// </summary>
        public long discard_count { get; set; }

        /// <summary>
        /// 丢弃原因
        /// </summary>
        public string discard_reason { get; set; }

        /// <summary>
        /// 丢弃时间
        /// </summary>
        public DateTime discard_time { get; set; }

        /// <summary>
        /// 是否已找回（0=未找回，1=已找回）
        /// </summary>
        public int is_recovered { get; set; } = 0;

        /// <summary>
        /// 找回时间
        /// </summary>
        public DateTime? recover_time { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
    }
}
