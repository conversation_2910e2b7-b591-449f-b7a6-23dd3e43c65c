using Microsoft.Extensions.Caching.Memory;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using SqlSugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 合成成长计算器实现
    /// </summary>
    public class SynthesisGrowthCalculator : ISynthesisGrowthCalculator
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<SynthesisGrowthCalculator> _logger;
        private readonly Random _random;

        public SynthesisGrowthCalculator(
            DbContext dbContext,
            ILogger<SynthesisGrowthCalculator> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
            _random = new Random();
        }

        /// <summary>
        /// 计算成长增加值
        /// </summary>
        public async Task<decimal> CalculateGrowthIncreaseAsync(SynthesisContext context)
        {
            try
            {
                // 1. 计算成长限制系数
                var growthLimitFactor = CalculateGrowthLimitFactor(context.MainPet.growth ?? 0);

                // 2. 计算基础成长增加
                var baseGrowthIncrease = CalculateBaseGrowthIncrease(context);

                // 3. 应用VIP加成
                var vipMultiplier = await GetVipMultiplierAsync(context.UserId);

                // 4. 应用道具加成
                var itemMultiplier = await CalculateItemMultiplierAsync(context.UsedItems);

                // 5. 应用效果系数
                var effectMultiplier = await GetEffectMultiplierAsync();

                // 6. 应用公式加成
                var formulaMultiplier = 1m + ((context.Formula?.growth_bonus ?? 0) / 100m);

                // 7. 最终计算
                var finalGrowthIncrease = baseGrowthIncrease * vipMultiplier * itemMultiplier * effectMultiplier * formulaMultiplier;

                _logger.LogInformation($"成长计算详情: 基础={baseGrowthIncrease}, VIP={vipMultiplier}, 道具={itemMultiplier}, 效果={effectMultiplier}, 公式={formulaMultiplier}, 最终={finalGrowthIncrease}");

                return Math.Round(finalGrowthIncrease, 6);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算成长增加值异常");
                return 0;
            }
        }

        /// <summary>
        /// 计算成长限制系数
        /// </summary>
        public decimal CalculateGrowthLimitFactor(decimal currentGrowth)
        {
            if (currentGrowth <= 60)
            {
                return currentGrowth * 10 + 400;
            }
            else if (currentGrowth <= 80)
            {
                return (currentGrowth - 60) * 35 + 1000;
            }
            else
            {
                return (currentGrowth - 60) * 60 + 500;
            }
        }

        /// <summary>
        /// 计算基础成长增加
        /// </summary>
        public decimal CalculateBaseGrowthIncrease(SynthesisContext context)
        {
            var growthLimitFactor = CalculateGrowthLimitFactor(context.MainPet.growth ?? 0);
            var viceGrowth = context.VicePet.growth ?? 0;
            
            // 基础成长计算公式 (简化版本)
            var baseIncrease = (viceGrowth / 10m) * (growthLimitFactor / 1000m);
            
            // 添加随机因子
            var randomFactor = (decimal)(_random.NextDouble() * 0.5 + 0.75); // 0.75-1.25的随机系数
            
            return baseIncrease * randomFactor;
        }

        /// <summary>
        /// 计算VIP加成系数
        /// </summary>
        public async Task<decimal> GetVipMultiplierAsync(int userId)
        {
            try
            {
                var user = await _dbContext.Db.Queryable<user>().FirstAsync(x => x.id == userId);
                
                var normalBonus = await GetConfigValueAsync<decimal>("synthesis.vip_growth_bonus");
                var supremeBonus = await GetConfigValueAsync<decimal>("synthesis.supreme_vip_growth_bonus");
                
                var isSupreme = user.supreme_vip ?? false;
                var bonusRate = isSupreme ? supremeBonus : normalBonus;

                return 1m + bonusRate * (user.vip_level ?? 0);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算VIP加成异常");
                return 1m;
            }
        }

        /// <summary>
        /// 计算道具加成系数
        /// </summary>
        public async Task<decimal> CalculateItemMultiplierAsync(List<string> usedItems)
        {
            try
            {
                decimal totalMultiplier = 1m;
                
                foreach (var itemId in usedItems)
                {
                    var itemConfig = await _dbContext.Db.Queryable<item_config>()
                        .FirstAsync(x => x.item_no.ToString() == itemId);
                    
                    if (itemConfig != null && !string.IsNullOrEmpty(itemConfig.script))
                    {
                        // 解析道具脚本获取加成
                        var bonus = ParseItemGrowthBonus(itemConfig.script ?? "");
                        totalMultiplier += bonus;
                    }
                }
                
                return totalMultiplier;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算道具加成异常");
                return 1m;
            }
        }

        /// <summary>
        /// 获取效果系数
        /// </summary>
        public async Task<decimal> GetEffectMultiplierAsync()
        {
            return await GetConfigValueAsync<decimal>("synthesis.effect_multiplier");
        }

        /// <summary>
        /// 解析道具成长加成
        /// </summary>
        private decimal ParseItemGrowthBonus(string script)
        {
            try
            {
                if (string.IsNullOrEmpty(script)) return 0;
                
                // 简化的脚本解析
                if (script.Contains("成长加成"))
                {
                    var parts = script.Split('|');
                    for (int i = 0; i < parts.Length - 1; i++)
                    {
                        if (parts[i].Contains("成长加成") && decimal.TryParse(parts[i + 1], out var bonus))
                        {
                            return bonus;
                        }
                    }
                }
                
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        private async Task<T> GetConfigValueAsync<T>(string configKey)
        {
            try
            {
                var config = await _dbContext.Db.Queryable<pet_synthesis_config>()
                    .FirstAsync(x => x.config_key == configKey && x.is_active == true);

                if (config == null) return default(T);

                return config.config_type.ToUpper() switch
                {
                    "INTEGER" => (T)(object)int.Parse(config.config_value),
                    "LONG" => (T)(object)long.Parse(config.config_value),
                    "DECIMAL" => (T)(object)decimal.Parse(config.config_value),
                    "BOOLEAN" => (T)(object)bool.Parse(config.config_value),
                    _ => (T)(object)config.config_value
                };
            }
            catch
            {
                return default(T);
            }
        }
    }

    /// <summary>
    /// 神宠合成计算器实现
    /// </summary>
    public class GodPetSynthesisCalculator : IGodPetSynthesisCalculator
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<GodPetSynthesisCalculator> _logger;
        private readonly Random _random;

        public GodPetSynthesisCalculator(
            DbContext dbContext,
            ILogger<GodPetSynthesisCalculator> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
            _random = new Random();
        }

        /// <summary>
        /// 判断是否应该合成神宠
        /// </summary>
        public async Task<bool> ShouldBecomeGodPetAsync(decimal currentGrowth)
        {
            try
            {
                var threshold = await GetConfigValueAsync<decimal>("synthesis.god_pet_threshold");
                
                if (currentGrowth <= threshold) return false;

                var probability = await CalculateGodPetProbabilityAsync(currentGrowth);
                var randomValue = _random.NextDouble() * 100;

                return randomValue <= (double)probability;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "判断神宠合成异常");
                return false;
            }
        }

        /// <summary>
        /// 选择神宠
        /// </summary>
        public async Task<int> SelectGodPetAsync()
        {
            try
            {
                var godPets = await _dbContext.Db.Queryable<pet_config>()
                    .Where(p => p.attribute == "神" && (p.is_active == true || p.is_active == null))
                    .ToListAsync();

                var holyPets = await _dbContext.Db.Queryable<pet_config>()
                    .Where(p => p.attribute == "神圣" && (p.is_active == true || p.is_active == null))
                    .ToListAsync();

                // 1/100 概率获得神圣宠物
                if (_random.Next(1, 101) == 77 && holyPets.Any())
                {
                    var selectedHoly = holyPets[_random.Next(holyPets.Count)];
                    _logger.LogInformation($"合成出神圣宠物: {selectedHoly.name}");
                    return selectedHoly.pet_no;
                }

                // 否则获得神宠
                if (godPets.Any())
                {
                    var selectedGod = godPets[_random.Next(godPets.Count)];
                    _logger.LogInformation($"合成出神宠: {selectedGod.name}");
                    return selectedGod.pet_no;
                }

                return 0; // 没有可用的神宠
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择神宠异常");
                return 0;
            }
        }

        /// <summary>
        /// 计算神宠概率
        /// </summary>
        public async Task<decimal> CalculateGodPetProbabilityAsync(decimal currentGrowth)
        {
            try
            {
                var threshold = await GetConfigValueAsync<decimal>("synthesis.god_pet_threshold");
                var maxRate = await GetConfigValueAsync<int>("synthesis.god_pet_max_rate");
                
                if (currentGrowth <= threshold) return 0;

                if (currentGrowth > 185)
                {
                    return maxRate;
                }
                else
                {
                    return Math.Min(maxRate, (currentGrowth - threshold) / 4);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算神宠概率异常");
                return 0;
            }
        }

        /// <summary>
        /// 应用神宠成长限制
        /// </summary>
        public decimal ApplyGodPetGrowthLimit(decimal currentGrowth, decimal growthIncrease)
        {
            var newGrowth = currentGrowth + growthIncrease;
            
            if (newGrowth >= 300)
            {
                return 100;
            }
            else if (newGrowth >= 60)
            {
                return 60 + (newGrowth - 60) / 6;
            }
            else
            {
                return newGrowth;
            }
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        private async Task<T> GetConfigValueAsync<T>(string configKey)
        {
            try
            {
                var config = await _dbContext.Db.Queryable<pet_synthesis_config>()
                    .FirstAsync(x => x.config_key == configKey && x.is_active == true);

                if (config == null) return default(T);

                return config.config_type.ToUpper() switch
                {
                    "INTEGER" => (T)(object)int.Parse(config.config_value),
                    "LONG" => (T)(object)long.Parse(config.config_value),
                    "DECIMAL" => (T)(object)decimal.Parse(config.config_value),
                    "BOOLEAN" => (T)(object)bool.Parse(config.config_value),
                    _ => (T)(object)config.config_value
                };
            }
            catch
            {
                return default(T);
            }
        }
    }
}
