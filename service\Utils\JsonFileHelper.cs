using System.Text.Json;

namespace WebApplication_HM.Utils
{
    /// <summary>
    /// JSON文件处理工具类
    /// </summary>
    public static class JsonFileHelper
    {
        /// <summary>
        /// 从JSON文件加载数据
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="filePath">JSON文件路径</param>
        /// <returns>反序列化后的对象</returns>
        public static T LoadJson<T>(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"未找到JSON文件: {filePath}");
                }

                string jsonContent = File.ReadAllText(filePath);
                return JsonSerializer.Deserialize<T>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"加载JSON文件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存数据到JSON文件
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要保存的数据</param>
        /// <param name="filePath">保存路径</param>
        /// <param name="formatted">是否格式化JSON（默认true）</param>
        public static void SaveJson<T>(T data, string filePath, bool formatted = true)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = formatted,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                string jsonContent = JsonSerializer.Serialize(data, options);
                string directory = Path.GetDirectoryName(filePath);
                
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(filePath, jsonContent);
            }
            catch (Exception ex)
            {
                throw new Exception($"保存JSON文件时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证JSON文件是否存在且格式正确
        /// </summary>
        /// <typeparam name="T">预期的数据类型</typeparam>
        /// <param name="filePath">JSON文件路径</param>
        /// <returns>true表示文件存在且格式正确</returns>
        public static bool ValidateJsonFile<T>(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return false;
                }

                string jsonContent = File.ReadAllText(filePath);
                JsonSerializer.Deserialize<T>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 