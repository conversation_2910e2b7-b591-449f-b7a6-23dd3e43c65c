using Microsoft.Extensions.Caching.Memory;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;
using SqlSugar;
using System.Text.Json;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 宠物合成服务实现
    /// </summary>
    public class PetSynthesisService : IPetSynthesisService
    {
        private readonly DbContext _dbContext;
        private readonly ILogger<PetSynthesisService> _logger;
        private readonly Random _random;
        private readonly IMemoryCache _cache;
        private readonly ISynthesisFormulaService _formulaService;
        private readonly ISynthesisGrowthCalculator _growthCalculator;
        private readonly IGodPetSynthesisCalculator _godPetCalculator;

        // 合成CD时间记录
        private static readonly Dictionary<int, long> _lastSynthesisTime = new Dictionary<int, long>();

        public PetSynthesisService(
            DbContext dbContext,
            ILogger<PetSynthesisService> logger,
            IMemoryCache cache,
            ISynthesisFormulaService formulaService,
            ISynthesisGrowthCalculator growthCalculator,
            IGodPetSynthesisCalculator godPetCalculator)
        {
            _dbContext = dbContext;
            _logger = logger;
            _random = new Random();
            _cache = cache;
            _formulaService = formulaService;
            _growthCalculator = growthCalculator;
            _godPetCalculator = godPetCalculator;
        }

        /// <summary>
        /// 执行宠物合成
        /// </summary>
        public async Task<SynthesisResultDto> SynthesizePetAsync(int userId, SynthesisRequestDto request)
        {
            try
            {
                _logger.LogInformation($"开始执行宠物合成 - 用户ID: {userId}, 主宠ID: {request.MainPetId}, 副宠ID: {request.VicePetId}");

                // 1. 验证合成条件
                var (isValid, errorMessage) = await ValidateSynthesisConditionsAsync(userId, request.MainPetId, request.VicePetId);
                if (!isValid)
                {
                    return new SynthesisResultDto { Success = false, Message = errorMessage };
                }

                // 2. 构建合成上下文
                var context = await BuildSynthesisContextAsync(userId, request);
                if (context == null)
                {
                    return new SynthesisResultDto { Success = false, Message = "获取合成信息失败！" };
                }

                // 3. 执行合成逻辑
                return await ExecuteSynthesisAsync(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"宠物合成异常: {ex.Message}");
                return new SynthesisResultDto { Success = false, Message = "合成失败，请稍后重试！" };
            }
        }

        /// <summary>
        /// 获取合成配置信息
        /// </summary>
        public async Task<SynthesisConfigDto> GetSynthesisConfigAsync(int userId, int mainPetId, int vicePetId)
        {
            var context = await BuildSynthesisContextAsync(userId, new SynthesisRequestDto 
            { 
                MainPetId = mainPetId, 
                VicePetId = vicePetId 
            });

            if (context == null) return null;

            var config = new SynthesisConfigDto
            {
                MainPet = new PetSynthesisInfoDto
                {
                    PetId = context.MainPet.id,
                    PetName = context.MainPet.custom_name ?? context.MainPetConfig?.name ?? "未知宠物",
                    PetNo = context.MainPet.pet_no,
                    Level = CalculateLevel(context.MainPet.exp ?? 0),
                    Growth = context.MainPet.growth ?? 0,
                    Element = context.MainPet.element,
                    IsFiveElement = IsFiveElement(context.MainPet.element)
                },
                VicePet = new PetSynthesisInfoDto
                {
                    PetId = context.VicePet.id,
                    PetName = context.VicePet.custom_name ?? context.VicePetConfig?.name ?? "未知宠物",
                    PetNo = context.VicePet.pet_no,
                    Level = CalculateLevel(context.VicePet.exp ?? 0),
                    Growth = context.VicePet.growth ?? 0,
                    Element = context.VicePet.element,
                    IsFiveElement = IsFiveElement(context.VicePet.element)
                },
                BaseSuccessRate = await GetConfigValueAsync<decimal>("synthesis.base_success_rate"),
                ActualSuccessRate = await CalculateSuccessRateAsync(context),
                CostGold = await GetConfigValueAsync<long>("synthesis.cost_gold"),
                GodPetProbability = await CalculateGodPetProbabilityAsync(context.MainPet.growth ?? 0),
                VipBonus = await GetVipBonusAsync(userId)
            };

            // 验证是否可以合成
            var (canSynthesize, reason) = await ValidateSynthesisConditionsAsync(userId, mainPetId, vicePetId);
            config.CanSynthesize = canSynthesize;
            config.CannotSynthesizeReason = reason;

            // 获取匹配的公式
            if (context.Formula != null)
            {
                var resultPetConfig = await _dbContext.Db.Queryable<pet_config>()
                    .FirstAsync(x => x.pet_no == context.Formula.result_pet_no);

                config.MatchedFormula = new SynthesisFormulaDto
                {
                    FormulaId = context.Formula.id,
                    MainPetNo = context.Formula.main_pet_no,
                    VicePetNo = context.Formula.vice_pet_no,
                    MainGrowthRequired = context.Formula.main_growth_required ?? 0,
                    ViceGrowthRequired = context.Formula.vice_growth_required ?? 0,
                    ResultPetNo = context.Formula.result_pet_no,
                    ResultPetName = resultPetConfig?.name ?? "未知宠物",
                    SuccessRateBonus = context.Formula.success_rate_bonus ?? 0,
                    GrowthBonus = context.Formula.growth_bonus ?? 0
                };
            }

            return config;
        }

        /// <summary>
        /// 验证合成条件
        /// </summary>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateSynthesisConditionsAsync(int userId, int mainPetId, int vicePetId)
        {
            try
            {
                // 1. CD时间检查
                var cdTime = await GetConfigValueAsync<long>("synthesis.cd_time");
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                
                if (_lastSynthesisTime.ContainsKey(userId))
                {
                    var timeDiff = currentTime - _lastSynthesisTime[userId];
                    if (timeDiff < cdTime)
                    {
                        return (false, "合成CD未到!");
                    }
                }

                // 2. 获取宠物信息
                var mainPet = await GetUserPetAsync(userId, mainPetId);
                var vicePet = await GetUserPetAsync(userId, vicePetId);

                if (mainPet == null) return (false, "主宠不存在或不属于您！");
                if (vicePet == null) return (false, "副宠不存在或不属于您！");

                // 3. 五行限制检查
                if (!IsFiveElement(mainPet.element))
                {
                    return (false, "合成失败,不能放入非五系宠!");
                }
                if (!IsFiveElement(vicePet.element))
                {
                    return (false, "合成失败,不能放入非五系宠!");
                }

                // 4. 等级要求检查
                var minLevel = await GetConfigValueAsync<int>("synthesis.min_level");
                var mainLevel = CalculateLevel(mainPet.exp ?? 0);
                var viceLevel = CalculateLevel(vicePet.exp ?? 0);

                if (mainLevel < minLevel)
                {
                    return (false, "主宠等级没有达到40级!");
                }
                if (viceLevel < minLevel)
                {
                    return (false, "副宠等级没有达到40级!");
                }

                // 5. 金币检查
                var user = await _dbContext.Db.Queryable<user>().FirstAsync(x => x.id == userId);
                var costGold = await GetConfigValueAsync<long>("synthesis.cost_gold");
                
                if (user.gold < costGold)
                {
                    return (false, "金币不足!合成一次需要50000金币噢!");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证合成条件异常");
                return (false, "验证合成条件失败！");
            }
        }

        /// <summary>
        /// 查找合成公式
        /// </summary>
        public async Task<pet_synthesis_formula> FindFormulaAsync(int mainPetNo, int vicePetNo, decimal mainGrowth, decimal viceGrowth)
        {
            return await _formulaService.FindMatchingFormulaAsync(mainPetNo, vicePetNo, mainGrowth, viceGrowth);
        }

        /// <summary>
        /// 计算合成成功率
        /// </summary>
        public async Task<decimal> CalculateSuccessRateAsync(SynthesisContext context)
        {
            var baseSuccessRate = await GetConfigValueAsync<decimal>("synthesis.base_success_rate");
            
            // 公式加成
            var formulaBonus = (context.Formula?.success_rate_bonus ?? 0);
            
            // 道具加成 (暂时简化处理)
            var itemBonus = 0m;
            
            var totalSuccessRate = baseSuccessRate + formulaBonus + itemBonus;
            
            return Math.Min(100, Math.Max(0, totalSuccessRate));
        }

        /// <summary>
        /// 计算成长增加值
        /// </summary>
        public async Task<decimal> CalculateGrowthIncreaseAsync(SynthesisContext context)
        {
            return await _growthCalculator.CalculateGrowthIncreaseAsync(context);
        }

        /// <summary>
        /// 计算神宠合成概率
        /// </summary>
        public async Task<decimal> CalculateGodPetProbabilityAsync(decimal currentGrowth)
        {
            return await _godPetCalculator.CalculateGodPetProbabilityAsync(currentGrowth);
        }

        /// <summary>
        /// 获取系统配置值
        /// </summary>
        public async Task<T> GetConfigValueAsync<T>(string configKey)
        {
            var cacheKey = $"synthesis_config_{configKey}";
            
            if (_cache.TryGetValue(cacheKey, out T cachedValue))
            {
                return cachedValue;
            }

            var config = await _dbContext.Db.Queryable<pet_synthesis_config>()
                .FirstAsync(x => x.config_key == configKey && x.is_active == true);

            if (config == null)
            {
                return default(T);
            }

            var value = ConvertConfigValue<T>(config.config_value, config.config_type);
            _cache.Set(cacheKey, value, TimeSpan.FromMinutes(30));
            
            return value;
        }

        /// <summary>
        /// 获取VIP加成信息
        /// </summary>
        public async Task<VipBonusDto> GetVipBonusAsync(int userId)
        {
            var user = await _dbContext.Db.Queryable<user>().FirstAsync(x => x.id == userId);
            
            var normalBonus = await GetConfigValueAsync<decimal>("synthesis.vip_growth_bonus");
            var supremeBonus = await GetConfigValueAsync<decimal>("synthesis.supreme_vip_growth_bonus");
            
            var isSupreme = user.supreme_vip ?? false;
            var bonusPercent = isSupreme ? supremeBonus * (user.vip_level ?? 0) : normalBonus * (user.vip_level ?? 0);
            
            return new VipBonusDto
            {
                VipLevel = user.vip_level ?? 0,
                IsSupremeVip = isSupreme,
                GrowthBonusPercent = bonusPercent * 100,
                BonusDescription = $"{(isSupreme ? "至尊" : "普通")}VIP{user.vip_level ?? 0}级，成长加成{bonusPercent * 100:F1}%"
            };
        }

        /// <summary>
        /// 获取合成历史
        /// </summary>
        public async Task<List<SynthesisHistoryDto>> GetSynthesisHistoryAsync(int userId, int? petId = null)
        {
            var query = _dbContext.Db.Queryable<pet_synthesis_log>()
                .Where(x => x.user_id == userId);

            if (petId.HasValue)
            {
                query = query.Where(x => x.main_pet_id == petId.Value);
            }

            var logs = await query.OrderBy(x => x.create_time, OrderByType.Desc)
                .Take(50)
                .ToListAsync();

            var result = new List<SynthesisHistoryDto>();
            
            foreach (var log in logs)
            {
                var beforePetConfig = await _dbContext.Db.Queryable<pet_config>()
                    .FirstAsync(x => x.pet_no == log.main_pet_no);

                var afterPetConfig = log.result_pet_no.HasValue ?
                    await _dbContext.Db.Queryable<pet_config>()
                        .FirstAsync(x => x.pet_no == log.result_pet_no.Value) : null;

                var usedItems = new List<string>();
                if (!string.IsNullOrEmpty(log.used_items))
                {
                    try
                    {
                        usedItems = JsonSerializer.Deserialize<List<string>>(log.used_items) ?? new List<string>();
                    }
                    catch
                    {
                        // 忽略JSON解析错误
                    }
                }

                result.Add(new SynthesisHistoryDto
                {
                    Id = log.id,
                    BeforeGrowth = log.before_main_growth ?? 0,
                    AfterGrowth = log.after_main_growth ?? 0,
                    GrowthIncrease = log.growth_increase ?? 0,
                    BeforePetName = beforePetConfig?.name ?? "未知宠物",
                    AfterPetName = afterPetConfig?.name ?? "未知宠物",
                    CostGold = log.cost_gold,
                    UsedItems = usedItems,
                    IsSuccess = log.is_success,
                    IsGodPet = log.is_god_pet ?? false,
                    SuccessRate = log.success_rate,
                    UsedFormula = log.formula_id.HasValue,
                    CreateTime = log.create_time ?? DateTime.Now
                });
            }

            return result;
        }

        /// <summary>
        /// 获取合成统计
        /// </summary>
        public async Task<SynthesisStatisticsDto> GetSynthesisStatisticsAsync(int userId)
        {
            var logs = await _dbContext.Db.Queryable<pet_synthesis_log>()
                .Where(x => x.user_id == userId)
                .ToListAsync();

            var totalCount = logs.Count;
            var successCount = logs.Count(x => x.is_success);
            var failureCount = totalCount - successCount;
            var godPetCount = logs.Count(x => x.is_god_pet == true);
            var formulaUsageCount = logs.Count(x => x.formula_id.HasValue);

            var successLogs = logs.Where(x => x.is_success && x.growth_increase.HasValue).ToList();
            var avgGrowthIncrease = successLogs.Any() ? successLogs.Average(x => x.growth_increase.Value) : 0;
            var maxGrowthIncrease = successLogs.Any() ? successLogs.Max(x => x.growth_increase.Value) : 0;

            return new SynthesisStatisticsDto
            {
                TotalSynthesis = totalCount,
                SuccessCount = successCount,
                FailureCount = failureCount,
                SuccessRate = totalCount > 0 ? (decimal)successCount / totalCount * 100 : 0,
                GodPetCount = godPetCount,
                GodPetRate = successCount > 0 ? (decimal)godPetCount / successCount * 100 : 0,
                TotalCostGold = logs.Sum(x => x.cost_gold),
                AverageGrowthIncrease = avgGrowthIncrease,
                MaxGrowthIncrease = maxGrowthIncrease,
                FormulaUsageCount = formulaUsageCount
            };
        }

        #region 私有方法

        /// <summary>
        /// 构建合成上下文
        /// </summary>
        private async Task<SynthesisContext> BuildSynthesisContextAsync(int userId, SynthesisRequestDto request)
        {
            var mainPet = await GetUserPetAsync(userId, request.MainPetId);
            var vicePet = await GetUserPetAsync(userId, request.VicePetId);
            
            if (mainPet == null || vicePet == null) return null;

            var user = await _dbContext.Db.Queryable<user>().FirstAsync(x => x.id == userId);
            var mainPetConfig = await _dbContext.Db.Queryable<pet_config>().FirstAsync(x => x.pet_no == mainPet.pet_no);
            var vicePetConfig = await _dbContext.Db.Queryable<pet_config>().FirstAsync(x => x.pet_no == vicePet.pet_no);
            
            var formula = await FindFormulaAsync(mainPet.pet_no, vicePet.pet_no, 
                mainPet.growth ?? 0, vicePet.growth ?? 0);

            return new SynthesisContext
            {
                UserId = userId,
                MainPet = mainPet,
                VicePet = vicePet,
                Formula = formula,
                UsedItems = request.UsedItems ?? new List<string>(),
                UserInfo = user,
                MainPetConfig = mainPetConfig,
                VicePetConfig = vicePetConfig
            };
        }

        /// <summary>
        /// 执行合成逻辑
        /// </summary>
        private async Task<SynthesisResultDto> ExecuteSynthesisAsync(SynthesisContext context)
        {
            _dbContext.Db.Ado.BeginTran();
            try
            {
                // 更新CD时间
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                _lastSynthesisTime[context.UserId] = currentTime;

                // 1. 消耗金币
                var costGold = await GetConfigValueAsync<long>("synthesis.cost_gold");
                await ConsumeGoldAsync(context.UserId, costGold);

                // 2. 消耗道具
                await ConsumeItemsAsync(context.UserId, context.UsedItems);

                // 3. 计算成功率
                var successRate = await CalculateSuccessRateAsync(context);

                // 4. 判断是否成功
                var randomValue = _random.NextDouble() * 100;
                var isSuccess = randomValue < (double)successRate;

                if (!isSuccess)
                {
                    // 合成失败，删除副宠
                    await DeletePetAsync(context.VicePet.id);
                    await LogSynthesisAsync(context, false, 0, 0, successRate);
                    
                    _dbContext.Db.Ado.CommitTran();
                    return new SynthesisResultDto 
                    { 
                        Success = false, 
                        Message = "合成失败！",
                        CostGold = costGold,
                        SuccessRate = successRate,
                        BeforePetNo = context.MainPet.pet_no,
                        AfterPetNo = context.MainPet.pet_no,
                        UsedItems = context.UsedItems
                    };
                }

                // 5. 计算成长增加
                var growthIncrease = await CalculateGrowthIncreaseAsync(context);

                // 6. 确定合成结果
                var resultPetNo = await DetermineResultPetAsync(context);
                var isGodPet = IsGodElement(resultPetNo);

                // 7. 更新主宠数据
                await UpdateMainPetAsync(context.MainPet, resultPetNo, growthIncrease);

                // 8. 删除副宠
                await DeletePetAsync(context.VicePet.id);

                // 9. 记录合成日志
                await LogSynthesisAsync(context, true, growthIncrease, resultPetNo, successRate);

                _dbContext.Db.Ado.CommitTran();

                return new SynthesisResultDto
                {
                    Success = true,
                    Message = "恭喜您！合成成功！",
                    GrowthIncrease = growthIncrease,
                    NewGrowth = (context.MainPet.growth ?? 0) + growthIncrease,
                    BeforePetNo = context.MainPet.pet_no,
                    AfterPetNo = resultPetNo,
                    CostGold = costGold,
                    UsedItems = context.UsedItems,
                    IsGodPet = isGodPet,
                    SuccessRate = successRate,
                    UsedFormula = context.Formula != null,
                    FormulaId = context.Formula?.id
                };
            }
            catch (Exception ex)
            {
                _dbContext.Db.Ado.RollbackTran();
                _logger.LogError(ex, "合成事务回滚");
                throw;
            }
        }

        /// <summary>
        /// 获取用户宠物
        /// </summary>
        private async Task<user_pet> GetUserPetAsync(int userId, int petId)
        {
            return await _dbContext.Db.Queryable<user_pet>()
                .FirstAsync(x => x.id == petId && x.user_id == userId && x.state != -1);
        }

        /// <summary>
        /// 判断是否为五系宠物
        /// </summary>
        private bool IsFiveElement(string element)
        {
            if (string.IsNullOrEmpty(element)) return false;
            
            if (Enum.TryParse<ElementType>(element, out var elementType))
            {
                return (int)elementType <= 5;
            }
            
            return false;
        }

        /// <summary>
        /// 判断是否为神系宠物
        /// </summary>
        private bool IsGodElement(int petNo)
        {
            // 简化实现，实际应该查询宠物配置
            return petNo >= 6000; // 假设神宠编号从6000开始
        }

        /// <summary>
        /// 计算宠物等级
        /// </summary>
        private int CalculateLevel(long exp)
        {
            // 简化的等级计算
            if (exp < 1000) return 1;
            if (exp < 5000) return 10;
            if (exp < 20000) return 20;
            if (exp < 50000) return 30;
            if (exp < 100000) return 40;
            if (exp < 200000) return 50;
            if (exp < 400000) return 60;
            if (exp < 800000) return 70;
            if (exp < 1600000) return 80;
            if (exp < 3200000) return 90;
            return 100;
        }

        /// <summary>
        /// 转换配置值
        /// </summary>
        private T ConvertConfigValue<T>(string value, string type)
        {
            try
            {
                return type.ToUpper() switch
                {
                    "INTEGER" => (T)(object)int.Parse(value),
                    "LONG" => (T)(object)long.Parse(value),
                    "DECIMAL" => (T)(object)decimal.Parse(value),
                    "BOOLEAN" => (T)(object)bool.Parse(value),
                    _ => (T)(object)value
                };
            }
            catch
            {
                return default(T);
            }
        }

        /// <summary>
        /// 消耗金币
        /// </summary>
        private async Task ConsumeGoldAsync(int userId, long goldAmount)
        {
            await _dbContext.Db.Updateable<user>()
                .SetColumns(x => new user { gold = x.gold - goldAmount })
                .Where(x => x.id == userId)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 消耗道具
        /// </summary>
        private async Task ConsumeItemsAsync(int userId, List<string> usedItems)
        {
            foreach (var itemId in usedItems)
            {
                await _dbContext.Db.Updateable<user_item>()
                    .SetColumns(x => new user_item { item_count = x.item_count - 1 })
                    .Where(x => x.user_id == userId && x.item_id == itemId)
                    .ExecuteCommandAsync();
            }
        }

        /// <summary>
        /// 确定合成结果
        /// </summary>
        private async Task<int> DetermineResultPetAsync(SynthesisContext context)
        {
            // 1. 检查是否应该合成神宠
            var shouldBecomeGodPet = await _godPetCalculator.ShouldBecomeGodPetAsync(context.MainPet.growth ?? 0);
            
            if (shouldBecomeGodPet)
            {
                var godPetNo = await _godPetCalculator.SelectGodPetAsync();
                if (godPetNo > 0) return godPetNo;
            }

            // 2. 使用公式结果
            if (context.Formula != null)
            {
                return context.Formula.result_pet_no;
            }

            // 3. 默认保持原宠物
            return context.MainPet.pet_no;
        }

        /// <summary>
        /// 更新主宠数据
        /// </summary>
        private async Task UpdateMainPetAsync(user_pet mainPet, int resultPetNo, decimal growthIncrease)
        {
            var newGrowth = (mainPet.growth ?? 0) + growthIncrease;
            
            // 如果是神宠，应用神宠成长限制
            if (IsGodElement(resultPetNo))
            {
                newGrowth = _godPetCalculator.ApplyGodPetGrowthLimit(mainPet.growth ?? 0, growthIncrease);
            }

            await _dbContext.Db.Updateable<user_pet>()
                .SetColumns(x => new user_pet
                {
                    growth = newGrowth,
                    synthesis_count = (x.synthesis_count ?? 0) + 1,
                    pet_no = resultPetNo,
                    image = resultPetNo
                })
                .Where(x => x.id == mainPet.id)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 删除宠物
        /// </summary>
        private async Task DeletePetAsync(int petId)
        {
            await _dbContext.Db.Updateable<user_pet>()
                .SetColumns(x => new user_pet { state = -1 })
                .Where(x => x.id == petId)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 记录合成日志
        /// </summary>
        private async Task LogSynthesisAsync(SynthesisContext context, bool isSuccess, decimal growthIncrease, int resultPetNo, decimal successRate)
        {
            var log = new pet_synthesis_log
            {
                user_id = context.UserId,
                main_pet_id = context.MainPet.id,
                vice_pet_id = context.VicePet.id,
                formula_id = context.Formula?.id,
                before_main_growth = context.MainPet.growth ?? 0,
                after_main_growth = isSuccess ? (context.MainPet.growth ?? 0) + growthIncrease : context.MainPet.growth ?? 0,
                growth_increase = isSuccess ? growthIncrease : 0,
                before_pet_no = context.MainPet.pet_no.ToString(),
                after_pet_no = isSuccess ? resultPetNo.ToString() : context.MainPet.pet_no.ToString(),
                used_items = context.UsedItems.Any() ? JsonSerializer.Serialize(context.UsedItems) : null,
                cost_gold = await GetConfigValueAsync<long>("synthesis.cost_gold"),
                is_success = isSuccess,
                is_god_pet = isSuccess && IsGodElement(resultPetNo),
                success_rate = successRate,
                create_time = DateTime.Now
            };

            await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
        }

        #endregion
    }

    /// <summary>
    /// 合成公式服务实现
    /// </summary>
    public class SynthesisFormulaService : ISynthesisFormulaService
    {
        private readonly DbContext _dbContext;
        private readonly IMemoryCache _cache;
        private readonly ILogger<SynthesisFormulaService> _logger;

        public SynthesisFormulaService(
            DbContext dbContext,
            IMemoryCache cache,
            ILogger<SynthesisFormulaService> logger)
        {
            _dbContext = dbContext;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// 查找匹配的公式
        /// </summary>
        public async Task<pet_synthesis_formula> FindMatchingFormulaAsync(int mainPetNo, int vicePetNo, decimal mainGrowth, decimal viceGrowth)
        {
            try
            {
                // 1. 尝试从缓存获取
                var cacheKey = $"formula_{mainPetNo}_{vicePetNo}";
                if (_cache.TryGetValue(cacheKey, out List<pet_synthesis_formula> cachedFormulas))
                {
                    return FindMatchingFormula(cachedFormulas, mainGrowth, viceGrowth);
                }

                // 2. 从数据库查询
                var formulas = await _dbContext.Db.Queryable<pet_synthesis_formula>()
                    .Where(f => f.main_pet_no == mainPetNo &&
                               (f.vice_pet_no == vicePetNo || f.vice_pet_no == -1) &&
                               f.is_active == true)
                    .OrderBy(f => f.vice_pet_no, OrderByType.Desc) // 优先匹配具体副宠
                    .ToListAsync();

                // 3. 缓存结果
                _cache.Set(cacheKey, formulas, TimeSpan.FromMinutes(30));

                // 4. 查找匹配的公式
                return FindMatchingFormula(formulas, mainGrowth, viceGrowth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"查找合成公式异常: 主宠{mainPetNo}, 副宠{vicePetNo}");
                return null;
            }
        }

        /// <summary>
        /// 获取所有公式
        /// </summary>
        public async Task<List<pet_synthesis_formula>> GetAllFormulasAsync()
        {
            return await _dbContext.Db.Queryable<pet_synthesis_formula>()
                .Where(f => f.is_active == true)
                .OrderBy(f => f.main_pet_no)
                .ToListAsync();
        }

        /// <summary>
        /// 添加公式
        /// </summary>
        public async Task<bool> AddFormulaAsync(pet_synthesis_formula formula)
        {
            try
            {
                await _dbContext.Db.Insertable(formula).ExecuteCommandAsync();
                await ClearFormulaCacheAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加合成公式异常");
                return false;
            }
        }

        /// <summary>
        /// 更新公式
        /// </summary>
        public async Task<bool> UpdateFormulaAsync(pet_synthesis_formula formula)
        {
            try
            {
                await _dbContext.Db.Updateable(formula).ExecuteCommandAsync();
                await ClearFormulaCacheAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新合成公式异常");
                return false;
            }
        }

        /// <summary>
        /// 删除公式
        /// </summary>
        public async Task<bool> DeleteFormulaAsync(int formulaId)
        {
            try
            {
                await _dbContext.Db.Updateable<pet_synthesis_formula>()
                    .SetColumns(x => new pet_synthesis_formula { is_active = false })
                    .Where(x => x.id == formulaId)
                    .ExecuteCommandAsync();
                await ClearFormulaCacheAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除合成公式异常");
                return false;
            }
        }

        /// <summary>
        /// 清除公式缓存
        /// </summary>
        public async Task ClearFormulaCacheAsync()
        {
            // 简化实现，实际应该清除所有相关缓存
            await Task.CompletedTask;
        }

        /// <summary>
        /// 查找匹配的公式
        /// </summary>
        private pet_synthesis_formula FindMatchingFormula(List<pet_synthesis_formula> formulas, decimal mainGrowth, decimal viceGrowth)
        {
            foreach (var formula in formulas)
            {
                if (mainGrowth >= (formula.main_growth_required ?? 0) &&
                    viceGrowth >= (formula.vice_growth_required ?? 0))
                {
                    return formula;
                }
            }
            return null;
        }
    }
}
