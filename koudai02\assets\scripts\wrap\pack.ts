import { _decorator, Component, find, Node, Label } from 'cc';
const { ccclass, property } = _decorator;
import { HttpRequest } from '../tool/HttpRequest';
import { Global } from '../Global';
import { ScrollViewContent } from './ScrollViewContent';
import { ChatWindow } from '../network/ChatWindow';


//背包主脚本
@ccclass('pack')
export class pack extends Component {
    
     //道具按钮块
     @property(Node)
     public backpack_btn: Node;

     //装备按钮块
     @property(Node)
     public equipment_btn: Node;

    //使用道具按钮
    public Btn_use: Node;

     //放入仓库
    @property(Node)
    public Btn_warehouse: Node;

     //丢弃道具
    @property(Node)
    public Btn_discard: Node;

     //使用装备
    @property(Node)
    public Btn_use_zb: Node;

     //五行点
    @property(Node)
    public Btn_warehouse_zb: Node;
     
    //分解
    @property(Node)
    public Btn_batch_zb:Node;

     //丢弃
    @property(Node)
    public Btn_discard_zb: Node;

    //刷新列表
    @property(Node)
    public rfrpg2: Node;

    //聊天窗口组件
    @property(ChatWindow)
    public chatWindow: ChatWindow = null;

    public st_scrollViewContent: ScrollViewContent = null;//Content脚本
    public scrollViewContentNode: Node; //道具展示Content

    public backpacksnum: Node;//背包数量
    public frameNum: number; //数据帧

    // 当前界面类型：'backpack' 道具界面，'equipment' 装备界面
    private currentInterfaceType: 'backpack' | 'equipment' = 'backpack';
    start() {
        // 每30帧去刷一次数量
        this.schedule(this.setBackpacksnum, 1);

        // 初始化界面显示
        this.updateButtonVisibility();
    }

    update(deltaTime: number) {

        //子节点会比主节点慢加载，需要刷新去读取
        if (!this.scrollViewContentNode) {
            this.scrollViewContentNode = find("/ScrollView/view/content", this.node);
        } else {
            this.st_scrollViewContent = this.scrollViewContentNode.getComponent(ScrollViewContent);
        }

        if (!this.Btn_use) {
            this.Btn_use = find("/backpack_btn/Btn_use", this.node);

        } else {
            this.Btn_use.on(Node.EventType.MOUSE_DOWN, this.useProp, this);//绑定使用道具按钮点击事件
        }

        if (!this.backpacksnum) {
            this.backpacksnum = find("/Numberofbackpacks/backpacksnum", this.node);
        }

        // 绑定刷新按钮点击事件
        if (this.rfrpg2 && !this.rfrpg2.hasEventListener(Node.EventType.MOUSE_DOWN)) {
            this.rfrpg2.on(Node.EventType.MOUSE_DOWN, this.refreshPropList, this);
        }

        // 绑定放入仓库按钮点击事件
        if (this.Btn_warehouse && !this.Btn_warehouse.hasEventListener(Node.EventType.MOUSE_DOWN)) {
            this.Btn_warehouse.on(Node.EventType.MOUSE_DOWN, this.moveToWarehouse, this);
        }

        // 绑定丢弃道具按钮点击事件
        if (this.Btn_discard && !this.Btn_discard.hasEventListener(Node.EventType.MOUSE_DOWN)) {
            this.Btn_discard.on(Node.EventType.MOUSE_DOWN, this.discardItem, this);
        }

        // 绑定装备相关按钮事件
        if (this.Btn_use_zb && !this.Btn_use_zb.hasEventListener(Node.EventType.MOUSE_DOWN)) {
            this.Btn_use_zb.on(Node.EventType.MOUSE_DOWN, this.useEquipment, this);
        }

        if (this.Btn_batch_zb && !this.Btn_batch_zb.hasEventListener(Node.EventType.MOUSE_DOWN)) {
            this.Btn_batch_zb.on(Node.EventType.MOUSE_DOWN, this.resolveEquipment, this);
        }

        if (this.Btn_discard_zb && !this.Btn_discard_zb.hasEventListener(Node.EventType.MOUSE_DOWN)) {
            this.Btn_discard_zb.on(Node.EventType.MOUSE_DOWN, this.discardEquipment, this);
        }

        if (this.Btn_warehouse_zb && !this.Btn_warehouse_zb.hasEventListener(Node.EventType.MOUSE_DOWN)) {
            this.Btn_warehouse_zb.on(Node.EventType.MOUSE_DOWN, this.transformElement, this);
        }

    }

    /**
     * 使用道具函数 - 使用RESTful API
     */
    public async useProp() {
        
        console.log("使用道具完成方法调用");

        if (this.st_scrollViewContent.propId > 0) {
            try {
                // 构建使用道具的请求体
                const requestBody = {
                    UserId: Global.userid,
                    ItemSeq: this.st_scrollViewContent.propId
                };

                // 使用POST方法调用标准API
                const data = await HttpRequest.postConvertJson('prop/use', requestBody);

                console.log("使用道具完成:", data);

                // 检查返回结果
                if (data && (data.success !== false)) {
                    // 使用成功，刷新道具列表
                    this.st_scrollViewContent.refreshBackpack(); // 刷新整个道具列表

                    // 显示使用结果消息
                    if (data.message) {
                        console.log("道具使用结果:", data.message);

                        // 获取当前选中的道具名称
                        const currentItem = this.st_scrollViewContent.getCurrentSelectedItem();
                        const itemName = currentItem?.itemName || "道具";

                        // 成功使用道具，发送到聊天频道
                        this.sendUsageResultToChat(data.message, data.effectDescription, true, itemName);
                    }
                } else {
                    // 使用失败
                    const errorMsg = data?.message || "使用道具失败";
                    console.error("使用道具失败:", errorMsg);

                    // 失败情况只发送到本地聊天框
                    this.sendUsageResultToChat(errorMsg, null, false);
                }

            } catch (error) {
                console.error("使用道具请求失败:", error);
                // 这里可以添加网络错误提示UI
            }
        } else {
            console.warn("请先选择要使用的道具");
            // 这里可以添加提示UI
        }
    }

    //设置背包道具数量
    public setBackpacksnum() {
        this.backpacksnum.getComponent(Label).string = this.st_scrollViewContent.propNum.toString() + "/" + this.st_scrollViewContent.propCount.toString();
    }

    /**
     * 刷新当前界面列表（根据界面类型决定刷新道具还是装备）
     */
    public refreshPropList() {
        console.log("点击刷新按钮，当前界面类型:", this.currentInterfaceType);
        this.refreshCurrentInterface();
    }

    /**
     * 刷新当前界面（通用方法）
     */
    public refreshCurrentInterface() {
        if (!this.st_scrollViewContent) {
            console.warn("ScrollViewContent组件未初始化，无法刷新列表");
            return;
        }

        if (this.currentInterfaceType === 'backpack') {
            // 道具界面：刷新道具列表
            console.log("刷新道具列表");
            this.st_scrollViewContent.refreshBackpack();
        } else if (this.currentInterfaceType === 'equipment') {
            // 装备界面：重新加载装备列表
            console.log("刷新装备列表");
            this.loadEquipmentItems();
        } else {
            // 默认刷新道具列表
            console.log("界面类型未知，默认刷新道具列表");
            this.st_scrollViewContent.refreshBackpack();
        }
    }

    /**
     * 将道具使用结果发送到聊天内容中
     * @param message 消息内容
     * @param effectDescription 效果描述
     * @param isSuccess 是否成功（成功发送到频道，失败只发送到本地聊天框）
     * @param itemName 道具名称（成功时需要）
     */
    private sendUsageResultToChat(message: string, effectDescription?: string, isSuccess: boolean = false, itemName?: string) {
        if (this.chatWindow) {
            if (isSuccess) {
                // 成功使用道具，发送到聊天频道
                // 格式：系统：用户名+使用的道具名+使用消息提示
                const username = Global.username || "玩家";
                const chatMessage = `${username}使用了${itemName}，${message}`;

                // 构造发送到服务器的消息格式
                const msgObj = {
                    Type: "chat",
                    Content: `系统：${chatMessage}`
                };

                // 发送到聊天频道
                this.chatWindow.sendMsg(JSON.stringify(msgObj));

                // 同时也添加到本地聊天框
                this.chatWindow.addMessage("系统", chatMessage);

                console.log("道具使用成功结果已发送到聊天频道:", chatMessage);
            } else {
                // 失败情况只发送到本地聊天框
                let chatMessage = message;
                if (effectDescription && effectDescription !== message) {
                    chatMessage = `${message} - ${effectDescription}`;
                }

                // 只添加到本地聊天框，不发送到频道
                this.chatWindow.addMessage("系统", chatMessage);

                console.log("道具操作失败结果已发送到本地聊天框:", chatMessage);
            }
        } else {
            console.warn("ChatWindow组件未初始化，无法发送聊天消息");
        }
    }

    /**
     * 放入仓库
     */
    public async moveToWarehouse() {
        if (this.st_scrollViewContent.propId > 0) {
            try {
                console.log("放入仓库，道具序号:", this.st_scrollViewContent.propId);

                const requestBody = {
                    UserId: Global.userid,
                    ItemSeq: this.st_scrollViewContent.propId,
                    NewPosition: 2 // 2表示仓库
                };

                const data = await HttpRequest.postConvertJson('prop/move', requestBody);
                console.log("放入仓库完成:", data);

                if (data && (data.success !== false)) {
                    // 移动成功，刷新道具列表
                    this.st_scrollViewContent.refreshBackpack();

                    if (data.message) {
                        console.log("放入仓库结果:", data.message);
                        // 失败情况只发送到本地聊天框，不发送到频道
                        this.sendUsageResultToChat(data.message, null, false);
                    }
                } else {
                    const errorMsg = data?.message || "放入仓库失败";
                    console.error("放入仓库失败:", errorMsg);
                    // 失败情况只发送到本地聊天框
                    this.sendUsageResultToChat(errorMsg, null, false);
                }
            } catch (error) {
                console.error("放入仓库请求失败:", error);
                // 这里可以添加网络错误提示UI
            }
        } else {
            console.warn("请先选择要放入仓库的道具");
            // 这里可以添加提示UI
        }
    }

    /**
     * 丢弃道具（真正删除并记录到丢弃表）
     */
    public async discardItem() {
        if (this.st_scrollViewContent.propId > 0) {
            try {
                console.log("丢弃道具，道具序号:", this.st_scrollViewContent.propId);

                // 获取当前选中的道具信息
                const currentItem = this.st_scrollViewContent.getCurrentSelectedItem();
                const itemName = currentItem?.itemName || "道具";

                // 确认丢弃操作
                const confirmMessage = `确定要丢弃 ${itemName} 吗？丢弃后可以在丢弃记录中找回。`;
                // 这里可以添加确认对话框，暂时直接执行

                const requestBody = {
                    UserId: Global.userid,
                    ItemSeq: this.st_scrollViewContent.propId,
                    Reason: "用户主动丢弃"
                };

                const data = await HttpRequest.postConvertJson('prop/discard', requestBody);
                console.log("丢弃道具完成:", data);

                if (data && (data.success !== false)) {
                    // 丢弃成功，刷新道具列表
                    this.st_scrollViewContent.refreshBackpack();

                    if (data.message) {
                        console.log("丢弃道具结果:", data.message);
                        // 丢弃成功，发送到本地聊天框
                        this.sendUsageResultToChat(data.message, null, false);
                    }
                } else {
                    const errorMsg = data?.message || "丢弃道具失败";
                    console.error("丢弃道具失败:", errorMsg);
                    // 失败情况只发送到本地聊天框
                    this.sendUsageResultToChat(errorMsg, null, false);
                }
            } catch (error) {
                console.error("丢弃道具请求失败:", error);
                this.sendUsageResultToChat("网络错误，丢弃道具失败", null, false);
            }
        } else {
            console.warn("请先选择要丢弃的道具");
            this.sendUsageResultToChat("请先选择要丢弃的道具", null, false);
        }
    }

    /**
     * 设置界面类型（从Friom.ts调用）
     * @param interfaceType 界面类型：'backpack' 道具界面，'equipment' 装备界面
     */
    public setInterfaceType(interfaceType: 'backpack' | 'equipment') {
        this.currentInterfaceType = interfaceType;
        this.updateButtonVisibility();

        // 根据界面类型加载对应的列表数据
        if (interfaceType === 'backpack') {
            this.loadBackpackItems();
        } else if (interfaceType === 'equipment') {
            this.loadEquipmentItems();
        }

        console.log(`界面类型已切换为: ${interfaceType}`);
    }

    /**
     * 根据当前界面类型更新按钮显示状态
     */
    private updateButtonVisibility() {
        if (this.currentInterfaceType === 'backpack') {
            // 道具界面：显示道具按钮块，隐藏装备按钮块
            if (this.backpack_btn) {
                this.backpack_btn.active = true;
            }
            if (this.equipment_btn) {
                this.equipment_btn.active = false;
            }
            console.log("显示道具界面按钮");
        } else if (this.currentInterfaceType === 'equipment') {
            // 装备界面：显示装备按钮块，隐藏道具按钮块
            if (this.backpack_btn) {
                this.backpack_btn.active = false;
            }
            if (this.equipment_btn) {
                this.equipment_btn.active = true;
            }
            console.log("显示装备界面按钮");
        }
    }

    /**
     * 加载背包道具列表
     */
    private loadBackpackItems() {
        if (this.st_scrollViewContent) {
            this.st_scrollViewContent.refreshBackpack();
            console.log("加载背包道具列表");
        } else {
            console.warn("ScrollViewContent组件未初始化，无法加载背包道具");
        }
    }

    /**
     * 加载装备列表
     */
    private async loadEquipmentItems() {
        try {
            console.log("开始加载装备列表，用户ID:", Global.userid);

            // 调用装备API获取用户装备列表
            const response = await HttpRequest.getConvertJson1(`equipment/user/${Global.userid}`, {});
            console.log("装备API响应:", response);

            // 处理返回的数据
            let equipments: any[] = [];

            console.log("装备API原始响应类型:", typeof response);
            console.log("装备API原始响应内容:", response);

            if (typeof response === 'string') {
                try {
                    const parsed = JSON.parse(response);
                    console.log("解析后的JSON:", parsed);
                    equipments = parsed.data || parsed || [];
                } catch (e) {
                    console.error("解析装备数据失败:", e);
                    equipments = [];
                }
            } else if (response && response.data) {
                console.log("response.data:", response.data);
                equipments = response.data.data || response.data || [];
            } else if (Array.isArray(response)) {
                console.log("response是数组:", response);
                equipments = response;
            } else if (response) {
                console.log("直接使用response:", response);
                equipments = response;
            }

            console.log("最终获取到装备数据:", equipments);
            console.log("装备数据数量:", equipments.length);

            if (equipments.length > 0) {
                // 转换装备数据格式以适配ScrollViewContent
                const convertedEquipments = this.convertEquipmentData(equipments);

                // 更新ScrollViewContent的数据
                if (this.st_scrollViewContent) {
                    this.st_scrollViewContent.updateItemList(convertedEquipments);
                    console.log("装备列表已更新到ScrollViewContent");
                } else {
                    console.warn("ScrollViewContent组件未初始化");
                }
            } else {
                console.log("用户暂无装备");
                // 显示空列表
                if (this.st_scrollViewContent) {
                    this.st_scrollViewContent.updateItemList([]);
                }
            }
        } catch (error) {
            console.error("加载装备列表失败:", error);
        }
    }

    /**
     * 转换装备数据格式以适配ScrollViewContent
     * @param equipments 装备数据数组
     */
    private convertEquipmentData(equipments: any[]): any[] {
        console.log("开始转换装备数据，原始数据:", equipments);

        // 正确映射装备数据字段
        const converted = equipments.map((equipment, index) => {
            const result = {
                ...equipment,                             // 保留所有原始字段
                // 装备名称：优先使用equipName，然后是name
                name: equipment.equipName || equipment.name || `装备${index + 1}`,
                // 装备类型：使用typeName而不是固定的"装备"
                type: equipment.typeName || "未知类型",
                // 数量：装备固定为1
                count: 1,
                // ID：使用装备实例ID
                id: equipment.id || index
            };

            console.log(`转换装备数据[${index}]:`, {
                装备名称: equipment.equipName,
                装备类型: equipment.typeName,
                强化等级: equipment.strengthenLevel,
                最终显示名称: result.name,
                最终显示类型: result.type,
                ID: result.id
            });

            return result;
        });

        console.log("装备数据转换完成，转换后数据:", converted);
        return converted;
    }

    // ==================== 装备功能方法 ====================

    /**
     * 使用装备 - 装备到宠物
     */
    public async useEquipment() {
        try {
            if (!this.st_scrollViewContent || this.st_scrollViewContent.propId <= 0) {
                console.warn("请先选择要使用的装备");
                return;
            }

            // 获取当前选中的装备信息
            const currentItem = this.st_scrollViewContent.getCurrentSelectedItem();
            const equipmentName = currentItem?.itemName || "装备";

            console.log("开始使用装备:", {
                equipmentId: this.st_scrollViewContent.propId,
                equipmentName: equipmentName
            });

            // TODO: 这里需要弹出宠物选择界面
            // 暂时使用默认宠物ID (需要后续完善)
            const petId = 1; // 临时使用固定宠物ID

            const requestBody = {
                UserEquipmentId: this.st_scrollViewContent.propId,
                PetId: petId
            };

            const response = await HttpRequest.postConvertJson('equipment/equip', requestBody);
            console.log("装备使用完成:", response);

            if (response && (response.success !== false)) {
                // 使用成功，刷新装备列表
                this.refreshCurrentInterface();

                if (response.message) {
                    console.log("装备使用结果:", response.message);
                    // 发送成功消息到聊天
                    this.sendUsageResultToChat(`成功装备 ${equipmentName}`, null, true, equipmentName);
                }
            } else {
                const errorMsg = response?.message || "装备使用失败";
                console.error("装备使用失败:", errorMsg);
                // 发送失败消息到本地聊天框
                this.sendUsageResultToChat(errorMsg, null, false);
            }

        } catch (error) {
            console.error("装备使用异常:", error);
            this.sendUsageResultToChat("装备使用异常，请重试", null, false);
        }
    }

    /**
     * 分解装备
     */
    public async resolveEquipment() {
        try {
            if (!this.st_scrollViewContent || this.st_scrollViewContent.propId <= 0) {
                console.warn("请先选择要分解的装备");
                return;
            }

            // 获取当前选中的装备信息
            const currentItem = this.st_scrollViewContent.getCurrentSelectedItem();
            const equipmentName = currentItem?.itemName || "装备";

            console.log("开始分解装备:", {
                equipmentId: this.st_scrollViewContent.propId,
                equipmentName: equipmentName
            });

            // 确认分解操作
            const confirmMessage = `确定要分解 ${equipmentName} 吗？分解后将获得材料和金币。`;
            // TODO: 这里可以添加确认对话框，暂时直接执行

            const requestBody = {
                UserEquipmentId: this.st_scrollViewContent.propId
            };

            const response = await HttpRequest.postConvertJson('equipment/resolve', requestBody);
            console.log("装备分解完成:", response);

            if (response && (response.success !== false)) {
                // 分解成功，刷新装备列表
                this.refreshCurrentInterface();

                if (response.message) {
                    console.log("装备分解结果:", response.message);
                    // 发送成功消息到聊天
                    this.sendUsageResultToChat(`成功分解 ${equipmentName}`, response.message, true, equipmentName);
                }

                // 显示分解奖励
                if (response.data) {
                    console.log("分解奖励:", response.data);
                    // TODO: 可以添加奖励显示界面
                }
            } else {
                const errorMsg = response?.message || "装备分解失败";
                console.error("装备分解失败:", errorMsg);
                // 发送失败消息到本地聊天框
                this.sendUsageResultToChat(errorMsg, null, false);
            }

        } catch (error) {
            console.error("装备分解异常:", error);
            this.sendUsageResultToChat("装备分解异常，请重试", null, false);
        }
    }

    /**
     * 丢弃装备
     */
    public async discardEquipment() {
        try {
            if (!this.st_scrollViewContent || this.st_scrollViewContent.propId <= 0) {
                console.warn("请先选择要丢弃的装备");
                return;
            }

            // 获取当前选中的装备信息
            const currentItem = this.st_scrollViewContent.getCurrentSelectedItem();
            const equipmentName = currentItem?.itemName || "装备";

            console.log("开始丢弃装备:", {
                equipmentId: this.st_scrollViewContent.propId,
                equipmentName: equipmentName
            });

            // 确认丢弃操作
            const confirmMessage = `确定要丢弃 ${equipmentName} 吗？丢弃后装备将被永久删除。`;
            // TODO: 这里可以添加确认对话框，暂时直接执行

            const response = await HttpRequest.deleteConvertJson(`equipment/${this.st_scrollViewContent.propId}`);
            console.log("装备丢弃完成:", response);

            if (response && (response.success !== false)) {
                // 丢弃成功，刷新装备列表
                this.refreshCurrentInterface();

                if (response.message) {
                    console.log("装备丢弃结果:", response.message);
                    // 发送成功消息到本地聊天框
                    this.sendUsageResultToChat(`成功丢弃 ${equipmentName}`, null, false);
                }
            } else {
                const errorMsg = response?.message || "装备丢弃失败";
                console.error("装备丢弃失败:", errorMsg);
                // 发送失败消息到本地聊天框
                this.sendUsageResultToChat(errorMsg, null, false);
            }

        } catch (error) {
            console.error("装备丢弃异常:", error);
            this.sendUsageResultToChat("装备丢弃异常，请重试", null, false);
        }
    }

    /**
     * 五行点化
     */
    public async transformElement() {
        try {
            if (!this.st_scrollViewContent || this.st_scrollViewContent.propId <= 0) {
                console.warn("请先选择要点化的装备");
                return;
            }

            // 获取当前选中的装备信息
            const currentItem = this.st_scrollViewContent.getCurrentSelectedItem();
            const equipmentName = currentItem?.itemName || "装备";

            console.log("开始五行点化:", {
                equipmentId: this.st_scrollViewContent.propId,
                equipmentName: equipmentName
            });

            // 确认点化操作
            const confirmMessage = `确定要对 ${equipmentName} 进行五行点化吗？需要消耗五行点化石。`;
            // TODO: 这里可以添加确认对话框，暂时直接执行

            const requestBody = {
                UserEquipmentId: this.st_scrollViewContent.propId
            };

            const response = await HttpRequest.postConvertJson('equipment/element/transform', requestBody);
            console.log("五行点化完成:", response);

            if (response && (response.success !== false)) {
                // 点化成功，刷新装备列表
                this.refreshCurrentInterface();

                if (response.message) {
                    console.log("五行点化结果:", response.message);
                    // 发送成功消息到聊天
                    this.sendUsageResultToChat(`${equipmentName} 五行点化成功`, response.message, true, equipmentName);
                }

                // 显示点化结果
                if (response.data) {
                    console.log("点化结果:", response.data);
                    // TODO: 可以添加点化结果显示界面
                }
            } else {
                const errorMsg = response?.message || "五行点化失败";
                console.error("五行点化失败:", errorMsg);
                // 发送失败消息到本地聊天框
                this.sendUsageResultToChat(errorMsg, null, false);
            }

        } catch (error) {
            console.error("五行点化异常:", error);
            this.sendUsageResultToChat("五行点化异常，请重试", null, false);
        }
    }

}


        