# Cocos Creator 3.8 动画相关接口规则文档

## 一、命名空间
### 1. Animation
暂未获取到详细说明，推测该命名空间包含了与动画系统相关的核心功能和常量等。在使用动画相关功能时，可能会从该命名空间引入一些基础的类或方法。
### 2. animation
同样暂未获取到详细说明，可能是与动画操作相关的工具函数或辅助类的集合。
### 3. AnimationClip
该命名空间下可能包含与动画剪辑相关的具体实现和工具。动画剪辑是动画系统中重要的组成部分，用于定义一段动画的关键帧和曲线等信息。
### 4. _legacy
推测该命名空间与旧版本的动画系统兼容相关，可能包含一些旧版本动画系统的遗留功能或接口。

## 二、类
### 1. Animation
#### 说明
动画组件管理一组动画状态，控制它们的播放。为了方便，动画组件还存储了一组动画剪辑。每个剪辑都会独自创建一个关联的动画状态对象。动画组件具有事件特性，它会派发一系列播放状态相关的事件。
#### 使用方式
```typescript
import { Animation, Node } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 Animation 组件
const animation = node.addComponent(Animation);

// 加载动画剪辑
const clip = new AnimationClip();
// 设置剪辑的名称
clip.name = 'myAnimationClip';
// 将剪辑添加到动画组件中
animation.addClip(clip);

// 播放动画
animation.play('myAnimationClip');
```
### 2. AnimationClip
#### 说明
动画剪辑表示一段使用动画编辑器编辑的关键帧动画或是外部美术工具生产的骨骼动画。它的数据主要被分为几层：轨道、关键帧和曲线。
#### 使用方式
```typescript
import { AnimationClip, Vec3 } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();
// 设置剪辑的名称
clip.name = 'moveAnimation';

// 创建一个轨道
const track = clip.createTrack('position');
// 添加关键帧
const keyframe1 = { time: 0, value: new Vec3(0, 0, 0) };
const keyframe2 = { time: 1, value: new Vec3(100, 0, 0) };

// 将关键帧添加到轨道中
clip.addKeyFrame(track, keyframe1);
clip.addKeyFrame(track, keyframe2);
```
### 3. AnimationClipLegacyData
暂未获取到详细说明，推测该类与旧版本的动画剪辑数据相关，可能用于处理旧版本动画剪辑的兼容性问题。
### 4. AnimationController
#### 说明
将动画图应用到动画控制器组件所挂载的节点上。当动画控制器开始运行时，动画图会被实例化。然后便可以设置动画图实例中的变量或者查询动画图的运行状况。
#### 使用方式
```typescript
import { AnimationController, Node } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 AnimationController 组件
const controller = node.addComponent(AnimationController);

// 加载动画图
// 假设已经有一个动画图资源 animationGraph
controller.animationGraph = animationGraph;

// 启动动画控制器
controller.start();

// 设置动画图中的变量
controller.setValue('speed', 2);
```
### 5. AnimationManager
暂未获取到详细说明，推测该类用于管理动画系统的全局状态，如动画的更新、调度等。
### 6. AnimationState
#### 说明
AnimationState 完全控制动画播放过程。大多数情况下动画组件是足够和易于使用的。如果您需要更多的动画控制接口，请使用 AnimationState。
#### 使用方式
```typescript
import { Animation, AnimationState, Node } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 Animation 组件
const animation = node.addComponent(Animation);

// 加载动画剪辑
const clip = new AnimationClip();
clip.name = 'myAnimationClip';
animation.addClip(clip);

// 获取动画状态
const state = animation.getState('myAnimationClip');

// 设置动画的播放速度
state.speed = 0.5;

// 播放动画
state.play();
```
### 7. AnimCurve
#### 说明
动画曲线，用于定义动画的变化规律，如线性变化、贝塞尔曲线变化等。
#### 使用方式
```typescript
import { AnimCurve } from 'cc';

// 创建一个动画曲线
const curve = new AnimCurve();
// 设置曲线的类型，例如线性曲线
curve.type = AnimCurve.Type.LINEAR;

// 添加关键点
curve.addKey(0, 0);
curve.addKey(1, 100);
```
### 8. ColorTrack
#### 说明
颜色轨道描述目标上某个颜色属性的动画，可用于实现颜色的渐变动画等。
#### 使用方式
```typescript
import { AnimationClip, ColorTrack, Color } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个颜色轨道
const colorTrack = new ColorTrack();
colorTrack.target = 'myNode'; // 目标节点
colorTrack.property = 'color'; // 目标属性

// 添加关键帧
const keyframe1 = { time: 0, value: new Color(255, 0, 0) };
const keyframe2 = { time: 1, value: new Color(0, 255, 0) };

clip.addKeyFrame(colorTrack, keyframe1);
clip.addKeyFrame(colorTrack, keyframe2);
```
### 9. ComponentPath
暂未获取到详细说明，推测该类用于描述组件在节点树中的路径，以便在动画中定位目标组件。
### 10. CubicSplineNumberValue
暂未获取到详细说明，推测该类与三次样条曲线的数值计算相关，可能用于实现平滑的数值动画。
### 11. EventInfo
#### 说明
用于定义动画中的事件信息，当动画播放到特定时间点时，可以触发相应的事件。
#### 使用方式
```typescript
import { AnimationClip, EventInfo } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个事件信息
const eventInfo = new EventInfo();
// 设置事件触发的时间
eventInfo.time = 0.5;
// 设置事件触发时调用的函数名
eventInfo.func = 'onAnimationEvent';

// 将事件信息添加到动画剪辑中
clip.events.push(eventInfo);
```
### 12. HierarchyPath
暂未获取到详细说明，推测该类用于描述节点在层级结构中的路径，与 ComponentPath 类似，但更侧重于节点的层级关系。
### 13. MorphWeightsAllValueProxy
#### 说明
用于设置模型组件目标上所有子网格形变权重的曲线值代理工厂，可用于实现模型的变形动画。
#### 使用方式
```typescript
import { MorphWeightsAllValueProxy, ModelComponent } from 'cc';

// 获取模型组件
const modelComponent = this.node.getComponent(ModelComponent);

// 创建形变权重代理工厂
const proxyFactory = new MorphWeightsAllValueProxy();
proxyFactory.target = modelComponent;

// 设置形变权重曲线
// 假设已经有一个动画曲线 curve
proxyFactory.curve = curve;
```
### 14. MorphWeightsValueProxy
#### 说明
用于设置模型组件目标上指定子网格形变权重的曲线值代理工厂。
#### 使用方式
```typescript
import { MorphWeightsValueProxy, ModelComponent } from 'cc';

// 获取模型组件
const modelComponent = this.node.getComponent(ModelComponent);

// 创建形变权重代理工厂
const proxyFactory = new MorphWeightsValueProxy();
proxyFactory.target = modelComponent;
proxyFactory.subMeshIndex = 0; // 指定子网格索引

// 设置形变权重曲线
// 假设已经有一个动画曲线 curve
proxyFactory.curve = curve;
```
### 15. MorphWeightValueProxy
#### 说明
用于设置模型组件目标上指定子网格的指定形状的形变权重的曲线值代理工厂。
#### 使用方式
```typescript
import { MorphWeightValueProxy, ModelComponent } from 'cc';

// 获取模型组件
const modelComponent = this.node.getComponent(ModelComponent);

// 创建形变权重代理工厂
const proxyFactory = new MorphWeightValueProxy();
proxyFactory.target = modelComponent;
proxyFactory.subMeshIndex = 0; // 指定子网格索引
proxyFactory.shapeIndex = 0; // 指定形状索引

// 设置形变权重曲线
// 假设已经有一个动画曲线 curve
proxyFactory.curve = curve;
```
### 16. ObjectTrack
#### 说明
对象轨道描述目标上某个对象类型的属性的动画，可用于实现对象属性的动画变化。
#### 使用方式
```typescript
import { AnimationClip, ObjectTrack } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个对象轨道
const objectTrack = new ObjectTrack();
objectTrack.target = 'myNode'; // 目标节点
objectTrack.property = 'myObjectProperty'; // 目标属性

// 添加关键帧
const keyframe1 = { time: 0, value: { x: 0, y: 0 } };
const keyframe2 = { time: 1, value: { x: 100, y: 100 } };

clip.addKeyFrame(objectTrack, keyframe1);
clip.addKeyFrame(objectTrack, keyframe2);
```
### 17. QuatTrack
#### 说明
四元数轨道描述目标上某个四元数（旋转）属性的动画，可用于实现旋转动画。
#### 使用方式
```typescript
import { AnimationClip, QuatTrack, Quat } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个四元数轨道
const quatTrack = new QuatTrack();
quatTrack.target = 'myNode'; // 目标节点
quatTrack.property = 'rotation'; // 目标属性

// 添加关键帧
const keyframe1 = { time: 0, value: new Quat() };
const keyframe2 = { time: 1, value: Quat.fromEuler(new Quat(), 0, 90, 0) };

clip.addKeyFrame(quatTrack, keyframe1);
clip.addKeyFrame(quatTrack, keyframe2);
```
### 18. RatioSampler
暂未获取到详细说明，推测该类用于根据比例采样动画曲线的值，可能在动画的插值计算中使用。
### 19. RealTrack
#### 说明
实数轨道描述目标上某个标量属性的动画，如透明度、大小等属性的动画。
#### 使用方式
```typescript
import { AnimationClip, RealTrack } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个实数轨道
const realTrack = new RealTrack();
realTrack.target = 'myNode'; // 目标节点
realTrack.property = 'opacity'; // 目标属性

// 添加关键帧
const keyframe1 = { time: 0, value: 1 };
const keyframe2 = { time: 1, value: 0 };

clip.addKeyFrame(realTrack, keyframe1);
clip.addKeyFrame(realTrack, keyframe2);
```
### 20. SizeTrack
#### 说明
尺寸轨道描述目标上某个尺寸属性的动画，可用于实现节点的缩放动画等。
#### 使用方式
```typescript
import { AnimationClip, SizeTrack, Size } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个尺寸轨道
const sizeTrack = new SizeTrack();
sizeTrack.target = 'myNode'; // 目标节点
sizeTrack.property = 'size'; // 目标属性

// 添加关键帧
const keyframe1 = { time: 0, value: new Size(100, 100) };
const keyframe2 = { time: 1, value: new Size(200, 200) };

clip.addKeyFrame(sizeTrack, keyframe1);
clip.addKeyFrame(sizeTrack, keyframe2);
```
### 21. StateMachineComponent
#### 说明
状态机组件，用于管理动画的状态转换，可实现复杂的动画逻辑。
#### 使用方式
```typescript
import { StateMachineComponent, Node } from 'cc';

// 创建一个节点
const node = new Node();
// 添加 StateMachineComponent 组件
const stateMachine = node.addComponent(StateMachineComponent);

// 设置状态机的配置
// 假设已经有一个状态机配置 stateMachineConfig
stateMachine.config = stateMachineConfig;

// 启动状态机
stateMachine.start();
```
### 22. Track
#### 说明
轨道描述了动画目标的路径和动画的方式。它是动画剪辑的基础单元。
#### 使用方式
```typescript
import { AnimationClip, Track } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个轨道
const track = new Track();
// 设置轨道的目标路径
// 假设已经有一个目标路径 targetPath
rack.targetPath = targetPath;

// 添加轨道到动画剪辑中
clip.addTrack(track);
```
### 23. TrackPath
#### 说明
描述怎样寻址动画目标，用于在动画中定位目标节点和属性。
#### 使用方式
```typescript
import { TrackPath } from 'cc';

// 创建一个轨道路径
const trackPath = new TrackPath();
// 设置路径信息
// 例如设置节点名称和属性名称
rackPath.nodeName = 'myNode';
trackPath.propertyName = 'position';
```
### 24. UniformProxyFactory
#### 说明
用于设置材质目标上指定 Uniform 的曲线值代理工厂，可用于实现材质属性的动画变化。
#### 使用方式
```typescript
import { UniformProxyFactory, Material } from 'cc';

// 获取材质
const material = this.node.getComponent(Material);

// 创建 Uniform 代理工厂
const proxyFactory = new UniformProxyFactory();
proxyFactory.target = material;
proxyFactory.uniformName = 'myUniform'; // 指定 Uniform 名称

// 设置曲线
// 假设已经有一个动画曲线 curve
proxyFactory.curve = curve;
```
### 25. VectorTrack
#### 说明
向量轨道描述目标上某个（二、三、四维）向量属性的动画，如位置、速度等属性的动画。
#### 使用方式
```typescript
import { AnimationClip, VectorTrack, Vec3 } from 'cc';

// 创建一个动画剪辑
const clip = new AnimationClip();

// 创建一个向量轨道
const vectorTrack = new VectorTrack();
vectorTrack.target = 'myNode'; // 目标节点
vectorTrack.property = 'position'; // 目标属性

// 添加关键帧
const keyframe1 = { time: 0, value: new Vec3(0, 0, 0) };
const keyframe2 = { time: 1, value: new Vec3(100, 100, 100) };

clip.addKeyFrame(vectorTrack, keyframe1);
clip.addKeyFrame(vectorTrack, keyframe2);
```

## 三、接口
### 1. AnimationGraphRunTime
#### 说明
一个非透明的类型，它是动画图在引擎外部的表示。可用于与动画图进行交互，如设置变量、查询状态等。
#### 使用方式
```typescript
import { AnimationController, AnimationGraphRunTime } from 'cc';

// 获取动画控制器
const controller = this.node.getComponent(AnimationController);

// 获取动画图运行时对象
const graphRuntime: AnimationGraphRunTime = controller.animationGraphRuntime;

// 设置动画图中的变量
graphRuntime.setValue('speed', 2);
```
### 2. AnimationGraphVariantRunTime
#### 说明
一个非透明的类型，它是动画图变体在引擎外部的表示。可用于管理动画图的不同变体。
#### 使用方式
```typescript
import { AnimationController, AnimationGraphVariantRunTime } from 'cc';

// 获取动画控制器
const controller = this.node.getComponent(AnimationController);

// 获取动画图变体运行时对象
const variantRuntime: AnimationGraphVariantRunTime = controller.getVariantRuntime('myVariant');

// 设置变体中的变量
variantRuntime.setValue('scale', 1.5);
```
### 3. ClipStatus
#### 说明
动作状态中包含的剪辑的运行状态，可用于查询动画剪辑的播放状态。
#### 使用方式
```typescript
import { Animation, ClipStatus } from 'cc';

// 获取动画组件
const animation = this.node.getComponent(Animation);

// 获取剪辑的状态
const status: ClipStatus = animation.getClipStatus('myAnimationClip');

// 查询剪辑是否正在播放
if (status.isPlaying) {
    console.log('动画正在播放');
}
```
### 4. ICustomTargetPath
暂未获取到详细说明，推测该接口用于自定义动画目标的寻址方式。
### 5. IEvent
暂未获取到详细说明，推测该接口用于定义动画事件的规范，如事件的触发条件、回调函数等。
### 6. ILerpable
暂未获取到详细说明，推测该接口用于实现可插值的对象，在动画的插值计算中使用。
### 7. IValueProxy
#### 说明
曲线值代理用来设置曲线值到目标，是广义的赋值。每个曲线值代理都关联着一个目标对象。
#### 使用方式
```typescript
import { IValueProxy, Node } from 'cc';

// 获取目标节点
const targetNode = this.node;

// 创建一个值代理
const valueProxy: IValueProxy = createValueProxy(targetNode, 'position');

// 设置值
valueProxy.setValue(new Vec3(100, 100, 100));
```
### 8. IValueProxyFactory
暂未获取到详细说明，推测该接口用于创建曲线值代理的工厂，可根据不同的目标对象和属性创建相应的值代理。
### 9. LegacyClipCurve
暂未获取到详细说明，推测该接口与旧版本的动画剪辑曲线相关，可能用于处理旧版本动画剪辑曲线的兼容性问题。
### 10. LegacyClipCurveData
暂未获取到详细说明，推测该接口与旧版本的动画剪辑曲线数据相关，可能包含旧版本动画剪辑曲线的具体数据信息。
### 11. LegacyCommonTarget
暂未获取到详细说明，推测该接口与旧版本的通用动画目标相关，可能用于处理旧版本动画目标的兼容性问题。
### 12. LegacyComponentsCurveData
暂未获取到详细说明，推测该接口与旧版本的组件动画曲线数据相关，可能包含旧版本组件动画曲线的具体数据信息。
### 13. LegacyNodeCurveData
暂未获取到详细说明，推测该接口与旧版本的节点动画曲线数据相关，可能包含旧版本节点动画曲线的具体数据信息。
### 14. LegacyObjectCurveData
暂未获取到详细说明，推测该接口与旧版本的对象动画曲线数据相关，可能包含旧版本对象动画曲线的具体数据信息。
### 15. MotionStateStatus
#### 说明
动作状态的运行状态，可用于查询动画动作状态的相关信息，如是否正在运行、是否已完成等。
#### 使用方式
```typescript
import { AnimationController, MotionStateStatus } from 'cc';

// 获取动画控制器
const controller = this.node.getComponent(AnimationController);

// 获取动作状态的运行状态
const status: MotionStateStatus = controller.getMotionStateStatus('myMotionState');

// 查询动作状态是否正在运行
if (status.isRunning) {
    console.log('动作状态正在运行');
}
```
### 16. TransitionStatus
#### 说明
过渡的运行状态，可用于查询动画过渡的相关信息，如过渡是否正在进行、过渡的进度等。
#### 使用方式
```typescript
import { AnimationController, TransitionStatus } from 'cc';

// 获取动画控制器
const controller = this.node.getComponent(AnimationController);

// 获取过渡的运行状态
const status: TransitionStatus = controller.getTransitionStatus('myTransition');

// 查询过渡的进度
console.log('过渡进度：', status.progress);
```

## 四、函数
### 1. computeRatioByType
#### 说明
根据曲线类型计算新的比例，可用于动画的插值计算。
#### 使用方式
```typescript
import { computeRatioByType } from 'cc';

// 假设已经有一个曲线类型 curveType 和当前时间比例 currentRatio
const newRatio = computeRatioByType(curveType, currentRatio);
```
### 2. getPathFromRoot
暂未获取到详细说明，推测该函数用于获取从根节点到目标节点的路径信息。
### 3. getWorldTransformUntilRoot
暂未获取到详细说明，推测该函数用于获取从目标节点到根节点的世界变换信息。
### 4. isCustomPath
暂未获取到详细说明，推测该函数用于判断一个路径是否为自定义路径。
### 5. isPropertyPath
暂未获取到详细说明，推测该函数用于判断一个路径是否为属性路径。
### 6. sampleAnimationCurve
#### 说明
采样动画曲线，根据时间获取动画曲线上对应的值。
#### 使用方式
```typescript
import { sampleAnimationCurve, AnimCurve } from 'cc';

// 创建一个动画曲线
const curve = new AnimCurve();
// 添加关键点
curve.addKey(0, 0);
curve.addKey(1, 100);

// 采样动画曲线
const value = sampleAnimationCurve(curve, 0.5);
console.log('采样值：', value);
```
### 7. timeBezierToTangents
#### 说明
Legacy curve uses time based bezier curve interpolation. That's, interpolate time 'x'(time ratio between two frames, eg. [0, 1]) and then use the interpolated time to sample curve. Now we need to compute the the end tangent of previous frame and the start tangent of the next frame.
#### 使用方式
```typescript
import { timeBezierToTangents } from 'cc';

// 假设已经有前一帧的时间、下一帧的时间、当前时间比例等信息
const prevFrameTime = 0;
const nextFrameTime = 1;
const currentTimeRatio = 0.5;

const [endTangent, startTangent] = timeBezierToTangents(prevFrameTime, nextFrameTime, currentTimeRatio);
```

## 五、枚举
### VariableType
#### 说明
表示动画图变量的类型，如整数、浮点数、布尔值等。
#### 使用方式
```typescript
import { AnimationController, VariableType } from 'cc';

// 获取动画控制器
const controller = this.node.getComponent(AnimationController);

// 设置动画图中的变量类型为整数
controller.setVariableType('myVariable', VariableType.INT);
```

## 六、变量
### 1. CubicSplineQuatValue
暂未获取到详细说明，推测该变量与三次样条曲线的四元数数值相关，可能用于实现平滑的旋转动画。
### 2. CubicSplineVec2Value
暂未获取到详细说明，推测该变量与三次样条曲线的二维向量数值相关，可能用于实现平滑的二维位置动画。
### 3. CubicSplineVec3Value
暂未获取到详细说明，推测该变量与三次样条曲线的三维向量数值相关，可能用于实现平滑的三维位置动画。
### 4. CubicSplineVec4Value
暂未获取到详细说明，推测该变量与三次样条曲线的四维向量数值相关，可能用于实现平滑的四维属性动画。
