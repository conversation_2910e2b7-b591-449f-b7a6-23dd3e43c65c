import { _decorator, Component, Node, ScrollView, EditBox, instantiate, RichText, Prefab } from 'cc';
const { ccclass, property } = _decorator;
import { Global } from '../Global';


@ccclass('ChatWindow')
export class ChatWindow extends Component {

    private ws: WebSocket | null = null;

    @property(ScrollView)
    scrollView: ScrollView = null;

    @property(Node)
    content: Node = null; // ScrollView的内容节点  

    @property(Node)
    SendButton: Node = null; // 发送按钮节点  

    @property(EditBox)
    inputField: EditBox = null; // 输入框节点  

    @property(Prefab)
    ChatItemPrefab: Prefab = null; // 聊天项预制体  

    Num: number = 0;

    start() {
        // 通过节点名查找发送按钮，并绑定点击事件  
        this.SendButton = this.node.getChildByName('sending');
        if (this.SendButton) {
            this.SendButton.on(Node.EventType.TOUCH_END, this.sendMessage, this);  //绑定发送按钮
        } else {
            console.error('找不到发送按钮');
        }

        this.connectWebSocket();// 连接服务器
    }

    //发送按钮发送消息
    public sendMessage(): void {
        const nickname = Global.username;
        this.Num += 1;

        const message = this.inputField.string;
        this.inputField.string = '';

        // 构造后端需要的消息格式
        const msgObj = {
            Type: "chat",
            Content: message
        };
        this.sendMsg(JSON.stringify(msgObj));
        this.addMessage(nickname, message); // 本地先显示
    }
    

    // 添加消息到聊天内容中
    public addMessage(nickname: string, message: string): void {
        // 创建聊天项
        const chatItem = this.createChatItem(nickname, message);
        if (chatItem) {
            this.content.addChild(chatItem.node); // 添加节点到内容节点  
        }

        // 确保滚动条滚动到最底部  
        this.scrollView.scrollToBottom(0.1); // 0.1是滚动动画的持续时间  

        // 检查内容节点中的子项数量  
        if (this.content.children.length > 30) {
            // 如果子项数量超过30，删除最上面的一条  
            const firstChild = this.content.children[0];
            if (firstChild) {
                firstChild.destroy(); // 销毁节点，从内存中移除  
            }
        }
    }
    
    // 创建聊天项
   private createChatItem(nickname: string, message: string): RichText {
        // 实例化聊天项预制体
        const item = instantiate(this.ChatItemPrefab);
        // 获取聊天项的RichText组件
        const richTextComponent = item.getComponent(RichText);
        // 如果RichText组件存在
        if (richTextComponent) {
            // 如果昵称存在且不为空
            if (nickname && nickname.length > 0) {
                // 设置RichText组件的文本，昵称和消息分别用不同颜色显示
                richTextComponent.string = `<color=#4f4f4f>${nickname}:</color><color=#FF0000>${message}</color>`;
            } else {
                // 如果昵称为空，只显示消息
                richTextComponent.string = `<color=#FF0000>${message}</color>`;
            }
        }
        // 返回RichText组件
        return richTextComponent;
    }

    //Socket连接服务器
    private connectWebSocket() {
        this.ws = new WebSocket(Global.WebSocketUrl);

        this.ws.onopen = () => {
            // 连接成功时，添加一条服务器连接成功的消息
            this.addMessage("","服务器连接成功....")
            // 可选：连接成功后可发送一条上线消息
            // this.sendMsg(JSON.stringify({ Type: "chat", Content: "Hello, server!" }));
        };

        this.ws.onmessage = (event) => {
            // 这里 event.data 是后端推送的内容
            // 如果后端推送格式为 {nickname}:{content}，可做分割
            let nickname = "";
            let message = event.data;
            // 可选：如后端格式为 "昵称:内容"
            // const splitIndex = event.data.indexOf(":");
            // if (splitIndex > 0) {
            //     nickname = event.data.substring(0, splitIndex);
            //     message = event.data.substring(splitIndex + 1);
            // }
            this.addMessage(nickname, message);
        };

        this.ws.onclose = () => {
            this.addMessage("", "服务端连接关闭......");
            this.ws = null;
        };

        this.ws.onerror = (error) => {
            this.addMessage("", "服务端连接异常......");
        };
    }

    //Socket发送消息到服务端
    public sendMsg(message: string) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(message);
        } else {
            console.error('WebSocket is not open. Unable to send message.');
        }
    }

    // 在组件销毁时关闭 WebSocket 连接
    onDestroy() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
    }

}