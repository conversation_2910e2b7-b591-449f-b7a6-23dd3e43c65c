import { _decorator, Component, Node, Label, Sprite, find, Script, Button, SpriteFrame, SpriteAtlas, resources } from 'cc';
const { ccclass, property } = _decorator;

import { Global } from "../Global";  
import { fight } from './fight';
import { PopFrame } from './PopFrame';
import { petInformation } from './petInformation';
import { monsterInformation } from './monsterInformation';
import { pet } from '../Entity/pet';
import { monster } from '../Entity/monster';
import { combatInfoPanel } from './combatInfoPanel';
import { Friom } from '../Friom';
import { HttpRequest } from '../tool/HttpRequest';
import { BattleRequestDTO, BattleResultDTO, BattleRoundDTO } from './BattleTypes';
import { BattleManager } from './BattleManager';
import { ResourceUtils } from '../manager/ResourceUtils';
import { ResourceManager } from '../manager/ResourceManager';
import { MassiveResourceManager } from '../manager/MassiveResourceManager';

//战斗界面主脚本
@ccclass('fightMian')
export class fightMian extends Component {

    @property(Label)
    private label!: Label;//倒计时label

    @property(Node)
    private Friom: Node;//主面板节点

    @property(Number)
    private ATK_pet: number = 0;//宠物攻击力

    @property(Number)
    private ATK_monster: number = 0;//怪物攻击力

    @property(Number)
    private HP_pet: number = 0;//宠物生命值

    @property(Number)
    private HP_monster: number = 0;//怪物生命值
    
    @property(Node)
    private combatbackground: Node;//战斗背景
   
    //退出战斗按钮
    @property(Button)
    private btn_exit: Button = null!;
    
    //攻击按钮
    @property(Button)
    private btn_attack: Button = null;

    //地图界面
    @property(Node)
    private panel_field: Node;//地图界面

    //工具栏
    @property(Node)
    private panel_ability: Node;//工具栏

    // UI组件引用
    private St_pet: fight;//宠物脚本
    private St_monster: fight;//怪物脚本
    private St_PopFrame: PopFrame;//结束提示框脚本
    private St_petInformation: petInformation;//宠物信息脚本
    private St_monsterInformation: monsterInformation;//怪物信息脚本
    private St_PetCombatInfo: combatInfoPanel;//宠物输出面板脚本
    private St_MonsterCombatInfo: combatInfoPanel;//怪物输出面板脚本
    private St_main: Friom;//主脚本
    private PopFrameNode: Node;
    Bar: Node;//读取功能栏节点

    // 战斗相关属性
    private battleManager: BattleManager;
    private currentBattleResult: BattleResultDTO | null = null;
    private currentRoundIndex: number = 0;
    private isPlayingSequence: boolean = false;
    private countdownNumber: number = 10; //倒计时
    private isPetVictory: boolean = false; //宠物是否胜利
    
    // 动画回调相关属性
    private currentAttackRound: BattleRoundDTO | null = null;
    private currentAttackCallback: (() => void) | null = null;
    private isPlayerAttacking: boolean = false;
    private currentTimeoutId: any = null;

    // 🆕 怪物循环战斗相关属性
    private currentMonsterIndex: number = 0; // 当前怪物在MapMonsters中的索引
    private totalMonstersDefeated: number = 0; // 已击败的怪物数量

    private get PetId(): number {
        return Global.currentPetId; // 动态获取当前选中的宠物ID
    }

    private get MonsterId(): number {
        // 🔧 根据当前怪物索引循环获取MapMonsters中的怪物ID
        const monsters = this.MapMonsters;
        if (monsters && monsters.length > 0) {
            // 确保索引在有效范围内
            const validIndex = this.currentMonsterIndex % monsters.length;
            return monsters[validIndex].monsterId;
        }
        
        // 如果没有怪物数据，返回默认值
        return 1;
    }
    
  
    // 获取当前地图怪物
    private get MapMonsters(): any[] {
        
        return Global.currentMapMonsters || [];
    }
    
    //获取当前地图掉落
    private get MapDrops(): any[] {
        return Global.currentMapDrops || [];
    }
    
    //获取当前地图信息
    private get MapInfo(): any {
        return Global.currentMapInfo;
    }

    //图像前缀  g、S:攻击   z:动态     q、k、t:头像

    //MP 魔法   /HP 生命    /EXP 经验
    async start() {
        // 🔧 第一步：基础初始化 - 查找节点和获取组件
        const initResult = await this.performBasicInitialization();
        if (!initResult.success) {
            console.error("❌ 基础初始化失败:", initResult.errorMessage);
            this.showErrorAndReturn(initResult.errorMessage);
            return;
        }

        // 🔧 第二步：验证战斗ID - 只有验证通过才开始战斗相关动作
        const validationResult = this.validateBattleIds();
        if (!validationResult.isValid) {
            console.error("❌ 战斗ID验证失败:", validationResult.errorMessage);
            this.showErrorAndReturn(validationResult.errorMessage);
            return;
        }

        console.log("✅ 战斗ID验证通过，开始战斗初始化");

        // 🔧 第三步：战斗初始化 - 只有在ID验证通过后才执行
        await this.performBattleInitialization();
    }

    /**
     * 🆕 节点激活时的处理（每次进入页面都会调用）
     */
    async onEnable() {
        console.log("🔄 fightMian页面被激活，开始重新初始化...");
        
        // 🔧 重要：只有在start方法已经执行过后才进行重新初始化
        if (!this.battleManager) {
            console.log("📝 首次激活，等待start方法完成初始化");
            return;
        }
        
        // 🔧 清理之前的战斗状态
        this.resetBattleState();
        
        // 🔧 重新验证战斗ID
        const validationResult = this.validateBattleIds();
        if (!validationResult.isValid) {
            console.error("❌ 重新激活时战斗ID验证失败:", validationResult.errorMessage);
            this.showErrorAndReturn(validationResult.errorMessage);
            return;
        }
        
        // 🔧 重新初始化战斗相关组件
        await this.reinitializeBattleComponents();
        
        console.log("✅ fightMian页面重新初始化完成");
    }

    /**
     * 🆕 重新初始化战斗组件
     */
    private async reinitializeBattleComponents(): Promise<void> {
        try {
            console.log("🔧 重新初始化战斗组件...");
            
            // 🆕 重新设置按钮事件监听器
            this.setupAttackButton();
            
            // 🆕 确保工具栏在初始化时显示
            if (this.panel_ability) {
                this.panel_ability.active = true;
                console.log("🔓 初始化时已显示工具栏");
            }
            
            // 重新预加载战斗资源
            await this.preloadBattleResources();
            
            // 重新设置战斗背景
            await this.setupCombatBackground();
            
            // 重新同步战斗节点资源
            await this.syncBattleNodesWithCurrentIds();
            
            // 重新启动倒计时
            this.startCountdown();
            
            // 重新初始化怪物循环
            this.currentMonsterIndex = 0;
            this.totalMonstersDefeated = 0;
            
            // 重新显示地图数据信息
            this.displayMapInfo();
            
            // 延迟更新宠物和怪物信息
            setTimeout(() => {
                console.log(`🎯 重新初始化 - 宠物ID: ${this.PetId}, 怪物ID: ${this.MonsterId}`);
                
                this.updateMonsterInfoFromMapData();

                this.St_petInformation.UpdatePetInformation(Global.petName, 50, this.PetId.toString());
            }, 500);
            
            console.log("✅ 战斗组件重新初始化完成");
            
        } catch (error) {
            console.error("💥 重新初始化战斗组件时发生错误:", error);
            this.showErrorAndReturn(`重新初始化失败: ${(error as Error).message}`);
        }
    }

    /**
     * 🔧 基础初始化：查找节点和获取组件
     */
    private async performBasicInitialization(): Promise<{success: boolean, errorMessage?: string}> {
        try {
            console.log("🔧 开始基础初始化...");

            // 初始化战斗管理器
            this.battleManager = BattleManager.getInstance();
            
            // 查找所有必要的节点
            const petNode: Node = find("/combatbackground/petNode", this.node);
            const monsterNode: Node = find("/combatbackground/monsterNode", this.node);
            this.PopFrameNode = find("/combatbackground/PopFrame", this.node);
            const petInformationNode: Node = find("/combatbackground/petInformation", this.node);
            const monsterInformationNode: Node = find("/combatbackground/monsterInformation", this.node);
            const petNoticeNode: Node = find("/combatbackground/petNode/petNotice", this.node);
            const monsterNoticeNode: Node = find("/combatbackground/monsterNode/monsterNotice", this.node);
            this.Bar = find("/combatbackground/ability/base", this.node);
            
            // 验证关键节点是否存在
            if (!petNode) return { success: false, errorMessage: "宠物节点未找到" };
            if (!monsterNode) return { success: false, errorMessage: "怪物节点未找到" };
            if (!this.Bar) return { success: false, errorMessage: "功能栏节点未找到" };
            if (!this.PopFrameNode) return { success: false, errorMessage: "结束提示框节点未找到" };
            if (!petInformationNode) return { success: false, errorMessage: "宠物信息节点未找到" };
            if (!monsterInformationNode) return { success: false, errorMessage: "怪物信息节点未找到" };
            if (!this.Friom) return { success: false, errorMessage: "主面板节点未找到" };

            // 获取所有组件
            this.St_main = this.Friom.getComponent(Friom)!;
            this.St_pet = petNode.getComponent(fight)!;
            this.St_monster = monsterNode.getComponent(fight)!;
            this.St_PopFrame = this.PopFrameNode.getComponent(PopFrame)!;
            this.St_petInformation = petInformationNode.getComponent(petInformation)!;
            this.St_monsterInformation = monsterInformationNode.getComponent(monsterInformation)!;
            this.St_PetCombatInfo = petNoticeNode.getComponent(combatInfoPanel)!;
            this.St_MonsterCombatInfo = monsterNoticeNode.getComponent(combatInfoPanel)!;
            
            // 🆕 添加退出战斗按钮事件监听器
            this.setupExitBattleButton();
            
            // 🆕 添加攻击按钮事件监听器
            this.setupAttackButton();
            
            console.log("✅ 基础初始化完成");
            return { success: true };

        } catch (error) {
            console.error("💥 基础初始化过程中发生错误:", error);
            return { success: false, errorMessage: `基础初始化失败: ${(error as Error).message}` };
        }
    }

    /**
     * 🔧 验证战斗ID是否有效
     */
    private validateBattleIds(): {isValid: boolean, errorMessage?: string} {
        console.log("🔍 开始验证战斗ID...");
        
        // 验证宠物ID
        const petId = this.PetId;
        if (!petId || petId <= 0) {
            return {
                isValid: false,
                errorMessage: `宠物ID无效 (${petId})，请先选择宠物后再开始战斗`
            };
        }

        // 验证怪物ID
        if (!this.MonsterId || this.MonsterId <= 0) {
            return {
                isValid: false,
                errorMessage: `怪物ID无效 (${this.MonsterId})，请联系管理员`
            };
        }

        console.log(`✅ 战斗ID验证通过 - 宠物ID: ${petId}, 怪物ID: ${this.MonsterId}`);
        return { isValid: true };
    }

    /**
     * 🔧 战斗初始化：只有在ID验证通过后才执行
     */
    private async performBattleInitialization(): Promise<void> {
        try {
            console.log("🎮 开始战斗初始化...");

            // 预加载战斗所需资源
            await this.preloadBattleResources();
            
            // 🆕 设置战斗背景
            await this.setupCombatBackground();
            
            // 🔧 修复：同步战斗节点的资源ID，确保显示正确的宠物和怪物
            await this.syncBattleNodesWithCurrentIds();
            
            // 启动倒计时
            this.startCountdown();

            // 🆕 初始化怪物循环（确保从第一只怪物开始）
            this.currentMonsterIndex = 0;
            this.totalMonstersDefeated = 0;
            
            // 🆕 显示地图数据信息
            this.displayMapInfo();
            
            // 延迟显示宠物和怪物信息（等待子节点完全加载）
            setTimeout(() => {
                console.log(`🎯 当前出战宠物ID: ${this.PetId} (来自Global.currentPetId)`);
                console.log(`🎯 当前战斗怪物ID: ${this.MonsterId} (循环索引: ${this.currentMonsterIndex})`);
                
                // 🆕 使用地图数据更新怪物信息
                this.updateMonsterInfoFromMapData();
                
                // 🆕 显示战斗进度
                const progress = this.getBattleProgress();
                console.log(`📊 战斗进度: ${progress.current}/${progress.total}, 已击败: ${progress.defeated}`);
                
                this.St_petInformation.UpdatePetInformation(Global.petName, 50, this.PetId.toString());
            }, 500);

            console.log("✅ 战斗初始化完成");

        } catch (error) {
            console.error("💥 战斗初始化过程中发生错误:", error);
            this.showErrorAndReturn(`战斗初始化失败: ${(error as Error).message}`);
        }
    }

    /**
     * 🆕 设置战斗背景
     */
    private async setupCombatBackground(): Promise<void> {
        try {
            console.log("🌄 开始设置战斗背景...");
            
            // 验证combatbackground节点是否存在
            if (!this.combatbackground) {
                console.warn("⚠️ combatbackground节点未找到，跳过背景设置");
                return;
            }
            
            // 获取combatbackground的Sprite组件
            const backgroundSprite = this.combatbackground.getComponent(Sprite);
            if (!backgroundSprite) {
                console.warn("⚠️ combatbackground节点没有Sprite组件，跳过背景设置");
                return;
            }
            
            // 从Global获取图集名称和背景图片名称
            const atlastName = Global.atlastName;
            const backgroundName = Global.background;
            
            console.log(`📦 背景配置 - 图集: "${atlastName}", 背景图: "${backgroundName}"`);
            
            if (!atlastName || !backgroundName) {
                console.warn("⚠️ 图集名称或背景图片名称为空，使用默认背景");
                return;
            }
            
            // 🔧 修复：使用Cocos Creator的resources.load方式加载地图图集
            const atlasPath = `map/${atlastName}`; // 地图图集存储在resources/map/目录下
            console.log(`📂 加载地图图集路径: ${atlasPath}`);
            
            // 使用Promise包装resources.load
            const spriteAtlas = await new Promise<SpriteAtlas>((resolve, reject) => {
                resources.load(atlasPath, SpriteAtlas, (err, atlas) => {
                    if (err) {
                        reject(new Error(`地图图集加载失败: ${err.message}`));
                    } else {
                        resolve(atlas);
                    }
                });
            });
            
            if (!spriteAtlas) {
                console.error(`❌ 无法加载地图图集: ${atlasPath}`);
                return;
            }
            
            // 设置图集
            backgroundSprite.spriteAtlas = spriteAtlas;
            console.log(`✅ 已设置背景图集: ${atlastName}`);
            
            // 从图集中获取背景图片的SpriteFrame
            const spriteFrame = spriteAtlas.getSpriteFrame(backgroundName);
            
            if (!spriteFrame) {
                console.error(`❌ 在图集 ${atlastName} 中未找到背景图片: ${backgroundName}`);
                // 尝试不带扩展名的方式
                const nameWithoutExt = backgroundName.replace(/\.(png|jpg|jpeg)$/i, '');
                const spriteFrameAlt = spriteAtlas.getSpriteFrame(nameWithoutExt);
                
                if (spriteFrameAlt) {
                    backgroundSprite.spriteFrame = spriteFrameAlt;
                    console.log(`✅ 已设置背景图片: ${nameWithoutExt} (移除扩展名)`);
                } else {
                    console.error(`❌ 在图集中完全找不到背景图片: ${backgroundName} 或 ${nameWithoutExt}`);
                    
                    // 🆕 显示图集中可用的SpriteFrame供调试
                    const availableFrames = spriteAtlas.getSpriteFrames();
                    console.log(`📋 图集中可用的SpriteFrame:`, Object.keys(availableFrames));
                    return;
                }
            } else {
                // 设置背景图片
                backgroundSprite.spriteFrame = spriteFrame;
                console.log(`✅ 已设置背景图片: ${backgroundName}`);
            }
            
            console.log("🎉 战斗背景设置完成");
            
        } catch (error) {
            console.error("💥 设置战斗背景时发生错误:", error);
            console.warn("⚠️ 将继续使用默认背景");
        }
    }

    /**
     * 🆕 显示地图数据信息
     */
    private displayMapInfo(): void {
        console.log("🗺️ ===== 当前地图战斗数据 =====");
        
        // 🆕 显示背景资源信息
        console.log(`🌄 战斗背景资源:`);
        console.log(`   - 图集名称: "${Global.atlastName || '未设置'}"`);
        console.log(`   - 背景图片: "${Global.background || '未设置'}"`);
        
        if (this.MapInfo) {
            console.log(`📍 地图名称: ${this.MapInfo.mapName} (ID: ${this.MapInfo.mapId})`);
            console.log(`📝 地图描述: ${this.MapInfo.mapDesc}`);
            console.log(`🎯 推荐等级: ${this.MapInfo.recommendLevel}`);
        }
        
        console.log(`👹 可战斗怪物数量: ${this.MapMonsters.length}`);
        if (this.MapMonsters.length > 0) {
            console.log("👹 怪物列表 (循环战斗):");
            this.MapMonsters.forEach((monster: any, index: number) => {
                const isCurrent = index === this.currentMonsterIndex;
                const marker = isCurrent ? "🎯" : "  ";
                const status = isCurrent ? " ← 当前" : "";
                console.log(`${marker} ${index + 1}. ${monster.monsterName} (ID: ${monster.monsterId})${status}`);
                console.log(`      等级: ${monster.levelRange}, 属性: ${monster.element}, 经验: ${monster.expReward}`);
            });
            console.log(`📊 战斗进度: 当前第${this.currentMonsterIndex + 1}只, 已击败${this.totalMonstersDefeated}只`);
        }
        
        console.log(`💎 掉落配置数量: ${this.MapDrops.length}`);
        if (this.MapDrops.length > 0) {
            console.log("💎 可能掉落:");
            this.MapDrops.forEach((drop: any, index: number) => {
                console.log(`   ${index + 1}. ${drop.itemName} (概率: ${(drop.dropRate * 100).toFixed(1)}%)`);
            });
        }
        
        console.log("🗺️ ==============================");
    }

    /**
     * 🆕 使用地图数据更新怪物信息
     */
    private updateMonsterInfoFromMapData(): void {
        // 🔧 直接获取当前循环中的怪物数据
        const currentMonster = this.getCurrentMonster();
        
        if (currentMonster) {
            console.log(`🎯 当前战斗怪物: ${currentMonster.monsterName} (${this.currentMonsterIndex + 1}/${this.MapMonsters.length})`);
            
            // 使用真实的怪物数据更新怪物信息显示
            this.St_monsterInformation.UpdateMonsterInformation(
                currentMonster.monsterName, 
                this.parseLevel(currentMonster.levelRange)
            );
            
            console.log(`✅ 已更新怪物信息显示: ${currentMonster.monsterName} (等级: ${currentMonster.levelRange})`);
        } else {
            console.warn(`⚠️ 未找到当前怪物数据，使用默认信息`);
            // 使用默认怪物信息
            this.St_monsterInformation.UpdateMonsterInformation("未知怪物", 1);
        }
    }

    /**
     * 🆕 解析等级范围，返回中间值
     */
    private parseLevel(levelRange: string): number {
        try {
            // 解析 "1-5" 格式的等级范围
            const parts = levelRange.split('-');
            if (parts.length === 2) {
                const min = parseInt(parts[0]);
                const max = parseInt(parts[1]);
                return Math.floor((min + max) / 2); // 返回中间值
            } else {
                return parseInt(levelRange) || 1; // 如果是单个数字
            }
        } catch (error) {
            console.warn("⚠️ 解析怪物等级失败:", levelRange);
            return 1; // 默认等级
        }
    }

    /**
     * 🆕 获取当前怪物的掉落信息
     */
    public getCurrentMonsterDrops(): any[] {
        return this.MapDrops.filter((drop: any) => 
            drop.dropType === "怪物掉落" || drop.dropType === "地图掉落"
        );
    }

    /**
     * 🆕 获取当前地图的所有怪物
     */
    public getAllMapMonsters(): any[] {
        return [...this.MapMonsters];
    }

    /**
     * 🆕 获取当前战斗怪物的详细信息
     */
    public getCurrentMonster(): any | null {
        const monsters = this.MapMonsters;
        if (monsters && monsters.length > 0) {
            const validIndex = this.currentMonsterIndex % monsters.length;
            return monsters[validIndex];
        }
        return null;
    }

    /**
     * 🆕 切换到下一只怪物
     */
    private switchToNextMonster(): void {
        const monsters = this.MapMonsters;
        if (monsters && monsters.length > 0) {
            this.currentMonsterIndex = (this.currentMonsterIndex + 1) % monsters.length;
            this.totalMonstersDefeated++;
            
            const newMonster = this.getCurrentMonster();
            console.log(`🔄 切换到下一只怪物: ${newMonster?.monsterName} (${this.currentMonsterIndex + 1}/${monsters.length})`);
            console.log(`📊 已击败怪物总数: ${this.totalMonstersDefeated}`);
            
            // 更新怪物信息显示
            this.updateMonsterInfoFromMapData();
        }
    }

    /**
     * 🆕 重置怪物循环（回到第一只怪物）
     */
    public resetMonsterCycle(): void {
        this.currentMonsterIndex = 0;
        this.totalMonstersDefeated = 0;
        console.log("🔄 重置怪物循环，回到第一只怪物");
        
        // 更新怪物信息显示
        this.updateMonsterInfoFromMapData();
    }

    /**
     * 🆕 获取战斗进度信息
     */
    public getBattleProgress(): { current: number; total: number; defeated: number; currentMonster: any } {
        const monsters = this.MapMonsters;
        return {
            current: this.currentMonsterIndex + 1,
            total: monsters ? monsters.length : 0,
            defeated: this.totalMonstersDefeated,
            currentMonster: this.getCurrentMonster()
        };
    }

    /**
     * 🔧 显示错误信息并返回上一页面
     */
    private showErrorAndReturn(errorMessage: string): void {
        console.error("❌ " + errorMessage);
        
        // 显示错误提示框
        if (this.St_PopFrame) {
            this.St_PopFrame.UpdatePopFrameInfo(
                "⚠️ 初始化失败",
                "",
                "",
                "",
                errorMessage + "\n\n点击返回重新选择宠物"
            );
        } else {
            // 如果提示框组件还未初始化，使用alert作为备用方案
            alert(errorMessage);
            
            // 直接返回上一页面
            this.returnToPreviousPage();
        }
    }

    /**
     * 🔧 返回上一页面
     */
    private returnToPreviousPage(): void {
        console.log("🔙 返回上一页面");
        
        // 隐藏当前战斗面板
        this.node.active = false;
        
        // 显示主界面或战斗准备界面
        if (this.St_main) {
            this.St_main.townShow();
        }
    }

    /**
     * 🔧 修复：同步战斗节点与当前设置的ID，确保显示正确的资源
     */
    private async syncBattleNodesWithCurrentIds(): Promise<void> {
        try {
            console.log(`🔧 同步战斗节点资源 - 宠物ID: ${this.PetId}, 怪物ID: ${this.MonsterId}`);
            
            // 更新宠物节点的Name属性，使其与当前PetId一致
            if (this.St_pet) {
                this.St_pet.Name = this.PetId.toString();
                console.log(`✅ 宠物节点Name已更新为: ${this.St_pet.Name}`);
                
                // 🔧 修复：重新初始化宠物fight组件以加载正确的图集和资源
                await this.reinitializePetFight();
            }
            
            // 更新怪物节点的Name属性，使其与当前MonsterId一致  
            if (this.St_monster) {
                this.St_monster.Name = this.MonsterId.toString();
                console.log(`✅ 怪物节点Name已更新为: ${this.St_monster.Name}`);
                
                // 🔧 修复：重新初始化怪物fight组件以加载正确的图集和资源
                await this.reinitializeMonsterFight();
            }
            
        } catch (error) {
            console.error("❌ 同步战斗节点资源失败:", error);
        }
    }

    /**
     * 🔧 重新初始化宠物fight组件
     */
    private async reinitializePetFight(): Promise<void> {
        try {
            if (!this.St_pet) return;
            
            const resourceManager = MassiveResourceManager.getInstance();
            const petId = this.PetId;
            
            // 1. 获取正确的图集路径
            const { atlas } = resourceManager.getPetGroup(petId);
            console.log(`🐾 宠物${petId}使用图集: ${atlas}`);
            
            // 2. 加载图集
            const spriteAtlas = await resourceManager.loadAtlas(atlas);
            this.St_pet.spriteAtlas = spriteAtlas;
            
            // 3. 重新初始化动作帧
            console.log(`🎬 重新初始化宠物${petId}动作帧`);
            this.St_pet.reinitializeActions();
            
            console.log(`✅ 宠物${petId}资源重新初始化完成`);
            
        } catch (error) {
            console.error(`❌ 宠物fight组件重新初始化失败:`, error);
        }
    }

    /**
     * 🔧 重新初始化怪物fight组件
     */
    private async reinitializeMonsterFight(): Promise<void> {
        try {
            if (!this.St_monster) return;
            
            const resourceManager = MassiveResourceManager.getInstance();
            const monsterId = this.MonsterId;
            
            // 1. 获取正确的图集路径
            const { atlas } = resourceManager.getMonsterGroup(monsterId);
            console.log(`👹 怪物${monsterId}使用图集: ${atlas}`);
            
            // 2. 加载图集
            const spriteAtlas = await resourceManager.loadAtlas(atlas);
            this.St_monster.spriteAtlas = spriteAtlas;
            
            // 3. 重新初始化动作帧
            console.log(`🎬 重新初始化怪物${monsterId}动作帧`);
            this.St_monster.reinitializeActions();
            
            console.log(`✅ 怪物${monsterId}资源重新初始化完成`);
            
        } catch (error) {
            console.error(`❌ 怪物fight组件重新初始化失败:`, error);
        }
    }

    /**
     * 预加载战斗资源
     */
    private async preloadBattleResources(): Promise<void> {
        try {
            console.log("🎮 开始预加载战斗资源...");
            
            // 预加载当前战斗所需的宠物和怪物资源
            await ResourceUtils.preloadBattleResources(this.PetId, this.MonsterId);
            
            console.log("✅ 战斗资源预加载完成");
            
        } catch (error) {
            console.warn("⚠️ 战斗资源预加载失败，但不影响游戏:", error);
        }
    }

    update(deltaTime: number) {

    }

    /**
     * 开始战斗 - 必须请求接口实现
     * 注意：已移除本地计算降级方案，所有战斗必须通过服务器接口完成
     */
    private async Startfight() {
        try {
            console.log("🎯 开始战斗流程 - 必须使用服务器接口");
            
            // 验证战斗条件
            const validation = this.battleManager.validateBattleConditions();
            if (!validation.isValid) {
                console.error("❌ 战斗条件验证失败:", validation.errorMessage);
                this.showErrorDialog("战斗条件验证失败：" + validation.errorMessage);
                return;
            }
            
            // 必须请求完整战斗结果 - 无降级方案
            this.currentBattleResult = await this.battleManager.startNewBattle();
            
            if (!this.currentBattleResult) {
                console.error("❌ 战斗接口请求失败 - 无降级方案");
                this.showErrorDialog("战斗服务器连接失败，请检查网络连接后重试！");
                return;
            }
            
            console.log("✅ 获得完整战斗结果");
            console.log("🔍 战斗结果详情:", {
                isWin: this.currentBattleResult.isWin,
                totalRounds: this.currentBattleResult.battleRounds.length,
                playerMaxHp: this.currentBattleResult.playerMaxHp,
                monsterMaxHp: this.currentBattleResult.monsterMaxHp,
                exp: this.currentBattleResult.exp,
                message: this.currentBattleResult.message
            });
            
            // 检查倒计时状态
            if (this.countdownNumber <= 0) {
                // 倒计时已结束，立即开始播放
                console.log("⚡ 倒计时已结束，立即开始战斗动画");
                this.updateBattleAttributes();
                this.playBattleSequence();
            } else {
                // 倒计时还在进行，等待倒计时结束
                console.log(`⏳ 战斗数据已就绪，等待倒计时结束（剩余${this.countdownNumber}秒）`);
            }
            
        } catch (error) {
            console.error("💥 战斗过程中发生错误 - 无降级方案:", error);
            this.showErrorDialog("战斗过程中发生异常：" + (error as Error).message + "，请重试！");
        }
    }

    /**
     * 更新战斗属性
     */
    // private updateBattleAttributes() {
    //     if (!this.currentBattleResult) return;
        
    //     // 🔧 只更新生命值，不计算攻击力（攻击力从battleRounds中直接获取）
    //     this.HP_pet = this.currentBattleResult.playerMaxHp;
    //     this.HP_monster = this.currentBattleResult.monsterMaxHp;
        
    //     console.log("📊 更新战斗属性:", {
    //         petHp: this.HP_pet,
    //         monsterHp: this.HP_monster,
    //         note: "攻击力直接从battleRounds获取，无需计算"
    //     });
    // }

    /**
     * 播放战斗序列动画
     */
    private async playBattleSequence() {
        if (!this.currentBattleResult || this.isPlayingSequence) return;
        
        this.isPlayingSequence = true;
        console.log(`🎬 开始播放战斗序列，共${this.currentBattleResult.battleRounds.length}回合`);
        
        // 🆕 战斗开始时隐藏工具栏
        if (this.panel_ability) {
            this.panel_ability.active = false;
            console.log("🔒 已隐藏工具栏");
        }
        
        // 初始化血量显示
        this.updateHealthBars(
            this.currentBattleResult.playerMaxHp,
            this.currentBattleResult.monsterMaxHp
        );
        
        this.currentRoundIndex = 0;
        
        // 逐回合播放动画
        await this.playNextRound();
    }

    /**
     * 播放下一回合
     */
    private async playNextRound() {
        console.log(`🔄 检查回合状态: currentRoundIndex=${this.currentRoundIndex}, totalRounds=${this.currentBattleResult?.battleRounds.length}`);
        
        if (!this.currentBattleResult || 
            this.currentRoundIndex >= this.currentBattleResult.battleRounds.length) {
            // 所有回合播放完毕，显示结果
            console.log("🏁 所有回合播放完毕，显示最终结果");
            this.showFinalResult();
            return;
        }
        
        const round = this.currentBattleResult.battleRounds[this.currentRoundIndex];
        console.log(`🎭 播放第${round.round}回合 (${this.currentRoundIndex + 1}/${this.currentBattleResult.battleRounds.length}):`, round.description);
        
        // 确保没有正在进行的攻击
        if (this.currentAttackCallback) {
            console.warn("⚠️ 检测到上一个攻击未完成，等待完成...");
            // 强制清理上一个攻击状态
            if (this.currentTimeoutId) {
                this.unschedule(this.currentTimeoutId);
                this.currentTimeoutId = null;
            }
            this.currentAttackCallback = null;
            this.currentAttackRound = null;
        }
        
        // 播放当前回合动画
        if (round.attackerType === "Player") {
            await this.playPlayerAttack(round);
        } else {
            await this.playMonsterAttack(round);
        }
        
        // 更新血量
        this.updateHealthBars(round.playerHpAfter, round.monsterHpAfter);
        
        this.currentRoundIndex++;
        
        // 短暂延迟后播放下一回合（让玩家看清血量变化）
        this.scheduleOnce(() => {
            this.playNextRound();
        }, 0.5); // 减少到0.5秒间隔
    }

    /**
     * 播放玩家攻击
     */
    private async playPlayerAttack(round: BattleRoundDTO): Promise<void> {
        return new Promise((resolve) => {
            console.log("🐾 播放宠物攻击动画");
            
            // 保存当前回合信息和resolve回调
            this.currentAttackRound = round;
            this.currentAttackCallback = resolve;
            this.isPlayerAttacking = true;
            
            // 添加超时保护，防止动画卡住
            const timeoutId = this.scheduleOnce(() => {
                console.warn("⚠️ 宠物攻击动画超时，强制继续下一回合");
                if (this.currentAttackCallback && this.isPlayerAttacking) {
                    this.showDamageEffect(round, true);
                    const callback = this.currentAttackCallback;
                    this.currentAttackCallback = null;
                    this.currentAttackRound = null;
                    this.currentTimeoutId = null;
                    callback();
                }
            }, 5.0); // 5秒超时
            
            // 保存超时ID以便取消
            this.currentTimeoutId = timeoutId;
            
            // 播放宠物攻击动画
            if (this.St_pet) {
                // 添加小延迟确保上一个动画状态已重置
                this.scheduleOnce(() => {
                    this.St_pet.playFirstAnimation(this.node);
                }, 0.1);
            } else {
                console.error("❌ 宠物脚本组件未找到");
                this.unschedule(timeoutId);
                resolve(); // 如果没有动画组件，直接结束
            }
        });
    }
    
    /**
     * 播放怪物攻击
     */
    private async playMonsterAttack(round: BattleRoundDTO): Promise<void> {
        return new Promise((resolve) => {
            console.log("👹 播放怪物攻击动画");
            
            // 保存当前回合信息和resolve回调
            this.currentAttackRound = round;
            this.currentAttackCallback = resolve;
            this.isPlayerAttacking = false;
            
            // 添加超时保护，防止动画卡住
            const timeoutId = this.scheduleOnce(() => {
                console.warn("⚠️ 怪物攻击动画超时，强制继续下一回合");
                if (this.currentAttackCallback && !this.isPlayerAttacking) {
                    this.showDamageEffect(round, false);
                    const callback = this.currentAttackCallback;
                    this.currentAttackCallback = null;
                    this.currentAttackRound = null;
                    this.currentTimeoutId = null;
                    callback();
                }
            }, 5.0); // 5秒超时
            
            // 保存超时ID以便取消
            this.currentTimeoutId = timeoutId;
            
            // 播放怪物攻击动画
            if (this.St_monster) {
                // 添加小延迟确保上一个动画状态已重置
                this.scheduleOnce(() => {
                    this.St_monster.playFirstAnimation(this.node);
                }, 0.1);
            } else {
                console.error("❌ 怪物脚本组件未找到");
                this.unschedule(timeoutId);
                resolve(); // 如果没有动画组件，直接结束
            }
        });
    }

    /**
     * 显示伤害效果
     */
    private showDamageEffect(round: BattleRoundDTO, isPlayerAttack: boolean) {
        
        if (round.isHit) {
            // 计算显示伤害（暴击有特效）
            const displayDamage = round.damage;
            
            if (isPlayerAttack) {
                console.log("🐾 宠物攻击造成伤害: " + displayDamage + (round.isCritical ? " (暴击)" : ""));
                // 宠物攻击怪物 - 在宠物面板显示宠物造成的伤害
                this.St_PetCombatInfo?.showNotice(
                    round.isCritical ? 0 : displayDamage,  // 普通攻击
                    round.isCritical ? displayDamage : 0,  // 暴击伤害
                    0, // 吸血
                    false // 🔧 修复：宠物攻击，不是怪物攻击
                );

            } else {
                console.log("👹 怪物攻击造成伤害: " + displayDamage + (round.isCritical ? " (暴击)" : ""));
                // 怪物攻击宠物 - 在怪物面板显示怪物造成的伤害
                this.St_MonsterCombatInfo?.showNotice(
                    round.isCritical ? 0 : displayDamage,  // 普通攻击
                    round.isCritical ? displayDamage : 0,  // 暴击伤害
                    0, // 吸血
                    true // 🔧 修复：怪物攻击，是怪物攻击
                );
            }
        } else {
            // 未命中，显示在控制台
            console.log("💨 " + round.description);
        }
    }

    /**
     * 更新血量条
     */
    private updateHealthBars(playerHp: number, monsterHp: number) {
        if (!this.currentBattleResult) return;
        
        const playerProgress = playerHp / this.currentBattleResult.playerMaxHp;
        const monsterProgress = monsterHp / this.currentBattleResult.monsterMaxHp;
        
        this.St_petInformation?.UpdateProgressBar("HP", playerProgress);
        this.St_monsterInformation?.UpdateProgressBar("HP", monsterProgress);
        
        console.log(`💚 玩家血量: ${playerHp}/${this.currentBattleResult.playerMaxHp} (${Math.round(playerProgress * 100)}%)`);
        console.log(`❤️ 怪物血量: ${monsterHp}/${this.currentBattleResult.monsterMaxHp} (${Math.round(monsterProgress * 100)}%)`);
    }

    /**
     * 显示最终结果
     */
    private showFinalResult() {
  
        if (!this.currentBattleResult) return;
      
        this.isPlayingSequence = false;
        this.isPetVictory = this.currentBattleResult.isWin;

        // 🆕 战斗结束时重新显示工具栏
        if (this.panel_ability) {
            this.panel_ability.active = true;
            console.log("🔓 已重新显示工具栏");
        }

        const formatted = this.battleManager.formatBattleResult(this.currentBattleResult);
        const rewards = this.battleManager.formatRewards(this.currentBattleResult);
        const stats = this.battleManager.formatBattleStats(this.currentBattleResult);
        
        // 🆕 显示完整的奖励信息
        this.St_PopFrame?.UpdatePopFrameInfo(
            formatted.resultText,
            rewards.experience || formatted.expText,  // 使用新的经验格式
            rewards.gold,                             // 🆕 显示金币
            rewards.yuanbao,                          // 🆕 显示元宝
            rewards.items || formatted.messageText   // 物品信息
        );
        
        // 🆕 显示战斗统计信息
        this.showBattleStats(stats);
        
        // 🆕 更新精确的血量和魔法值
        this.updateBattleAttributes();
        
        // 🆕 检查自动战斗
        this.checkAutoBattle();
    }

    /**
     * 🆕 显示战斗统计信息
     */
    private showBattleStats(stats: any) {
        console.log("📊 战斗统计:", stats.totalDamage);
        console.log("⚔️ 特效信息:", stats.damageEffects);
        console.log("🎯 先手状态:", stats.firstStrike);
        console.log("💰 经济奖励:", stats.economicRewards);
        
        // 如果有统计显示UI组件，可以在这里更新
        // this.battleStatsPanel?.updateStats(stats);
    }

    /**
     * 🆕 更新精确的血量和魔法值（避免重复实现）
     */
    private updateBattleAttributes() {
        if (!this.currentBattleResult) return;
        
        // 🔧 使用服务端返回的精确数据更新宠物信息
        if (this.St_PetCombatInfo) {
            this.St_PetCombatInfo.updateHpMp(
                this.currentBattleResult.playerRemainingHp || this.currentBattleResult.playerCurrentHp,
                this.currentBattleResult.playerMaxHp,
                this.currentBattleResult.remainingMp || 0,  // 魔法值
                100 // 假设最大魔法值为100，可以从配置获取
            );
        }
        
        // 🔧 更新怪物血量信息
        if (this.St_MonsterCombatInfo) {
            this.St_MonsterCombatInfo.updateHp(
                this.currentBattleResult.monsterRemainingHp || this.currentBattleResult.monsterCurrentHp,
                this.currentBattleResult.monsterMaxHp
            );
        }
        
        // 🔧 同时更新进度条显示
        this.updateHealthBars(
            this.currentBattleResult.playerRemainingHp || this.currentBattleResult.playerCurrentHp,
            this.currentBattleResult.monsterRemainingHp || this.currentBattleResult.monsterCurrentHp
        );
        
        console.log("✅ 战斗属性已更新", {
            playerHp: `${this.currentBattleResult.playerRemainingHp}/${this.currentBattleResult.playerMaxHp}`,
            monsterHp: `${this.currentBattleResult.monsterRemainingHp}/${this.currentBattleResult.monsterMaxHp}`,
            playerMp: this.currentBattleResult.remainingMp
        });
    }

    /**
     * 🆕 检查自动战斗状态
     */
    private checkAutoBattle() {
        if (this.currentBattleResult?.autoBattleStatus === 1) {
            console.log("🤖 检测到自动战斗模式激活");
            
            // 显示自动战斗提示
            this.showAutoBattleHint();
            
            // 2秒后自动继续下一场战斗
            this.scheduleOnce(() => {
                if (this.currentBattleResult?.isWin) {
                    this.PopFrameContinue();
                }
            }, 2.0);
        }
    }

    /**
     * 🆕 显示自动战斗提示
     */
    private showAutoBattleHint() {
        console.log("🤖 自动战斗模式：2秒后自动继续...");
        // 可以在UI上显示倒计时提示
    }

    /**
     * 显示错误弹框
     */
    private showErrorDialog(errorMessage: string) {
        console.error("❌ " + errorMessage);
        
        // 🆕 出现错误时重新显示工具栏
        if (this.panel_ability) {
            this.panel_ability.active = true;
            console.log("🔓 错误时已重新显示工具栏");
        }
        
        // 使用结束提示框显示错误信息
        this.St_PopFrame?.UpdatePopFrameInfo(
            "⚠️ 错误提示",
            "",
            "",
            "",
            errorMessage
        );
        
        // 重置倒计时
        this.resetBattleState();
    }
    
    /**
     * 重置战斗状态
     */
    private resetBattleState() {
        this.currentBattleResult = null;
        this.currentRoundIndex = 0;
        this.isPlayingSequence = false;
        this.countdownNumber = 10;
        
        // 清理动画回调和超时
        this.currentAttackRound = null;
        this.currentAttackCallback = null;
        this.isPlayerAttacking = false;
        if (this.currentTimeoutId) {
            this.unschedule(this.currentTimeoutId);
            this.currentTimeoutId = null;
        }
        
        // 🆕 重置时确保工具栏显示
        if (this.panel_ability) {
            this.panel_ability.active = true;
            console.log("🔓 重置时已显示工具栏");
        }
        
        // 重置血量条到满血状态
        this.St_petInformation?.UpdateProgressBar("HP", 1);
        this.St_monsterInformation?.UpdateProgressBar("HP", 1);
    }


    //显示宠物输出的伤害值面板
    public PetshowNotice() {
        // 🔧 使用当前回合的实际伤害数据，而非计算的攻击力
        const currentRound = this.currentAttackRound;
        const damage = currentRound ? currentRound.damage : 0;
        this.St_PetCombatInfo?.showNotice(damage, 0, 0, false); // 使用实际伤害值
    }

    //隐藏宠物输出的面板
    public PethaideNotice() {
         this.St_PetCombatInfo?.hideNotice();
    }

    //显示怪物输出的伤害值面板
    public MonstershowNotice() {
        // 🔧 使用当前回合的实际伤害数据，而非计算的攻击力
        const currentRound = this.currentAttackRound;
        const damage = currentRound ? currentRound.damage : 0;
        this.St_MonsterCombatInfo?.showNotice(damage, 0, 0, true); // 使用实际伤害值
    }

    //隐藏怪物输出的面板
    public MonsterhideNotice() {
        this.St_MonsterCombatInfo?.hideNotice();
    }

    //攻击结束回调函数
    public attackOver(Type: string) {
        console.log(`✅ ${Type} 攻击动画播放完毕，当前回合: ${this.currentRoundIndex + 1}`);
        
        // 防止重复回调
        if (!this.currentAttackCallback) {
            console.warn("⚠️ 攻击回调已被处理或为空，忽略重复调用");
            return;
        }
        
        // 验证攻击者类型匹配
        const expectedType = this.isPlayerAttacking ? 'petNode' : 'monsterNode';
        if (Type !== expectedType) {
            console.warn(`⚠️ 攻击者类型不匹配，期望: ${expectedType}, 实际: ${Type}`);
            return;
        }
        
        // 取消超时定时器
        if (this.currentTimeoutId) {
            this.unschedule(this.currentTimeoutId);
            this.currentTimeoutId = null;
        }
        
        // 调用Promise回调，继续下一回合
        console.log("🔄 调用回调，准备下一回合");
        const callback = this.currentAttackCallback;
        this.currentAttackCallback = null;
        this.currentAttackRound = null;
        
        // 延迟一帧执行回调，确保动画状态完全重置
        this.scheduleOnce(() => {
            callback();
        }, 0.1);
    }

    //结束提示框，点击继续
    public PopFrameContinue() {
        console.log("🔄 继续战斗");
        
        // 🔧 显示当前战斗进度
        const progress = this.getBattleProgress();
        console.log(`📊 继续战斗 - 当前怪物: ${progress.currentMonster?.monsterName} (${progress.current}/${progress.total})`);
        console.log(`📊 已击败怪物数量: ${progress.defeated}`);
        
        // 重置战斗状态（但不重置怪物循环）
        this.resetBattleState();
        
        // 更新怪物信息显示（显示当前怪物）
        this.updateMonsterInfoFromMapData();
        
        // 启动新的倒计时（5秒），同时发起新的战斗请求
        this.countdownNumber = 5;
        this.startCountdown();
    }

    //结束提示框，点击返回村庄
    public PopFrameback() {
        console.log("🚪 返回主界面");
        this.returnToPreviousPage();
    }

    //倒计时结束
    private Countdownends() {
        // 如果在倒计时期间战斗结果已经获取到，直接播放
        if (this.currentBattleResult) {
            console.log("⚡ 战斗数据已准备就绪，立即开始播放");
            this.updateBattleAttributes();
            this.playBattleSequence();
        } else {
            console.log("⏰ 倒计时结束但战斗数据未就绪，继续等待...");
            // 如果还没有结果，继续等待（Startfight已经在倒计时开始时调用了）
        }
    }

    //倒计时函数
    private startCountdown() {
        console.log("⏱️ 开始倒计时，同时发起战斗请求");
        
        // 在倒计时开始时立即发起战斗请求
        this.Startfight();
        
        // 设置定时器，每秒更新一次  
        this.schedule(() => {
            if (this.countdownNumber > 0) {
                this.countdownNumber--;
                this.label.string = this.countdownNumber.toString();
            } else {
                // 倒计时结束，可以在这里添加其他逻辑  
                this.label.string = "PK";

                // 取消定时器  
                this.unscheduleAllCallbacks();

                this.Countdownends();
            }
        }, 1, this.countdownNumber, 0);
    }

    /**
     * 🆕 验证并获取可用的背景资源信息
     */
    public getBackgroundResourceInfo(): { atlas: string; background: string; isValid: boolean } {
        const atlastName = Global.atlastName || "";
        const backgroundName = Global.background || "";
        
        const isValid = !!(atlastName && backgroundName);
        
        return {
            atlas: atlastName,
            background: backgroundName,
            isValid: isValid
        };
    }

    /**
     * 🆕 重新设置战斗背景（供外部调用）
     */
    public async refreshCombatBackground(): Promise<void> {
        console.log("🔄 重新设置战斗背景...");
        await this.setupCombatBackground();
    }

    // 🆕 添加退出战斗按钮事件监听器
    private setupExitBattleButton() {
        if (this.btn_exit) {
            this.btn_exit.node.on(Node.EventType.TOUCH_END, this.handleExitBattle, this);
        }
    }

    // 🆕 添加攻击按钮事件监听器
    private setupAttackButton() {
        if (this.btn_attack) {
            this.btn_attack.node.on(Node.EventType.TOUCH_END, this.handleAttackButton, this);
            console.log("✅ 攻击按钮事件监听器已设置");
        } else {
            console.warn("⚠️ 攻击按钮未找到");
        }
    }

    // 🆕 处理攻击按钮点击的逻辑
    private handleAttackButton() {
        console.log("⚔️ 用户点击攻击按钮，跳过倒计时");
        
        // 🔧 验证战斗条件
        const validationResult = this.validateBattleIds();
        if (!validationResult.isValid) {
            console.error("❌ 攻击按钮点击时战斗ID验证失败:", validationResult.errorMessage);
            this.showErrorDialog(validationResult.errorMessage);
            return;
        }
        
        // 🔧 如果已经在播放战斗序列，则忽略
        if (this.isPlayingSequence) {
            console.log("⚠️ 战斗序列正在播放中，忽略攻击按钮点击");
            return;
        }
        
        // 🔧 如果战斗结果已经准备好，直接开始播放
        if (this.currentBattleResult) {
            console.log("⚡ 战斗数据已就绪，立即开始播放");
            this.skipCountdownAndStartBattle();
            return;
        }
        
        // 🔧 如果战斗数据还未就绪，强制跳过倒计时并等待数据
        console.log("⏩ 跳过倒计时，等待战斗数据...");
        this.skipCountdownAndWaitForBattle();
    }

    // 🆕 跳过倒计时并立即开始战斗
    private skipCountdownAndStartBattle() {
        // 停止倒计时
        this.unscheduleAllCallbacks();
        
        // 设置倒计时为0
        this.countdownNumber = 0;
        this.label.string = "PK";
        
        // 立即开始战斗
        this.updateBattleAttributes();
        this.playBattleSequence();
        
        console.log("🚀 已跳过倒计时，战斗立即开始");
    }

    // 🆕 跳过倒计时并等待战斗数据
    private skipCountdownAndWaitForBattle() {
        // 停止倒计时
        this.unscheduleAllCallbacks();
        
        // 设置倒计时为0
        this.countdownNumber = 0;
        this.label.string = "等待中...";
        
        // 如果还没有发起战斗请求，现在发起
        if (!this.currentBattleResult) {
            console.log("🎯 发起战斗请求...");
            this.Startfight();
        }
        
        // 设置一个监听器，等待战斗数据准备完成
        this.waitForBattleData();
        
        console.log("⏳ 已跳过倒计时，正在等待战斗数据...");
    }

    // 🆕 等待战斗数据准备完成
    private waitForBattleData() {
        const checkInterval = 100; // 每100ms检查一次
        const maxWaitTime = 10000; // 最多等待10秒
        let waitedTime = 0;
        
        const checkBattleData = () => {
            waitedTime += checkInterval;
            
            if (this.currentBattleResult) {
                // 数据准备完成，开始战斗
                console.log("✅ 战斗数据已准备完成，开始战斗");
                this.label.string = "PK";
                this.updateBattleAttributes();
                this.playBattleSequence();
                return;
            }
            
            if (waitedTime >= maxWaitTime) {
                // 超时处理
                console.error("❌ 等待战斗数据超时");
                this.label.string = "超时";
                this.showErrorDialog("战斗数据获取超时，请重试！");
                return;
            }
            
            // 继续等待
            setTimeout(checkBattleData, checkInterval);
        };
        
        // 开始检查
        setTimeout(checkBattleData, checkInterval);
    }

    // 🆕 处理退出战斗的逻辑
    private handleExitBattle() {
        console.log("🚪 退出战斗，跳转到地图界面");
        
        // 清理战斗状态
        this.resetBattleState();
        
        // 隐藏当前战斗界面
        this.node.active = false;
        
        // 显示地图界面（panel_field）
        if (this.panel_field) {
            this.panel_field.active = true;
            console.log("✅ 已显示地图界面");
        } else {
            console.warn("⚠️ panel_field节点未找到，回退到主界面");
            // 如果地图界面不存在，回退到主界面
            if (this.St_main) {
                this.St_main.townShow();
            }
        }
    }

    /**
     * 🆕 组件销毁时清理事件监听器
     */
    onDestroy() {
        // 移除退出按钮事件监听器
        if (this.btn_exit) {
            this.btn_exit.node.off(Node.EventType.TOUCH_END, this.handleExitBattle, this);
        }
        
        // 🆕 移除攻击按钮事件监听器
        if (this.btn_attack) {
            this.btn_attack.node.off(Node.EventType.TOUCH_END, this.handleAttackButton, this);
        }
        
        // 清理其他状态
        this.resetBattleState();
        
        console.log("🗑️ 战斗界面组件已销毁，事件监听器已清理");
    }
}


