# Cocos Creator 3.8 图形后端抽象模块相关接口规则文档

## 一、命名空间
### gfx
该命名空间涵盖了图形后端抽象模块的核心内容，包含各类图形相关的类、枚举和常量，是整个图形后端抽象的基础。通过这个命名空间，可以访问到用于抹平不同渲染后端 API 差异的各种接口，使得开发者能够以统一的方式处理不同图形硬件和 API 的特性。

## 二、类
### 1. Attribute
#### 说明
`Attribute` 类用于描述顶点属性，在图形渲染中，顶点属性包含了如位置、法线、纹理坐标等信息。通过该类可以定义和管理这些属性，确保在渲染过程中正确地传递和处理顶点数据。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建一个 Attribute 实例，用于描述顶点的位置属性
const positionAttribute = new gfx.Attribute('a_position', gfx.Format.FLOAT32_X3);
```
### 2. BindingMappingInfo
#### 说明
`BindingMappingInfo` 类用于管理绑定映射信息，在图形渲染中，绑定映射定义了如何将资源（如缓冲区、纹理等）绑定到着色器中的特定位置。该类帮助开发者配置和管理这些绑定关系，确保资源能够正确地被着色器使用。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 BindingMappingInfo 实例
const bindingMappingInfo = new gfx.BindingMappingInfo();
// 添加绑定信息
bindingMappingInfo.addBinding('u_texture', 0);
```
### 3. BlendState
#### 说明
`BlendState` 类表示 GFX 混合状态，混合状态用于控制颜色在渲染过程中的混合方式，例如实现透明效果、颜色叠加等。通过设置不同的混合因子和操作，可以实现多样化的渲染效果。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 BlendState 实例
const blendState = new gfx.BlendState();
// 设置混合因子和操作
blendState.enabled = true;
blendState.srcBlend = gfx.BlendFactor.SRC_ALPHA;
blendState.dstBlend = gfx.BlendFactor.ONE_MINUS_SRC_ALPHA;
```
### 4. BlendTarget
#### 说明
`BlendTarget` 类表示 GFX 混合目标，它是 `BlendState` 的一部分，用于指定每个颜色附件的混合设置。在多渲染目标的情况下，可以为每个目标单独设置混合方式。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 BlendTarget 实例
const blendTarget = new gfx.BlendTarget();
// 设置混合目标的属性
blendTarget.blendEnable = true;
blendTarget.srcBlend = gfx.BlendFactor.SRC_ALPHA;
blendTarget.dstBlend = gfx.BlendFactor.ONE_MINUS_SRC_ALPHA;
```
### 5. Buffer
#### 说明
`Buffer` 类表示 GFX 缓冲，用于存储顶点数据、索引数据、常量数据等。在图形渲染中，缓冲区是数据的重要存储和传输载体，通过 `Buffer` 类可以创建、管理和操作这些缓冲区。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建一个顶点缓冲区实例
const vertexBuffer = new gfx.Buffer(gfx.Device.instance, { 
    usage: gfx.BufferUsage.VERTEX, 
    size: 1024, 
    stride: 12 
}); 
// 向缓冲区写入数据
const data = new Float32Array([1, 2, 3, 4, 5, 6]);
vertexBuffer.update(data);
```
### 6. BufferBarrierInfo
#### 说明
`BufferBarrierInfo` 类用于描述缓冲区的内存屏障信息，内存屏障用于确保在不同的渲染操作之间，缓冲区的数据能够正确地同步和更新。通过设置缓冲区的屏障信息，可以避免数据竞争和不一致的问题。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 BufferBarrierInfo 实例
const bufferBarrierInfo = new gfx.BufferBarrierInfo();
// 设置缓冲区屏障的属性
bufferBarrierInfo.buffer = vertexBuffer;
bufferBarrierInfo.srcAccesses = gfx.AccessType.VERTEX_ATTRIBUTE_READ;
bufferBarrierInfo.dstAccesses = gfx.AccessType.VERTEX_SHADER_READ;
```
### 7. BufferInfo
#### 说明
`BufferInfo` 类用于描述缓冲区的创建信息，在创建 `Buffer` 实例时，需要提供 `BufferInfo` 对象来指定缓冲区的使用方式、大小、步长等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 BufferInfo 实例
const bufferInfo = new gfx.BufferInfo({ 
    usage: gfx.BufferUsage.VERTEX, 
    size: 1024, 
    stride: 12 
}); 
// 使用 BufferInfo 创建缓冲区
const buffer = new gfx.Buffer(gfx.Device.instance, bufferInfo);
```
### 8. BufferTextureCopy
#### 说明
`BufferTextureCopy` 类用于描述缓冲区和纹理之间的复制操作信息，在图形渲染中，有时需要将缓冲区中的数据复制到纹理中，或者将纹理中的数据复制到缓冲区中，该类用于配置这些复制操作的参数。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 BufferTextureCopy 实例
const bufferTextureCopy = new gfx.BufferTextureCopy();
// 设置复制操作的属性
bufferTextureCopy.bufferOffset = 0;
bufferTextureCopy.textureSubresource = { mipLevel: 0, arrayLayer: 0 };
```
### 9. BufferViewInfo
#### 说明
`BufferViewInfo` 类用于描述缓冲区视图的创建信息，缓冲区视图允许开发者以不同的方式访问缓冲区中的数据，例如指定数据的偏移量、范围等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 BufferViewInfo 实例
const bufferViewInfo = new gfx.BufferViewInfo({ 
    buffer: vertexBuffer, 
    offset: 0, 
    range: 512 
}); 
// 使用 BufferViewInfo 创建缓冲区视图
const bufferView = new gfx.BufferView(gfx.Device.instance, bufferViewInfo);
```
### 10. Color
#### 说明
`Color` 类用于表示颜色，在图形渲染中，颜色是一个重要的属性，用于设置物体的外观、光照效果等。该类提供了方便的方法来创建和操作颜色值。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建一个红色的 Color 实例
const redColor = new gfx.Color(1, 0, 0, 1);
```
### 11. ColorAttachment
#### 说明
`ColorAttachment` 类用于描述颜色附件，在图形渲染中，颜色附件是帧缓冲的一部分，用于存储渲染过程中的颜色数据。该类用于配置颜色附件的格式、清除值等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ColorAttachment 实例
const colorAttachment = new gfx.ColorAttachment();
// 设置颜色附件的属性
colorAttachment.format = gfx.Format.RGBA8_UNORM;
colorAttachment.clearValue = new gfx.Color(0, 0, 0, 1);
```
### 12. CommandBuffer
#### 说明
`CommandBuffer` 类表示 GFX 命令缓冲，用于存储和执行一系列的图形命令。在图形渲染中，命令缓冲是将各种渲染操作（如绘制、清除、复制等）组织和提交给图形硬件的重要工具。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 CommandBuffer 实例
const commandBuffer = new gfx.CommandBuffer(gfx.Device.instance, { 
    type: gfx.CommandBufferType.GRAPHICS 
}); 
// 记录绘制命令
commandBuffer.beginRenderPass(renderPass, framebuffer);
commandBuffer.bindPipelineState(pipelineState);
commandBuffer.bindDescriptorSet(0, descriptorSet);
commandBuffer.draw(drawInfo);
commandBuffer.endRenderPass();
```
### 13. CommandBufferInfo
#### 说明
`CommandBufferInfo` 类用于描述命令缓冲的创建信息，在创建 `CommandBuffer` 实例时，需要提供 `CommandBufferInfo` 对象来指定命令缓冲的类型、用途等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 CommandBufferInfo 实例
const commandBufferInfo = new gfx.CommandBufferInfo({ 
    type: gfx.CommandBufferType.GRAPHICS 
}); 
// 使用 CommandBufferInfo 创建命令缓冲
const commandBuffer = new gfx.CommandBuffer(gfx.Device.instance, commandBufferInfo);
```
### 14. DefaultResource
#### 说明
`DefaultResource` 类提供了一些默认的图形资源，如默认的纹理、缓冲区等。在开发过程中，当没有特定的资源可用时，可以使用这些默认资源来确保渲染的正常进行。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 获取默认的纹理资源
const defaultTexture = gfx.DefaultResource.defaultTexture;
```
### 15. DepthStencilAttachment
#### 说明
`DepthStencilAttachment` 类用于描述深度模板附件，在图形渲染中，深度模板附件用于存储深度信息和模板信息，用于实现深度测试和模板测试等功能。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DepthStencilAttachment 实例
const depthStencilAttachment = new gfx.DepthStencilAttachment();
// 设置深度模板附件的属性
    depthStencilAttachment.format = gfx.Format.DEPTH_STENCIL;
    depthStencilAttachment.clearDepth = 1.0;
    depthStencilAttachment.clearStencil = 0;
```
### 16. DepthStencilState
#### 说明
`DepthStencilState` 类表示 GFX 深度模板状态，用于配置深度测试和模板测试的参数，如深度比较函数、模板操作等。通过设置不同的深度模板状态，可以实现不同的渲染效果，如遮挡、裁剪等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DepthStencilState 实例
const depthStencilState = new gfx.DepthStencilState();
// 设置深度模板状态的属性
    depthStencilState.depthTest = true;
    depthStencilState.depthWrite = true;
    depthStencilState.depthFunc = gfx.ComparisonFunc.LESS;
```
### 17. DescriptorSet
#### 说明
`DescriptorSet` 类表示 GFX 描述符集组，用于管理和绑定资源（如缓冲区、纹理等）到着色器中的特定位置。描述符集是图形渲染中资源管理的重要机制，通过它可以方便地更新和切换不同的资源。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DescriptorSet 实例
const descriptorSet = new gfx.DescriptorSet(gfx.Device.instance, descriptorSetLayout);
// 更新描述符集的资源
    descriptorSet.bindBuffer(0, vertexBuffer);
    descriptorSet.bindTexture(1, texture);
```
### 18. DescriptorSetInfo
#### 说明
`DescriptorSetInfo` 类用于描述描述符集的创建信息，在创建 `DescriptorSet` 实例时，需要提供 `DescriptorSetInfo` 对象来指定描述符集的布局等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DescriptorSetInfo 实例
const descriptorSetInfo = new gfx.DescriptorSetInfo({ 
    layout: descriptorSetLayout 
}); 
// 使用 DescriptorSetInfo 创建描述符集
const descriptorSet = new gfx.DescriptorSet(gfx.Device.instance, descriptorSetInfo);
```
### 19. DescriptorSetLayout
#### 说明
`DescriptorSetLayout` 类表示 GFX 描述符集布局，用于定义描述符集的结构和绑定规则。描述符集布局是描述符集的基础，它规定了每个绑定点的类型、数量和用途。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DescriptorSetLayoutBinding 数组
const bindings = [
    new gfx.DescriptorSetLayoutBinding({ 
        binding: 0, 
        descriptorType: gfx.DescriptorType.UNIFORM_BUFFER, 
        count: 1 
    }), 
    new gfx.DescriptorSetLayoutBinding({ 
        binding: 1, 
        descriptorType: gfx.DescriptorType.SAMPLER_TEXTURE, 
        count: 1 
    }) 
]; 
// 创建 DescriptorSetLayout 实例
const descriptorSetLayout = new gfx.DescriptorSetLayout(gfx.Device.instance, { 
    bindings: bindings 
}); 
```
### 20. DescriptorSetLayoutBinding
#### 说明
`DescriptorSetLayoutBinding` 类用于描述描述符集布局中的单个绑定信息，它定义了每个绑定点的具体属性，如绑定编号、描述符类型、数量等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DescriptorSetLayoutBinding 实例
const binding = new gfx.DescriptorSetLayoutBinding({ 
    binding: 0, 
    descriptorType: gfx.DescriptorType.UNIFORM_BUFFER, 
    count: 1 
}); 
```
### 21. DescriptorSetLayoutInfo
#### 说明
`DescriptorSetLayoutInfo` 类用于描述描述符集布局的创建信息，在创建 `DescriptorSetLayout` 实例时，需要提供 `DescriptorSetLayoutInfo` 对象来指定绑定信息等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DescriptorSetLayoutBinding 数组
const bindings = [
    new gfx.DescriptorSetLayoutBinding({ 
        binding: 0, 
        descriptorType: gfx.DescriptorType.UNIFORM_BUFFER, 
        count: 1 
    }), 
    new gfx.DescriptorSetLayoutBinding({ 
        binding: 1, 
        descriptorType: gfx.DescriptorType.SAMPLER_TEXTURE, 
        count: 1 
    }) 
]; 
// 创建 DescriptorSetLayoutInfo 实例
const descriptorSetLayoutInfo = new gfx.DescriptorSetLayoutInfo({ 
    bindings: bindings 
}); 
// 使用 DescriptorSetLayoutInfo 创建描述符集布局
const descriptorSetLayout = new gfx.DescriptorSetLayout(gfx.Device.instance, descriptorSetLayoutInfo);
```
### 22. Device
#### 说明
`Device` 类表示 GFX 设备，是图形渲染的核心对象，负责管理图形硬件的资源和操作。通过 `Device` 类可以创建各种图形资源（如缓冲区、纹理、命令缓冲等），并执行图形命令。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 获取 GFX 设备实例
const device = gfx.Device.instance;
// 使用设备创建缓冲区
const buffer = device.createBuffer({ 
    usage: gfx.BufferUsage.VERTEX, 
    size: 1024, 
    stride: 12 
}); 
```
### 23. DeviceCaps
#### 说明
`DeviceCaps` 类用于描述图形设备的能力信息，包括支持的纹理格式、最大缓冲区大小、最大纹理尺寸等。通过查询设备的能力信息，可以根据不同的硬件环境进行优化和适配。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 获取 GFX 设备实例
const device = gfx.Device.instance;
// 获取设备的能力信息
const deviceCaps = device.caps;
// 查询设备支持的最大纹理尺寸
const maxTextureSize = deviceCaps.maxTextureSize;
```
### 24. DeviceInfo
#### 说明
`DeviceInfo` 类用于描述图形设备的创建信息，在初始化 `Device` 实例时，需要提供 `DeviceInfo` 对象来指定设备的一些基本属性，如设备名称、版本等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DeviceInfo 实例
const deviceInfo = new gfx.DeviceInfo({ 
    name: 'My Graphics Device', 
    version: '1.0' 
}); 
// 使用 DeviceInfo 初始化设备
const device = new gfx.Device(deviceInfo);
```
### 25. DeviceManager
#### 说明
`DeviceManager` 类用于管理图形设备的生命周期和资源，它负责设备的初始化、销毁和资源的分配、释放等操作。通过 `DeviceManager` 可以更方便地管理多个图形设备。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 获取 DeviceManager 实例
const deviceManager = gfx.DeviceManager.instance;
// 注册设备
deviceManager.registerDevice(device);
```
### 26. DeviceOptions
#### 说明
`DeviceOptions` 类用于描述图形设备的初始化选项，在创建 `Device` 实例时，可以通过 `DeviceOptions` 对象来指定一些可选的配置，如是否启用调试模式、使用的渲染后端等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DeviceOptions 实例
const deviceOptions = new gfx.DeviceOptions({ 
    debug: true, 
    backend: gfx.BackendType.WEBGL2 
}); 
// 使用 DeviceOptions 创建设备
const device = new gfx.Device(deviceOptions);
```
### 27. DispatchInfo
#### 说明
`DispatchInfo` 类用于描述计算着色器的调度信息，在使用计算着色器进行并行计算时，需要通过 `DispatchInfo` 对象来指定工作组的数量和大小。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DispatchInfo 实例
const dispatchInfo = new gfx.DispatchInfo({ 
    x: 10, 
    y: 10, 
    z: 1 
}); 
// 在命令缓冲中记录调度命令
commandBuffer.dispatch(dispatchInfo);
```
### 28. DrawInfo
#### 说明
`DrawInfo` 类用于描述绘制命令的信息，包括绘制的图元类型、顶点数量、索引数量等。在记录绘制命令时，需要提供 `DrawInfo` 对象来指定绘制的具体参数。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DrawInfo 实例
const drawInfo = new gfx.DrawInfo({ 
    primitive: gfx.PrimitiveType.TRIANGLES, 
    vertexCount: 6, 
    indexCount: 0 
}); 
// 在命令缓冲中记录绘制命令
commandBuffer.draw(drawInfo);
```
### 29. DynamicStates
#### 说明
`DynamicStates` 类用于描述动态状态，在图形渲染中，动态状态是指在渲染过程中可以动态改变的状态，如视口、裁剪矩形等。通过 `DynamicStates` 类可以方便地管理和更新这些动态状态。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DynamicStates 实例
const dynamicStates = new gfx.DynamicStates();
// 设置动态状态
    dynamicStates.viewport = new gfx.Viewport(0, 0, 800, 600);
    dynamicStates.scissor = new gfx.Rect(0, 0, 800, 600);
```
### 30. DynamicStencilStates
#### 说明
`DynamicStencilStates` 类用于描述动态模板状态，它是动态状态的一部分，用于在渲染过程中动态改变模板测试的参数。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 DynamicStencilStates 实例
const dynamicStencilStates = new gfx.DynamicStencilStates();
// 设置动态模板状态
    dynamicStencilStates.front.failOp = gfx.StencilOp.KEEP;
    dynamicStencilStates.front.passOp = gfx.StencilOp.REPLACE;
```
### 31. EmptyDevice
#### 说明
`EmptyDevice` 类是一个空的图形设备实现，通常用于在没有可用图形设备时提供一个占位符，避免程序崩溃。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 EmptyDevice 实例
const emptyDevice = new gfx.EmptyDevice();
```
### 32. Extent
#### 说明
`Extent` 类用于表示二维或三维的范围，通常用于描述纹理、帧缓冲等的尺寸。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 Extent 实例
const extent = new gfx.Extent(800, 600);
```
### 33. FormatInfo
#### 说明
`FormatInfo` 类用于描述纹理和缓冲区的格式信息，包括格式的大小、通道数量、数据类型等。通过 `FormatInfo` 可以了解不同格式的特性，以便正确地使用和处理数据。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 获取某种格式的信息
const formatInfo = gfx.FormatInfo.get(gfx.Format.RGBA8_UNORM);
// 查询格式的大小
const formatSize = formatInfo.size;
```
### 34. Framebuffer
#### 说明
`Framebuffer` 类表示 GFX 帧缓冲，是图形渲染的目标对象，用于存储渲染过程中的颜色、深度和模板数据。通过 `Framebuffer` 可以将渲染结果输出到屏幕或纹理中。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 FramebufferInfo 实例
const framebufferInfo = new gfx.FramebufferInfo({ 
    colorAttachments: [colorAttachment], 
    depthStencilAttachment: depthStencilAttachment 
}); 
// 使用 FramebufferInfo 创建帧缓冲
const framebuffer = new gfx.Framebuffer(gfx.Device.instance, framebufferInfo);
```
### 35. FramebufferInfo
#### 说明
`FramebufferInfo` 类用于描述帧缓冲的创建信息，在创建 `Framebuffer` 实例时，需要提供 `FramebufferInfo` 对象来指定颜色附件、深度模板附件等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ColorAttachment 和 DepthStencilAttachment 实例
const colorAttachment = new gfx.ColorAttachment();
const depthStencilAttachment = new gfx.DepthStencilAttachment();
// 创建 FramebufferInfo 实例
const framebufferInfo = new gfx.FramebufferInfo({ 
    colorAttachments: [colorAttachment], 
    depthStencilAttachment: depthStencilAttachment 
}); 
// 使用 FramebufferInfo 创建帧缓冲
const framebuffer = new gfx.Framebuffer(gfx.Device.instance, framebufferInfo);
```
### 36. GeneralBarrier
#### 说明
`GeneralBarrier` 类表示 GFX 全局内存屏障，用于确保在不同的渲染操作之间，全局内存的数据能够正确地同步和更新。全局内存屏障可以避免数据竞争和不一致的问题。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 GeneralBarrier 实例
const generalBarrier = new gfx.GeneralBarrier();
// 设置全局内存屏障的属性
    generalBarrier.srcAccesses = gfx.AccessType.VERTEX_ATTRIBUTE_READ;
    generalBarrier.dstAccesses = gfx.AccessType.VERTEX_SHADER_READ;
```
### 37. GeneralBarrierInfo
#### 说明
`GeneralBarrierInfo` 类用于描述全局内存屏障的创建信息，在创建 `GeneralBarrier` 实例时，需要提供 `GeneralBarrierInfo` 对象来指定源访问类型、目标访问类型等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 GeneralBarrierInfo 实例
const generalBarrierInfo = new gfx.GeneralBarrierInfo({ 
    srcAccesses: gfx.AccessType.VERTEX_ATTRIBUTE_READ, 
    dstAccesses: gfx.AccessType.VERTEX_SHADER_READ 
}); 
// 使用 GeneralBarrierInfo 创建全局内存屏障
const generalBarrier = new gfx.GeneralBarrier(gfx.Device.instance, generalBarrierInfo);
```
### 38. GraphicsPipelineState
#### 说明
`GraphicsPipelineState` 类表示图形管线状态，用于配置图形渲染的各个阶段，如顶点着色器、片元着色器、混合状态、深度模板状态等。通过设置不同的图形管线状态，可以实现不同的渲染效果。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 GraphicsPipelineStateInfo 实例
const graphicsPipelineStateInfo = new gfx.GraphicsPipelineStateInfo({ 
    vertexShader: vertexShader, 
    fragmentShader: fragmentShader, 
    blendState: blendState, 
    depthStencilState: depthStencilState 
}); 
// 使用 GraphicsPipelineStateInfo 创建图形管线状态
const graphicsPipelineState = new gfx.GraphicsPipelineState(gfx.Device.instance, graphicsPipelineStateInfo);
```
### 39. GraphicsPipelineStateInfo
#### 说明
`GraphicsPipelineStateInfo` 类用于描述图形管线状态的创建信息，在创建 `GraphicsPipelineState` 实例时，需要提供 `GraphicsPipelineStateInfo` 对象来指定各种管线状态的属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建顶点着色器和片元着色器实例
const vertexShader = new gfx.Shader(gfx.Device.instance, vertexShaderSource);
const fragmentShader = new gfx.Shader(gfx.Device.instance, fragmentShaderSource);
// 创建 BlendState 和 DepthStencilState 实例
const blendState = new gfx.BlendState();
const depthStencilState = new gfx.DepthStencilState();
// 创建 GraphicsPipelineStateInfo 实例
const graphicsPipelineStateInfo = new gfx.GraphicsPipelineStateInfo({ 
    vertexShader: vertexShader, 
    fragmentShader: fragmentShader, 
    blendState: blendState, 
    depthStencilState: depthStencilState 
}); 
// 使用 GraphicsPipelineStateInfo 创建图形管线状态
const graphicsPipelineState = new gfx.GraphicsPipelineState(gfx.Device.instance, graphicsPipelineStateInfo);
```
### 40. Handle
#### 说明
`Handle` 类是一个通用的句柄类，用于表示图形资源的引用。在图形渲染中，为了提高性能和资源管理的效率，通常使用句柄来引用资源，而不是直接使用资源对象。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建一个缓冲区资源
const buffer = new gfx.Buffer(gfx.Device.instance, { 
    usage: gfx.BufferUsage.VERTEX, 
    size: 1024, 
    stride: 12 
}); 
// 获取缓冲区的句柄
const bufferHandle = buffer.handle;
```
### 41. ImageCopy
#### 说明
`ImageCopy` 类用于描述图像复制操作的信息，在图形渲染中，有时需要将一个图像的内容复制到另一个图像中，该类用于配置这些复制操作的参数。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ImageCopy 实例
const imageCopy = new gfx.ImageCopy();
// 设置图像复制操作的属性
    imageCopy.srcSubresource = { mipLevel: 0, arrayLayer: 0 };
    imageCopy.dstSubresource = { mipLevel: 0, arrayLayer: 0 };
```
### 42. ImageInfo
#### 说明
`ImageInfo` 类用于描述图像的创建信息，在创建图像资源（如纹理）时，需要提供 `ImageInfo` 对象来指定图像的格式、尺寸、层级数等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ImageInfo 实例
const imageInfo = new gfx.ImageInfo({ 
    format: gfx.Format.RGBA8_UNORM, 
    width: 800, 
    height: 600, 
    mipLevels: 1 
}); 
// 使用 ImageInfo 创建图像资源
const image = new gfx.Image(gfx.Device.instance, imageInfo);
```
### 43. ImageLayout
#### 说明
`ImageLayout` 类用于表示图像的布局状态，在图形渲染中，图像的布局状态会影响其在不同操作中的使用方式，如读取、写入、呈现等。通过设置图像的布局状态，可以确保数据的正确访问和同步。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 设置图像的布局状态
const imageLayout = gfx.ImageLayout.SHADER_READ_ONLY_OPTIMAL;
```
### 44. ImageMemoryBarrier
#### 说明
`ImageMemoryBarrier` 类用于描述图像的内存屏障信息，图像内存屏障用于确保在不同的渲染操作之间，图像的数据能够正确地同步和更新。通过设置图像的内存屏障信息，可以避免数据竞争和不一致的问题。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ImageMemoryBarrier 实例
const imageMemoryBarrier = new gfx.ImageMemoryBarrier();
// 设置图像内存屏障的属性
    imageMemoryBarrier.image = texture;
    imageMemoryBarrier.srcLayout = gfx.ImageLayout.TRANSFER_DST_OPTIMAL;
    imageMemoryBarrier.dstLayout = gfx.ImageLayout.SHADER_READ_ONLY_OPTIMAL;
```
### 45. ImageMemoryBarrierInfo
#### 说明
`ImageMemoryBarrierInfo` 类用于描述图像内存屏障的创建信息，在创建 `ImageMemoryBarrier` 实例时，需要提供 `ImageMemoryBarrierInfo` 对象来指定图像、源布局、目标布局等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ImageMemoryBarrierInfo 实例
const imageMemoryBarrierInfo = new gfx.ImageMemoryBarrierInfo({ 
    image: texture, 
    srcLayout: gfx.ImageLayout.TRANSFER_DST_OPTIMAL, 
    dstLayout: gfx.ImageLayout.SHADER_READ_ONLY_OPTIMAL 
}); 
// 使用 ImageMemoryBarrierInfo 创建图像内存屏障
const imageMemoryBarrier = new gfx.ImageMemoryBarrier(gfx.Device.instance, imageMemoryBarrierInfo);
```
### 46. ImageSubresource
#### 说明
`ImageSubresource` 类用于描述图像的子资源信息，图像可以包含多个层级（mip levels）和数组层（array layers），`ImageSubresource` 类用于指定具体的子资源。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ImageSubresource 实例
const imageSubresource = new gfx.ImageSubresource({ 
    mipLevel: 0, 
    arrayLayer: 0 
}); 
```
### 47. ImageSubresourceRange
#### 说明
`ImageSubresourceRange` 类用于描述图像子资源的范围信息，它指定了图像的哪些子资源（层级和数组层）将被操作。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ImageSubresourceRange 实例
const imageSubresourceRange = new gfx.ImageSubresourceRange({ 
    baseMipLevel: 0, 
    levelCount: 1, 
    baseArrayLayer: 0, 
    layerCount: 1 
}); 
```
### 48. IndirectBuffer
#### 说明
`IndirectBuffer` 类用于存储间接绘制命令，间接绘制是一种高效的绘制方式，通过在缓冲区中存储绘制命令，可以减少 CPU 与 GPU 之间的通信开销。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 IndirectBuffer 实例
const indirectBuffer = new gfx.IndirectBuffer(gfx.Device.instance, { 
    usage: gfx.BufferUsage.INDIRECT, 
    size: 1024 
}); 
// 向间接缓冲区写入绘制命令
const data = new Uint32Array([1, 2, 3, 4]);
indirectBuffer.update(data);
```
### 49. IndirectDraw
#### 说明
`IndirectDraw` 类用于描述间接绘制命令的信息，它包含了间接绘制所需的各种参数，如顶点数量、实例数量等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 IndirectDraw 实例
const indirectDraw = new gfx.IndirectDraw();
// 设置间接绘制命令的属性
    indirectDraw.vertexCount = 6;
    indirectDraw.instanceCount = 1;
```
### 50. InputAssembler
#### 说明
`InputAssembler` 类用于管理顶点数据的输入和装配，在图形渲染中，输入装配阶段负责将顶点数据从缓冲区中读取出来，并按照指定的图元类型进行装配。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 InputAssemblerInfo 实例
const inputAssemblerInfo = new gfx.InputAssemblerInfo({ 
    vertexBuffers: [vertexBuffer], 
    indexBuffer: indexBuffer, 
    attributes: [positionAttribute] 
}); 
// 使用 InputAssemblerInfo 创建输入装配器
const inputAssembler = new gfx.InputAssembler(gfx.Device.instance, inputAssemblerInfo);
```
### 51. InputAssemblerInfo
#### 说明
`InputAssemblerInfo` 类用于描述输入装配器的创建信息，在创建 `InputAssembler` 实例时，需要提供 `InputAssemblerInfo` 对象来指定顶点缓冲区、索引缓冲区、顶点属性等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建顶点缓冲区和索引缓冲区实例
const vertexBuffer = new gfx.Buffer(gfx.Device.instance, { 
    usage: gfx.BufferUsage.VERTEX, 
    size: 1024, 
    stride: 12 
}); 
const indexBuffer = new gfx.Buffer(gfx.Device.instance, { 
    usage: gfx.BufferUsage.INDEX, 
    size: 1024, 
    stride: 4 
}); 
// 创建 Attribute 实例
const positionAttribute = new gfx.Attribute('a_position', gfx.Format.FLOAT32_X3);
// 创建 InputAssemblerInfo 实例
const inputAssemblerInfo = new gfx.InputAssemblerInfo({ 
    vertexBuffers: [vertexBuffer], 
    indexBuffer: indexBuffer, 
    attributes: [positionAttribute] 
}); 
// 使用 InputAssemblerInfo 创建输入装配器
const inputAssembler = new gfx.InputAssembler(gfx.Device.instance, inputAssemblerInfo);
```
### 52. MemoryBarrier
#### 说明
`MemoryBarrier` 类用于描述内存屏障，内存屏障用于确保在不同的渲染操作之间，内存的数据能够正确地同步和更新。内存屏障可以避免数据竞争和不一致的问题。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 MemoryBarrier 实例
const memoryBarrier = new gfx.MemoryBarrier();
// 设置内存屏障的属性
    memoryBarrier.srcAccesses = gfx.AccessType.VERTEX_ATTRIBUTE_READ;
    memoryBarrier.dstAccesses = gfx.AccessType.VERTEX_SHADER_READ;
```
### 53. MemoryBarrierInfo
#### 说明
`MemoryBarrierInfo` 类用于描述内存屏障的创建信息，在创建 `MemoryBarrier` 实例时，需要提供 `MemoryBarrierInfo` 对象来指定源访问类型、目标访问类型等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 MemoryBarrierInfo 实例
const memoryBarrierInfo = new gfx.MemoryBarrierInfo({ 
    srcAccesses: gfx.AccessType.VERTEX_ATTRIBUTE_READ, 
    dstAccesses: gfx.AccessType.VERTEX_SHADER_READ 
}); 
// 使用 MemoryBarrierInfo 创建内存屏障
const memoryBarrier = new gfx.MemoryBarrier(gfx.Device.instance, memoryBarrierInfo);
```
### 54. Pass
#### 说明
`Pass` 类表示渲染通道，渲染通道定义了一组渲染操作的执行顺序和状态，如颜色附件的清除、绘制操作等。通过组织不同的渲染通道，可以实现复杂的渲染效果。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 PassInfo 实例
const passInfo = new gfx.PassInfo({ 
    colorAttachments: [colorAttachment], 
    depthStencilAttachment: depthStencilAttachment 
}); 
// 使用 PassInfo 创建渲染通道
const pass = new gfx.Pass(gfx.Device.instance, passInfo);
```
### 55. PassInfo
#### 说明
`PassInfo` 类用于描述渲染通道的创建信息，在创建 `Pass` 实例时，需要提供 `PassInfo` 对象来指定颜色附件、深度模板附件、混合状态等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ColorAttachment 和 DepthStencilAttachment 实例
const colorAttachment = new gfx.ColorAttachment();
const depthStencilAttachment = new gfx.DepthStencilAttachment();
// 创建 PassInfo 实例
const passInfo = new gfx.PassInfo({ 
    colorAttachments: [colorAttachment], 
    depthStencilAttachment: depthStencilAttachment 
}); 
// 使用 PassInfo 创建渲染通道
const pass = new gfx.Pass(gfx.Device.instance, passInfo);
```
### 56. PipelineState
#### 说明
`PipelineState` 类是图形管线状态的基类，它包含了图形渲染的各种状态信息，如着色器、混合状态、深度模板状态等。不同类型的管线状态（如图形管线状态、计算管线状态）都继承自该类。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 GraphicsPipelineStateInfo 实例
const graphicsPipelineStateInfo = new gfx.GraphicsPipelineStateInfo({ 
    vertexShader: vertexShader, 
    fragmentShader: fragmentShader, 
    blendState: blendState, 
    depthStencilState: depthStencilState 
}); 
// 使用 GraphicsPipelineStateInfo 创建图形管线状态
const graphicsPipelineState = new gfx.GraphicsPipelineState(gfx.Device.instance, graphicsPipelineStateInfo);
```
### 57. PipelineStateInfo
#### 说明
`PipelineStateInfo` 类是图形管线状态信息的基类，用于描述管线状态的创建信息。不同类型的管线状态信息（如图形管线状态信息、计算管线状态信息）都继承自该类。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建顶点着色器和片元着色器实例
const vertexShader = new gfx.Shader(gfx.Device.instance, vertexShaderSource);
const fragmentShader = new gfx.Shader(gfx.Device.instance, fragmentShaderSource);
// 创建 BlendState 和 DepthStencilState 实例
const blendState = new gfx.BlendState();
const depthStencilState = new gfx.DepthStencilState();
// 创建 GraphicsPipelineStateInfo 实例
const graphicsPipelineStateInfo = new gfx.GraphicsPipelineStateInfo({ 
    vertexShader: vertexShader, 
    fragmentShader: fragmentShader, 
    blendState: blendState, 
    depthStencilState: depthStencilState 
}); 
// 使用 GraphicsPipelineStateInfo 创建图形管线状态
const graphicsPipelineState = new gfx.GraphicsPipelineState(gfx.Device.instance, graphicsPipelineStateInfo);
```
### 58. Query
#### 说明
`Query` 类用于进行查询操作，在图形渲染中，查询可以用于获取一些性能信息，如绘制调用次数、时间统计等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 Query 实例
const query = new gfx.Query(gfx.Device.instance, { 
    type: gfx.QueryType.TIMESTAMP 
}); 
// 开始查询
commandBuffer.beginQuery(query);
// 执行一些渲染操作
// ...
// 结束查询
commandBuffer.endQuery(query);
```
### 59. QueryInfo
#### 说明
`QueryInfo` 类用于描述查询的创建信息，在创建 `Query` 实例时，需要提供 `QueryInfo` 对象来指定查询的类型、数量等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 QueryInfo 实例
const queryInfo = new gfx.QueryInfo({ 
    type: gfx.QueryType.TIMESTAMP, 
    count: 1 
}); 
// 使用 QueryInfo 创建查询
const query = new gfx.Query(gfx.Device.instance, queryInfo);
```
### 60. QueryPool
#### 说明
`QueryPool` 类用于管理查询对象的池，通过使用查询池可以提高查询对象的创建和管理效率。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 QueryPool 实例
const queryPool = new gfx.QueryPool(gfx.Device.instance, { 
    type: gfx.QueryType.TIMESTAMP, 
    count: 10 
}); 
// 从查询池中获取查询对象
const query = queryPool.getQuery();
```
### 61. Queue
#### 说明
`Queue` 类表示图形命令队列，用于存储和执行图形命令。图形命令队列是将命令缓冲中的命令提交给图形硬件的通道。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 获取图形设备的队列
const queue = gfx.Device.instance.queue;
// 提交命令缓冲
queue.submit([commandBuffer]);
```
### 62. QueueInfo
#### 说明
`QueueInfo` 类用于描述图形命令队列的创建信息，在创建 `Queue` 实例时，需要提供 `QueueInfo` 对象来指定队列的类型、优先级等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 QueueInfo 实例
const queueInfo = new gfx.QueueInfo({ 
    type: gfx.QueueType.GRAPHICS, 
    priority: gfx.QueuePriority.HIGH 
}); 
// 使用 QueueInfo 创建队列
const queue = new gfx.Queue(gfx.Device.instance, queueInfo);
```
### 63. Rect
#### 说明
`Rect` 类用于表示二维矩形区域，通常用于描述视口、裁剪矩形等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 Rect 实例
const rect = new gfx.Rect(0, 0, 800, 600);
```
### 64. RenderPass
#### 说明
`RenderPass` 类表示渲染通道，它定义了一组渲染操作的执行顺序和状态，与 `Pass` 类类似，但 `RenderPass` 更侧重于描述渲染通道的整体结构和配置。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 RenderPassInfo 实例
const renderPassInfo = new gfx.RenderPassInfo({ 
    colorAttachments: [colorAttachment], 
    depthStencilAttachment: depthStencilAttachment 
}); 
// 使用 RenderPassInfo 创建渲染通道
const renderPass = new gfx.RenderPass(gfx.Device.instance, renderPassInfo);
```
### 65. RenderPassInfo
#### 说明
`RenderPassInfo` 类用于描述渲染通道的创建信息，在创建 `RenderPass` 实例时，需要提供 `RenderPassInfo` 对象来指定颜色附件、深度模板附件、子通道等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 ColorAttachment 和 DepthStencilAttachment 实例
const colorAttachment = new gfx.ColorAttachment();
const depthStencilAttachment = new gfx.DepthStencilAttachment();
// 创建 RenderPassInfo 实例
const renderPassInfo = new gfx.RenderPassInfo({ 
    colorAttachments: [colorAttachment], 
    depthStencilAttachment: depthStencilAttachment 
}); 
// 使用 RenderPassInfo 创建渲染通道
const renderPass = new gfx.RenderPass(gfx.Device.instance, renderPassInfo);
```
### 66. RenderTarget
#### 说明
`RenderTarget` 类表示渲染目标，是渲染操作的输出对象，可以是帧缓冲、纹理等。通过设置渲染目标，可以将渲染结果输出到不同的地方。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 RenderTarget 实例
const renderTarget = new gfx.RenderTarget(gfx.Device.instance, { 
    format: gfx.Format.RGBA8_UNORM, 
    width: 800, 
    height: 600 
}); 
```
### 67. RenderTargetInfo
#### 说明
`RenderTargetInfo` 类用于描述渲染目标的创建信息，在创建 `RenderTarget` 实例时，需要提供 `RenderTargetInfo` 对象来指定渲染目标的格式、尺寸等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 RenderTargetInfo 实例
const renderTargetInfo = new gfx.RenderTargetInfo({ 
    format: gfx.Format.RGBA8_UNORM, 
    width: 800, 
    height: 600 
}); 
// 使用 RenderTargetInfo 创建渲染目标
const renderTarget = new gfx.RenderTarget(gfx.Device.instance, renderTargetInfo);
```
### 68. Sampler
#### 说明
`Sampler` 类用于描述纹理采样器，纹理采样器定义了如何从纹理中采样数据，如过滤模式、寻址模式等。通过设置不同的采样器，可以实现不同的纹理采样效果。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 SamplerInfo 实例
const samplerInfo = new gfx.SamplerInfo({ 
    magFilter: gfx.FilterMode.LINEAR, 
    minFilter: gfx.FilterMode.LINEAR, 
    addressU: gfx.AddressMode.REPEAT, 
    addressV: gfx.AddressMode.REPEAT 
}); 
// 使用 SamplerInfo 创建采样器
const sampler = new gfx.Sampler(gfx.Device.instance, samplerInfo);
```
### 69. SamplerInfo
#### 说明
`SamplerInfo` 类用于描述纹理采样器的创建信息，在创建 `Sampler` 实例时，需要提供 `SamplerInfo` 对象来指定过滤模式、寻址模式、最大各向异性等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 SamplerInfo 实例
const samplerInfo = new gfx.SamplerInfo({ 
    magFilter: gfx.FilterMode.LINEAR, 
    minFilter: gfx.FilterMode.LINEAR, 
    addressU: gfx.AddressMode.REPEAT, 
    addressV: gfx.AddressMode.REPEAT, 
    maxAnisotropy: 16 
}); 
// 使用 SamplerInfo 创建采样器
const sampler = new gfx.Sampler(gfx.Device.instance, samplerInfo);
```
### 70. Shader
#### 说明
`Shader` 类用于表示着色器，着色器是图形渲染的核心程序，包括顶点着色器、片元着色器等。通过创建和使用着色器，可以实现各种复杂的渲染效果。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建顶点着色器源代码
const vertexShaderSource = `
    attribute vec3 a_position;
    void main() {
        gl_Position = vec4(a_position, 1.0);
    }
`; 
// 创建片元着色器源代码
const fragmentShaderSource = `
    void main() {
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
    }
`; 
// 创建 ShaderInfo 实例
const shaderInfo = new gfx.ShaderInfo({ 
    name: 'MyShader', 
    stages: [
        { 
            type: gfx.ShaderStage.VERTEX, 
            source: vertexShaderSource 
        }, 
        { 
            type: gfx.ShaderStage.FRAGMENT, 
            source: fragmentShaderSource 
        } 
    ] 
}); 
// 使用 ShaderInfo 创建着色器
const shader = new gfx.Shader(gfx.Device.instance, shaderInfo);
```
### 71. ShaderInfo
#### 说明
`ShaderInfo` 类用于描述着色器的创建信息，在创建 `Shader` 实例时，需要提供 `ShaderInfo` 对象来指定着色器的名称、阶段（顶点着色器、片元着色器等）、源代码等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建顶点着色器源代码
const vertexShaderSource = `
    attribute vec3 a_position;
    void main() {
        gl_Position = vec4(a_position, 1.0);
    }
`; 
// 创建片元着色器源代码
const fragmentShaderSource = `
    void main() {
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
    }
`; 
// 创建 ShaderInfo 实例
const shaderInfo = new gfx.ShaderInfo({ 
    name: 'MyShader', 
    stages: [
        { 
            type: gfx.ShaderStage.VERTEX, 
            source: vertexShaderSource 
        }, 
        { 
            type: gfx.ShaderStage.FRAGMENT, 
            source: fragmentShaderSource 
        } 
    ] 
}); 
// 使用 ShaderInfo 创建着色器
const shader = new gfx.Shader(gfx.Device.instance, shaderInfo);
```
### 72. ShaderStage
#### 说明
`ShaderStage` 类用于表示着色器的阶段，包括顶点着色器、片元着色器、计算着色器等。不同的着色器阶段在图形渲染中承担不同的任务。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 获取顶点着色器阶段
const vertexShaderStage = gfx.ShaderStage.VERTEX;
```
### 73. Swapchain
#### 说明
`Swapchain` 类用于管理交换链，交换链是图形渲染中用于双缓冲或多缓冲的机制，通过交换链可以实现平滑的画面切换和显示。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 SwapchainInfo 实例
const swapchainInfo = new gfx.SwapchainInfo({ 
    width: 800, 
    height: 600, 
    format: gfx.Format.RGBA8_UNORM 
}); 
// 使用 SwapchainInfo 创建交换链
const swapchain = new gfx.Swapchain(gfx.Device.instance, swapchainInfo);
```
### 74. SwapchainInfo
#### 说明
`SwapchainInfo` 类用于描述交换链的创建信息，在创建 `Swapchain` 实例时，需要提供 `SwapchainInfo` 对象来指定交换链的宽度、高度、格式等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 SwapchainInfo 实例
const swapchainInfo = new gfx.SwapchainInfo({ 
    width: 800, 
    height: 600, 
    format: gfx.Format.RGBA8_UNORM 
}); 
// 使用 SwapchainInfo 创建交换链
const swapchain = new gfx.Swapchain(gfx.Device.instance, swapchainInfo);
```
### 75. Texture
#### 说明
`Texture` 类用于表示纹理，纹理是图形渲染中用于存储图像数据的对象，可以用于材质、光照等效果的实现。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 TextureInfo 实例
const textureInfo = new gfx.TextureInfo({ 
    format: gfx.Format.RGBA8_UNORM, 
    width: 800, 
    height: 600, 
    mipLevels: 1 
}); 
// 使用 TextureInfo 创建纹理
const texture = new gfx.Texture(gfx.Device.instance, textureInfo);
```
### 76. TextureBarrier
#### 说明
`TextureBarrier` 类用于描述纹理的内存屏障信息，纹理内存屏障用于确保在不同的渲染操作之间，纹理的数据能够正确地同步和更新。通过设置纹理的内存屏障信息，可以避免数据竞争和不一致的问题。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 TextureBarrier 实例
const textureBarrier = new gfx.TextureBarrier();
// 设置纹理内存屏障的属性
    textureBarrier.texture = texture;
    textureBarrier.srcLayout = gfx.ImageLayout.TRANSFER_DST_OPTIMAL;
    textureBarrier.dstLayout = gfx.ImageLayout.SHADER_READ_ONLY_OPTIMAL;
```
### 77. TextureBarrierInfo
#### 说明
`TextureBarrierInfo` 类用于描述纹理内存屏障的创建信息，在创建 `TextureBarrier` 实例时，需要提供 `TextureBarrierInfo` 对象来指定纹理、源布局、目标布局等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 TextureBarrierInfo 实例
const textureBarrierInfo = new gfx.TextureBarrierInfo({ 
    texture: texture, 
    srcLayout: gfx.ImageLayout.TRANSFER_DST_OPTIMAL, 
    dstLayout: gfx.ImageLayout.SHADER_READ_ONLY_OPTIMAL 
}); 
// 使用 TextureBarrierInfo 创建纹理内存屏障
const textureBarrier = new gfx.TextureBarrier(gfx.Device.instance, textureBarrierInfo);
```
### 78. TextureCopy
#### 说明
`TextureCopy` 类用于描述纹理复制操作的信息，在图形渲染中，有时需要将一个纹理的内容复制到另一个纹理中，该类用于配置这些复制操作的参数。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 TextureCopy 实例
const textureCopy = new gfx.TextureCopy();
// 设置纹理复制操作的属性
    textureCopy.srcSubresource = { mipLevel: 0, arrayLayer: 0 };
    textureCopy.dstSubresource = { mipLevel: 0, arrayLayer: 0 };
```
### 79. TextureInfo
#### 说明
`TextureInfo` 类用于描述纹理的创建信息，在创建 `Texture` 实例时，需要提供 `TextureInfo` 对象来指定纹理的格式、尺寸、层级数、用途等属性。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 TextureInfo 实例
const textureInfo = new gfx.TextureInfo({ 
    format: gfx.Format.RGBA8_UNORM, 
    width: 800, 
    height: 600, 
    mipLevels: 1, 
    usage: gfx.TextureUsage.SAMPLED 
}); 
// 使用 TextureInfo 创建纹理
const texture = new gfx.Texture(gfx.Device.instance, textureInfo);
```
### 80. TextureUsage
#### 说明
`TextureUsage` 类用于表示纹理的使用方式，定义了纹理可以用于哪些操作，如采样、存储、渲染目标等。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 设置纹理的使用方式
const textureUsage = gfx.TextureUsage.SAMPLED | gfx.TextureUsage.RENDER_TARGET;
```
### 81. Viewport
#### 说明
`Viewport` 类用于表示视口，视口定义了渲染结果在屏幕上的显示区域。通过设置视口，可以控制渲染结果的显示位置和大小。
#### 使用方式
```typescript
import { gfx } from 'cc';
// 创建 Viewport 实例
const viewport = new gfx.Viewport(0, 0, 800, 600);
```

## 三、总结
图形后端抽象模块提供了一系列丰富的接口和类，用于处理图形渲染中的各种操作和资源管理。通过这些接口，开发者可以在不同的图形硬件和 API 上实现统一的图形渲染逻辑，提高开发效率和代码的可移植性。在实际开发中，需要根据具体的需求选择合适的类和接口，并正确配置它们的属性，以实现预期的渲染效果。