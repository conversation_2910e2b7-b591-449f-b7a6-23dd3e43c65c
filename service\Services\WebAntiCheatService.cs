using WebApplication_HM.Models;
using SqlSugar;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// Web版反作弊检测服务 (优化版)
    /// 专为Web环境设计，去除单机游戏特有的检测逻辑
    /// </summary>
    public class WebAntiCheatService
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<WebAntiCheatService> _logger;

        // 用户战斗时间记录 (内存缓存)
        private static readonly Dictionary<int, DateTime> _userLastBattleTime = new();
        private static readonly Dictionary<int, List<TimeSpan>> _userBattleDurations = new();

        // 累计违规计数器 (分级处理)
        private static readonly Dictionary<int, int> _suspiciousCount = new();
        private static readonly Dictionary<int, int> _frequencyViolations = new();
        private static readonly Dictionary<int, int> _patternViolations = new();

        public WebAntiCheatService(ISqlSugarClient db, ILogger<WebAntiCheatService> logger)
        {
            _db = db;
            _logger = logger;
        }
        
        /// <summary>
        /// 验证战斗时间间隔 (Web环境优化版)
        /// 放宽时间限制，采用累计判断避免误判
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="battleDuration">战斗持续时间(毫秒)</param>
        /// <param name="rounds">战斗回合数</param>
        /// <returns>是否通过验证</returns>
        public bool ValidateBattleTiming(int userId, long battleDuration, int rounds)
        {
            try
            {
                if (rounds <= 0) return true;

                // Web环境放宽时间限制: 每回合不少于500ms (考虑网络延迟)
                var avgTimePerRound = battleDuration / rounds;
                var minTimePerRound = 500; // 从原项目的820ms降到500ms

                if (avgTimePerRound < minTimePerRound)
                {
                    // 累计可疑次数，避免单次误判
                    if (!_suspiciousCount.ContainsKey(userId))
                        _suspiciousCount[userId] = 0;

                    _suspiciousCount[userId]++;

                    _logger.LogWarning($"用户 {userId} 战斗速度可疑: 平均每回合 {avgTimePerRound}ms (累计: {_suspiciousCount[userId]}次)");

                    // 累计5次可疑行为才判定为作弊
                    if (_suspiciousCount[userId] >= 5)
                    {
                        LogSuspiciousActivity(userId, "SPEED_CHEAT_ACCUMULATED", new
                        {
                            BattleDuration = battleDuration,
                            Rounds = rounds,
                            AveragePerRound = avgTimePerRound,
                            Threshold = minTimePerRound,
                            AccumulatedCount = _suspiciousCount[userId]
                        });

                        return false;
                    }

                    // 单次可疑但允许通过
                    return true;
                }
                else
                {
                    // 重置可疑计数器
                    _suspiciousCount[userId] = 0;
                }

                // 记录正常的战斗时间
                RecordBattleDuration(userId, TimeSpan.FromMilliseconds(battleDuration));

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证战斗时间失败: UserId={userId}");
                return true; // 验证失败时默认通过，避免影响正常用户
            }
        }
        
        /// <summary>
        /// 检测异常请求频率 (Web环境优化版)
        /// 降低阈值，采用累计判断
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否通过验证</returns>
        public bool ValidateRequestFrequency(int userId)
        {
            try
            {
                var now = DateTime.Now;
                var minInterval = 200; // 从500ms降到200ms，适应Web环境

                if (_userLastBattleTime.ContainsKey(userId))
                {
                    var timeSinceLastBattle = now - _userLastBattleTime[userId];
                    var interval = timeSinceLastBattle.TotalMilliseconds;

                    if (interval < minInterval)
                    {
                        // 累计频率违规次数
                        if (!_frequencyViolations.ContainsKey(userId))
                            _frequencyViolations[userId] = 0;

                        _frequencyViolations[userId]++;

                        _logger.LogWarning($"用户 {userId} 请求频率过高: 间隔 {interval}ms (累计: {_frequencyViolations[userId]}次)");

                        // 累计10次违规才判定为异常
                        if (_frequencyViolations[userId] > 10)
                        {
                            LogSuspiciousActivity(userId, "HIGH_FREQUENCY_ACCUMULATED", new
                            {
                                Interval = interval,
                                Threshold = minInterval,
                                AccumulatedCount = _frequencyViolations[userId]
                            });

                            return false;
                        }
                    }
                    else
                    {
                        // 重置违规计数器
                        _frequencyViolations[userId] = 0;
                    }
                }

                _userLastBattleTime[userId] = now;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证请求频率失败: UserId={userId}");
                return true;
            }
        }
        
        /// <summary>
        /// 验证伤害数值合理性
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="damage">伤害值</param>
        /// <param name="playerAtk">玩家攻击力</param>
        /// <param name="monsterDef">怪物防御力</param>
        /// <returns>是否通过验证</returns>
        public bool ValidateDamageValue(int userId, long damage, int playerAtk, int monsterDef)
        {
            try
            {
                // 检查伤害是否为异常标记值
                if (damage == long.MaxValue)
                {
                    _logger.LogWarning($"用户 {userId} 伤害计算异常: 检测到作弊标记");
                    
                    LogSuspiciousActivity(userId, "DAMAGE_CHEAT", new 
                    { 
                        Damage = damage,
                        PlayerAtk = playerAtk,
                        MonsterDef = monsterDef
                    });
                    
                    return false;
                }
                
                // 检查伤害是否超出合理范围 (不超过攻击力的10倍)
                if (damage > playerAtk * 10)
                {
                    _logger.LogWarning($"用户 {userId} 伤害值异常: {damage} (攻击力: {playerAtk})");
                    
                    LogSuspiciousActivity(userId, "ABNORMAL_DAMAGE", new 
                    { 
                        Damage = damage,
                        PlayerAtk = playerAtk,
                        Ratio = (double)damage / playerAtk
                    });
                    
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证伤害数值失败: UserId={userId}");
                return true;
            }
        }
        
        /// <summary>
        /// 检测用户行为模式 (Web环境优化版)
        /// 增加样本要求，降低阈值，采用累计判断
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否通过验证</returns>
        public bool DetectBehaviorPattern(int userId)
        {
            try
            {
                if (!_userBattleDurations.ContainsKey(userId))
                {
                    return true;
                }

                var durations = _userBattleDurations[userId];
                if (durations.Count < 20) // 增加样本要求从10次到20次
                {
                    return true; // 数据不足，无法判断
                }

                // 检查战斗时间是否过于规律 (可能是脚本)
                var recentDurations = durations.TakeLast(20).ToList();
                var avgDuration = recentDurations.Average(d => d.TotalMilliseconds);
                var variance = recentDurations.Sum(d => Math.Pow(d.TotalMilliseconds - avgDuration, 2)) / recentDurations.Count;
                var standardDeviation = Math.Sqrt(variance);

                // 降低阈值，增加容忍度 (从100ms降到50ms)
                if (standardDeviation < 50)
                {
                    // 累计模式违规次数
                    if (!_patternViolations.ContainsKey(userId))
                        _patternViolations[userId] = 0;

                    _patternViolations[userId]++;

                    _logger.LogWarning($"用户 {userId} 行为模式可疑: 战斗时间过于规律 (标准差: {standardDeviation:F2}ms, 累计: {_patternViolations[userId]}次)");

                    // 需要多次违规才判定为脚本行为
                    if (_patternViolations[userId] > 3)
                    {
                        LogSuspiciousActivity(userId, "SCRIPT_BEHAVIOR_ACCUMULATED", new
                        {
                            AverageDuration = avgDuration,
                            StandardDeviation = standardDeviation,
                            RecentDurations = recentDurations.Select(d => d.TotalMilliseconds).ToArray(),
                            AccumulatedCount = _patternViolations[userId]
                        });

                        return false;
                    }
                }
                else
                {
                    // 重置模式违规计数器
                    _patternViolations[userId] = 0;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检测行为模式失败: UserId={userId}");
                return true;
            }
        }
        
        /// <summary>
        /// 记录可疑活动
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="cheatType">作弊类型</param>
        /// <param name="data">检测数据</param>
        private void LogSuspiciousActivity(int userId, string cheatType, object data)
        {
            try
            {
                // 这里可以记录到数据库或日志文件
                _logger.LogWarning($"检测到可疑活动: UserId={userId}, Type={cheatType}, Data={System.Text.Json.JsonSerializer.Serialize(data)}");
                
                // 可以考虑记录到专门的反作弊日志表
                // var log = new anti_cheat_log
                // {
                //     user_id = userId,
                //     cheat_type = cheatType,
                //     detection_data = System.Text.Json.JsonSerializer.Serialize(data),
                //     create_time = DateTime.Now
                // };
                // _db.Insertable(log).ExecuteCommand();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录可疑活动失败");
            }
        }
        
        /// <summary>
        /// 记录战斗持续时间
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="duration">持续时间</param>
        private void RecordBattleDuration(int userId, TimeSpan duration)
        {
            if (!_userBattleDurations.ContainsKey(userId))
            {
                _userBattleDurations[userId] = new List<TimeSpan>();
            }
            
            _userBattleDurations[userId].Add(duration);
            
            // 只保留最近50次记录
            if (_userBattleDurations[userId].Count > 50)
            {
                _userBattleDurations[userId].RemoveAt(0);
            }
        }
        
        /// <summary>
        /// 获取用户风险评分
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>风险评分 (0-100)</returns>
        public double GetUserRiskScore(int userId)
        {
            // 这里可以实现复杂的风险评分算法
            // 基于历史记录、行为模式等计算风险分数
            return 0.0; // 暂时返回0，表示无风险
        }
        
        /// <summary>
        /// 检查用户是否被封禁
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否被封禁</returns>
        public async Task<bool> IsUserBannedAsync(int userId)
        {
            try
            {
                var user = await _db.Queryable<user>()
                    .Where(u => u.id == userId)
                    .FirstAsync();

                // 检查用户状态 (0正常 1封禁)
                return user?.status == 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查用户封禁状态失败: UserId={userId}");
                return false;
            }
        }

        /// <summary>
        /// Web环境专用: 验证IP地址异常
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>是否通过验证</returns>
        public bool ValidateIPAddress(int userId, string ipAddress)
        {
            try
            {
                // 这里可以实现IP地址检测逻辑
                // 1. 检测异常IP切换
                // 2. 检测代理/VPN使用
                // 3. 检测地理位置异常

                // 暂时返回true，后续可以扩展
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证IP地址失败: UserId={userId}, IP={ipAddress}");
                return true;
            }
        }

        /// <summary>
        /// Web环境专用: 验证会话完整性
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="sessionToken">会话令牌</param>
        /// <returns>是否通过验证</returns>
        public bool ValidateSession(int userId, string sessionToken)
        {
            try
            {
                // 这里可以实现会话检测逻辑
                // 1. 检测会话劫持
                // 2. 检测并发登录
                // 3. 检测异常设备

                // 暂时返回true，后续可以扩展
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证会话失败: UserId={userId}");
                return true;
            }
        }

        /// <summary>
        /// 重置用户的所有违规计数器
        /// </summary>
        /// <param name="userId">用户ID</param>
        public void ResetUserViolations(int userId)
        {
            _suspiciousCount.Remove(userId);
            _frequencyViolations.Remove(userId);
            _patternViolations.Remove(userId);
            _logger.LogInformation($"已重置用户 {userId} 的违规计数器");
        }

        /// <summary>
        /// 获取用户当前的违规统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>违规统计信息</returns>
        public object GetUserViolationStats(int userId)
        {
            return new
            {
                SuspiciousCount = _suspiciousCount.GetValueOrDefault(userId, 0),
                FrequencyViolations = _frequencyViolations.GetValueOrDefault(userId, 0),
                PatternViolations = _patternViolations.GetValueOrDefault(userId, 0),
                LastBattleTime = _userLastBattleTime.GetValueOrDefault(userId, DateTime.MinValue),
                BattleDurationCount = _userBattleDurations.GetValueOrDefault(userId, new List<TimeSpan>()).Count
            };
        }
    }
}
