using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Sugar;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 宠物进化测试控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PetEvolutionTestController : ControllerBase
    {
        private readonly IPetEvolutionService _evolutionService;
        private readonly DbContext _dbContext;
        private readonly ILogger<PetEvolutionTestController> _logger;

        public PetEvolutionTestController(
            IPetEvolutionService evolutionService, 
            DbContext dbContext,
            ILogger<PetEvolutionTestController> logger)
        {
            _evolutionService = evolutionService;
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 初始化测试数据
        /// </summary>
        /// <returns>初始化结果</returns>
        [HttpPost("init-test-data")]
        public async Task<IActionResult> InitTestData()
        {
            try
            {
                _logger.LogInformation("开始初始化进化系统测试数据");

                // 清理可能存在的测试数据
                await CleanupTestData();

                // 创建测试用户
                var testUser = new user
                {
                    id = 9999,
                    username = "evolution_test_user",
                    nickname = "进化测试用户",
                    gold = 100000
                };

                await _dbContext.Db.Insertable(testUser).ExecuteCommandAsync();
                _logger.LogInformation("创建测试用户成功");

                // 创建测试宠物配置
                var testPetConfig = new pet_config
                {
                    pet_no = 999001,
                    name = "测试宠物",
                    attribute = "火"
                };

                await _dbContext.Db.Insertable(testPetConfig).ExecuteCommandAsync();
                _logger.LogInformation("创建测试宠物配置成功");

                // 创建测试用户宠物
                var testUserPet = new user_pet
                {
                    id = 99999,
                    user_id = 9999,
                    pet_no = 999001,
                    custom_name = "测试宠物",
                    level = 50,
                    exp = 100000,
                    growth = 10.5m,
                    evolve_count = 0,
                    state = 1
                };

                await _dbContext.Db.Insertable(testUserPet).ExecuteCommandAsync();
                _logger.LogInformation("创建测试用户宠物成功");

                // 创建测试道具
                var testItems = new List<item_config>
                {
                    new item_config { item_no = 2016110512, name = "强化丹A", type = "进化道具", description = "五系A路线进化道具" },
                    new item_config { item_no = 2016110513, name = "强化丹B", type = "进化道具", description = "五系B路线进化道具" }
                };

                foreach (var item in testItems)
                {
                    var existing = await _dbContext.Db.Queryable<item_config>()
                        .FirstAsync(x => x.item_no == item.item_no);
                    if (existing == null)
                    {
                        await _dbContext.Db.Insertable(item).ExecuteCommandAsync();
                    }
                }

                // 给用户添加道具
                var userItems = new List<user_item>
                {
                    new user_item { user_id = 9999, item_id = "2016110512", item_count = 10 },
                    new user_item { user_id = 9999, item_id = "2016110513", item_count = 10 }
                };

                foreach (var userItem in userItems)
                {
                    await _dbContext.Db.Insertable(userItem).ExecuteCommandAsync();
                }

                _logger.LogInformation("测试数据初始化完成");

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "测试数据初始化成功",
                    Data = new
                    {
                        TestUserId = 9999,
                        TestUserPetId = 99999,
                        TestPetNo = 999001
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化测试数据失败");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"初始化失败：{ex.Message}"
                });
            }
        }

        /// <summary>
        /// 测试进化功能
        /// </summary>
        /// <param name="evolutionType">进化类型</param>
        /// <returns>测试结果</returns>
        [HttpPost("test-evolution/{evolutionType}")]
        public async Task<IActionResult> TestEvolution(string evolutionType)
        {
            try
            {
                if (evolutionType != "A" && evolutionType != "B")
                {
                    return BadRequest(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "进化类型只能是A或B"
                    });
                }

                var request = new EvolutionRequestDto
                {
                    UserPetId = 99999,
                    EvolutionType = evolutionType
                };

                // 模拟用户ID为9999
                Request.Headers.Add("X-User-Id", "9999");

                var result = await _evolutionService.EvolvePetAsync(9999, request);

                return Ok(new ApiResponse<EvolutionResultDto>
                {
                    Success = result.Success,
                    Message = result.Message,
                    Data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试进化功能失败");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"测试失败：{ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取测试宠物进化信息
        /// </summary>
        /// <returns>进化信息</returns>
        [HttpGet("test-pet-info")]
        public async Task<IActionResult> GetTestPetInfo()
        {
            try
            {
                var info = await _evolutionService.GetPetEvolutionInfoAsync(9999, 99999);

                if (info == null)
                {
                    return NotFound(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "测试宠物不存在，请先初始化测试数据"
                    });
                }

                return Ok(new ApiResponse<PetEvolutionInfoDto>
                {
                    Success = true,
                    Message = "获取成功",
                    Data = info
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取测试宠物信息失败");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"获取失败：{ex.Message}"
                });
            }
        }

        /// <summary>
        /// 获取测试宠物进化历史
        /// </summary>
        /// <returns>进化历史</returns>
        [HttpGet("test-evolution-history")]
        public async Task<IActionResult> GetTestEvolutionHistory()
        {
            try
            {
                var history = await _evolutionService.GetEvolutionHistoryAsync(9999, 99999);

                return Ok(new ApiResponse<List<EvolutionHistoryDto>>
                {
                    Success = true,
                    Message = "获取成功",
                    Data = history
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取进化历史失败");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"获取失败：{ex.Message}"
                });
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        /// <returns>清理结果</returns>
        [HttpDelete("cleanup-test-data")]
        public async Task<IActionResult> CleanupTestData()
        {
            try
            {
                _logger.LogInformation("开始清理测试数据");

                // 删除测试数据
                await _dbContext.Db.Deleteable<pet_evolution_log>().Where(x => x.user_id == 9999).ExecuteCommandAsync();
                await _dbContext.Db.Deleteable<user_item>().Where(x => x.user_id == 9999).ExecuteCommandAsync();
                await _dbContext.Db.Deleteable<user_pet>().Where(x => x.id == 99999).ExecuteCommandAsync();
                await _dbContext.Db.Deleteable<pet_config>().Where(x => x.pet_no == 999001).ExecuteCommandAsync();
                await _dbContext.Db.Deleteable<user>().Where(x => x.id == 9999).ExecuteCommandAsync();

                _logger.LogInformation("测试数据清理完成");

                return Ok(new ApiResponse<object>
                {
                    Success = true,
                    Message = "测试数据清理成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理测试数据失败");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"清理失败：{ex.Message}"
                });
            }
        }

        /// <summary>
        /// 运行完整的进化系统测试
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpPost("run-full-test")]
        public async Task<IActionResult> RunFullTest()
        {
            try
            {
                var testResults = new List<string>();

                // 1. 初始化测试数据
                testResults.Add("=== 开始宠物进化系统测试 ===");
                await CleanupTestData();
                
                // 重新初始化
                await InitTestData();
                testResults.Add("✅ 测试数据初始化成功");

                // 2. 获取进化信息
                var info = await _evolutionService.GetPetEvolutionInfoAsync(9999, 99999);
                if (info != null)
                {
                    testResults.Add($"✅ 获取宠物信息成功：{info.PetName}, 等级{info.Level}, 成长{info.Growth}, 进化次数{info.EvolveCount}");
                    testResults.Add($"✅ 可用进化路线：{info.AvailableEvolutions.Count}个");
                }

                // 3. 测试A路线进化
                var requestA = new EvolutionRequestDto { UserPetId = 99999, EvolutionType = "A" };
                var resultA = await _evolutionService.EvolvePetAsync(9999, requestA);
                if (resultA.Success)
                {
                    testResults.Add($"✅ A路线进化成功：成长增加{resultA.GrowthIncrease}, 新成长{resultA.NewGrowth}");
                }
                else
                {
                    testResults.Add($"❌ A路线进化失败：{resultA.Message}");
                }

                // 4. 获取进化历史
                var history = await _evolutionService.GetEvolutionHistoryAsync(9999, 99999);
                testResults.Add($"✅ 进化历史记录：{history.Count}条");

                // 5. 清理测试数据
                await CleanupTestData();
                testResults.Add("✅ 测试数据清理完成");
                testResults.Add("=== 宠物进化系统测试完成 ===");

                return Ok(new ApiResponse<List<string>>
                {
                    Success = true,
                    Message = "完整测试执行成功",
                    Data = testResults
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完整测试失败");
                return StatusCode(500, new ApiResponse<object>
                {
                    Success = false,
                    Message = $"测试失败：{ex.Message}"
                });
            }
        }
    }
}
