import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

import { Global } from '../Global';
import { HttpRequest } from '../tool/HttpRequest';

/**
 * 宠物选择功能测试脚本
 */
@ccclass('PetSelectionTest')
export class PetSelectionTest extends Component {

    start() {
        console.log("🧪 宠物选择功能测试开始");
        this.testPetSelection();
    }

    /**
     * 测试宠物选择功能
     */
    private async testPetSelection() {
        console.log("📋 测试步骤:");
        console.log("1. 当前Global.currentPetId =", Global.currentPetId);
        
        try {
            // 测试获取携带宠物接口
            console.log("2. 测试获取携带宠物接口...");
            
            const requestData = {
                userId: Global.userid,
                status: "携带"
            };
            
            const response = await HttpRequest.postConvertJson("Player/pets", requestData);
            console.log("✅ 接口响应:", response);
            
            if (response && response.success && response.pets) {
                console.log(`📊 获取到${response.pets.length}只携带宠物:`);
                response.pets.forEach((pet: any, index: number) => {
                    console.log(`   宠物${index + 1}: ${pet.name} (ID: ${pet.petNo}, 主战: ${pet.isMainPet})`);
                });
                
                console.log(`🌟 主战宠物ID: ${response.mainPetId}`);
                
                // 模拟选择第一只宠物
                if (response.pets.length > 0) {
                    const firstPet = response.pets[0];
                    console.log(`3. 模拟选择宠物: ${firstPet.name} (ID: ${firstPet.petNo})`);
                    Global.currentPetId = firstPet.petNo;
                    console.log(`✅ Global.currentPetId 已更新为: ${Global.currentPetId}`);
                }
                
            } else {
                console.error("❌ 接口调用失败或无宠物数据");
            }
            
        } catch (error) {
            console.error("💥 测试过程中发生错误:", error);
        }
        
        console.log("🧪 宠物选择功能测试完成");
    }
} 