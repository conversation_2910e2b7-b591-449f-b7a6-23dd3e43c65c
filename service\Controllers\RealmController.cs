using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Services;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Models;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 境界系统控制器
    /// 基于原项目WindowsFormsApplication7的境界系统完整迁移
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RealmController : ControllerBase
    {
        private readonly RealmService _realmService;
        private readonly ILogger<RealmController> _logger;

        public RealmController(RealmService realmService, ILogger<RealmController> logger)
        {
            _realmService = realmService;
            _logger = logger;
        }

        /// <summary>
        /// 获取宠物境界信息
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>境界信息</returns>
        [HttpPost("info")]
        public async Task<IActionResult> GetRealmInfo([FromBody] GetRealmInfoRequestDTO request)
        {
            try
            {
                var realmConfig = await _realmService.GetPetRealmAsync(request.PetId);
                if (realmConfig == null)
                {
                    return Ok(new RealmInfoResultDTO
                    {
                        RealmLevel = 1,
                        RealmName = "凡人",
                        AttributeBonusRate = 1.0,
                        CanUpgrade = false
                    });
                }

                // 获取下一境界信息
                var nextRealmLevel = realmConfig.realm_level + 1;
                var nextRealm = await _realmService.GetRealmListAsync();
                var nextRealmConfig = nextRealm.FirstOrDefault(x => x.realm_level == nextRealmLevel);

                var result = new RealmInfoResultDTO
                {
                    RealmLevel = realmConfig.realm_level,
                    RealmName = realmConfig.realm_name,
                    AttributeBonusRate = (double)realmConfig.attribute_bonus_rate,
                    UpgradeSuccessRate = (double)realmConfig.upgrade_success_rate,
                    UpgradeCostGold = realmConfig.upgrade_cost_gold,
                    UpgradeItemRequired = realmConfig.upgrade_item_required,
                    Description = realmConfig.description,
                    CanUpgrade = realmConfig.realm_name != "天地同寿" && realmConfig.realm_name != "神轮境",
                    NextRealm = nextRealmConfig != null ? new NextRealmInfo
                    {
                        RealmLevel = nextRealmConfig.realm_level,
                        RealmName = nextRealmConfig.realm_name,
                        AttributeBonusRate = (double)nextRealmConfig.attribute_bonus_rate
                    } : null
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取境界信息失败");
                return StatusCode(500, new { message = "获取境界信息失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 境界升级 (使用修炼丹)
        /// </summary>
        /// <param name="request">升级请求</param>
        /// <returns>升级结果</returns>
        [HttpPost("upgrade")]
        public async Task<IActionResult> UpgradeRealm([FromBody] RealmUpgradeRequestDTO request)
        {
            try
            {
                var result = await _realmService.UpgradeRealmAsync(request.UserId, request.PetId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "境界升级失败");
                return StatusCode(500, new { message = "境界升级失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 境界突破 (使用玄元丹)
        /// </summary>
        /// <param name="request">突破请求</param>
        /// <returns>突破结果</returns>
        [HttpPost("breakthrough")]
        public async Task<IActionResult> BreakthroughRealm([FromBody] RealmUpgradeRequestDTO request)
        {
            try
            {
                var result = await _realmService.BreakthroughRealmAsync(request.UserId, request.PetId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "境界突破失败");
                return StatusCode(500, new { message = "境界突破失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取境界列表
        /// </summary>
        /// <returns>境界配置列表</returns>
        [HttpGet("list")]
        public async Task<IActionResult> GetRealmList()
        {
            try
            {
                var realms = await _realmService.GetRealmListAsync();
                var result = new RealmListResultDTO
                {
                    Realms = realms.Select(r => new RealmConfigInfo
                    {
                        RealmLevel = r.realm_level,
                        RealmName = r.realm_name,
                        LevelRequirement = r.level_requirement,
                        AttributeBonusRate = (double)r.attribute_bonus_rate,
                        UpgradeSuccessRate = (double)r.upgrade_success_rate,
                        UpgradeCostGold = r.upgrade_cost_gold,
                        UpgradeItemRequired = r.upgrade_item_required,
                        Description = r.description
                    }).ToList()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取境界列表失败");
                return StatusCode(500, new { message = "获取境界列表失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 获取境界升级历史
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>升级历史</returns>
        [HttpPost("history")]
        public async Task<IActionResult> GetRealmHistory([FromBody] GetRealmHistoryRequestDTO request)
        {
            try
            {
                var history = await _realmService.GetUpgradeHistoryAsync(request.UserId, request.PetId);
                var realms = await _realmService.GetRealmListAsync();
                var realmDict = realms.ToDictionary(r => r.realm_level, r => r.realm_name);

                var totalCount = history.Count;
                var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
                var pagedHistory = history
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                var result = new RealmHistoryResultDTO
                {
                    TotalCount = totalCount,
                    CurrentPage = request.Page,
                    PageSize = request.PageSize,
                    TotalPages = totalPages,
                    Records = pagedHistory.Select(h => new RealmUpgradeRecord
                    {
                        Id = h.id,
                        PetId = h.pet_id,
                        FromRealmLevel = h.from_realm_level,
                        FromRealmName = realmDict.GetValueOrDefault(h.from_realm_level, "未知境界"),
                        ToRealmLevel = h.to_realm_level,
                        ToRealmName = realmDict.GetValueOrDefault(h.to_realm_level, "未知境界"),
                        UpgradeType = h.upgrade_type,
                        Success = h.success == 1,
                        CostGold = h.cost_gold,
                        CostItems = h.cost_items,
                        CreatedAt = h.created_at ?? DateTime.Now
                    }).ToList()
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取境界历史失败");
                return StatusCode(500, new { message = "获取境界历史失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 计算宠物境界属性加成
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>属性加成倍率</returns>
        [HttpGet("bonus/{petId}")]
        public async Task<IActionResult> GetRealmAttributeBonus(int petId)
        {
            try
            {
                var bonus = await _realmService.CalculateRealmAttributeBonusAsync(petId);
                return Ok(new { petId, attributeBonus = bonus });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算境界属性加成失败");
                return StatusCode(500, new { message = "计算境界属性加成失败", error = ex.Message });
            }
        }

        /// <summary>
        /// 批量获取多个宠物的境界信息
        /// </summary>
        /// <param name="petIds">宠物ID列表</param>
        /// <returns>境界信息列表</returns>
        [HttpPost("batch-info")]
        public async Task<IActionResult> GetBatchRealmInfo([FromBody] List<int> petIds)
        {
            try
            {
                var results = new List<object>();
                
                foreach (var petId in petIds)
                {
                    var realmConfig = await _realmService.GetPetRealmAsync(petId);
                    var bonus = await _realmService.CalculateRealmAttributeBonusAsync(petId);
                    
                    results.Add(new
                    {
                        petId,
                        realmLevel = realmConfig?.realm_level ?? 1,
                        realmName = realmConfig?.realm_name ?? "凡人",
                        attributeBonus = bonus
                    });
                }

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量获取境界信息失败");
                return StatusCode(500, new { message = "批量获取境界信息失败", error = ex.Message });
            }
        }
    }
}
