﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///宠物转生配置表
    ///</summary>
    [SugarTable("pet_nirvana_config")]
    public partial class pet_nirvana_config
    {
           public pet_nirvana_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:主宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int main_pet_no {get;set;}

           /// <summary>
           /// Desc:副宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sub_pet_no {get;set;}

           /// <summary>
           /// Desc:涅槃兽编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int nirvana_pet_no {get;set;}

           /// <summary>
           /// Desc:转生结果宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int result_pet_no {get;set;}

           /// <summary>
           /// Desc:所需等级
           /// Default:60
           /// Nullable:True
           /// </summary>           
           public int? required_level {get;set;}

           /// <summary>
           /// Desc:基础成功率(%)
           /// Default:30.00
           /// Nullable:True
           /// </summary>           
           public decimal? base_success_rate {get;set;}

           /// <summary>
           /// Desc:消耗金币
           /// Default:500000
           /// Nullable:True
           /// </summary>           
           public long? cost_gold {get;set;}

           /// <summary>
           /// Desc:主宠成长继承比例
           /// Default:0.2500
           /// Nullable:True
           /// </summary>           
           public decimal? main_growth_inherit {get;set;}

           /// <summary>
           /// Desc:副宠成长继承比例
           /// Default:0.0500
           /// Nullable:True
           /// </summary>           
           public decimal? sub_growth_inherit {get;set;}

           /// <summary>
           /// Desc:特殊规则(JSON格式)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? special_rule {get;set;}

           /// <summary>
           /// Desc:是否启用
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_active {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
