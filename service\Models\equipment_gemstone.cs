﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///装备宝石关联表
    ///</summary>
    [SugarTable("equipment_gemstone")]
    public partial class equipment_gemstone
    {
           public equipment_gemstone(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户装备ID（关联user_equipment表）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_equipment_id {get;set;}

           /// <summary>
           /// Desc:宝石类型名称（关联gemstone_config表）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string gemstone_type_name {get;set;}

           /// <summary>
           /// Desc:镶嵌位置（1-第一槽，2-第二槽，3-第三槽）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int position {get;set;}

           /// <summary>
           /// Desc:镶嵌时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
