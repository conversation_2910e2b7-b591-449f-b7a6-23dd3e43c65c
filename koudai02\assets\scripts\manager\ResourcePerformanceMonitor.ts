import { Component, Label, _decorator, director } from 'cc';
import { MassiveResourceManager } from './MassiveResourceManager';

const { ccclass, property } = _decorator;

/**
 * 资源性能监控组件
 * 实时显示资源加载和内存使用情况
 */
@ccclass('ResourcePerformanceMonitor')
export class ResourcePerformanceMonitor extends Component {
    @property(Label)
    statsLabel: Label = null!;

    @property({ type: <PERSON><PERSON>an, tooltip: '是否在发布版本中显示' })
    showInRelease: boolean = false;

    private updateInterval: number = 1.0; // 更新间隔（秒）
    private timer: number = 0;

    protected onLoad(): void {
        // 发布版本中默认隐藏
        if (!CC_DEBUG && !this.showInRelease) {
            this.node.active = false;
            return;
        }

        if (!this.statsLabel) {
            console.warn('⚠️ ResourcePerformanceMonitor: statsLabel未设置');
            return;
        }

        this.updateStats();
    }

    protected update(deltaTime: number): void {
        if (!this.node.active) return;

        this.timer += deltaTime;
        if (this.timer >= this.updateInterval) {
            this.timer = 0;
            this.updateStats();
        }
    }

    /**
     * 更新统计信息显示
     */
    private updateStats(): void {
        if (!this.statsLabel) return;

        try {
            const resourceManager = MassiveResourceManager.getInstance();
            const stats = resourceManager.getCacheStats();
            const memoryInfo = this.getMemoryInfo();

            const statsText = [
                '=== 资源监控 ===',
                `缓存资源: ${stats.size}/${stats.maxSize}`,
                `内存使用: ${stats.memoryMB.toFixed(1)}MB/${stats.maxMemoryMB}MB`,
                `使用率: ${((stats.memoryMB / stats.maxMemoryMB) * 100).toFixed(1)}%`,
                `正在加载: ${stats.loadingCount}`,
                `预加载中: ${stats.isPreloading ? '是' : '否'}`,
                '',
                '=== 系统信息 ===',
                `JS堆内存: ${memoryInfo.jsHeapSize.toFixed(1)}MB`,
                `总内存: ${memoryInfo.totalMemory.toFixed(1)}MB`,
                `FPS: ${director.root?.fps.toFixed(0) || 'N/A'}`,
                '',
                '=== 最近使用 ===',
                ...stats.recentlyUsed.slice(0, 3).map((item: string, index: number) => 
                    `${index + 1}. ${this.shortenPath(item)}`)
            ].join('\n');

            this.statsLabel.string = statsText;

            // 内存警告
            if (stats.memoryMB / stats.maxMemoryMB > 0.9) {
                this.setWarningStyle();
            } else {
                this.setNormalStyle();
            }

        } catch (error) {
            this.statsLabel.string = '监控数据获取失败';
            console.error('ResourcePerformanceMonitor 更新失败:', error);
        }
    }

    /**
     * 获取内存信息
     */
    private getMemoryInfo(): { jsHeapSize: number; totalMemory: number } {
        try {
            // @ts-ignore
            const performance = window.performance;
            if (performance && performance.memory) {
                return {
                    jsHeapSize: performance.memory.usedJSHeapSize / (1024 * 1024),
                    totalMemory: performance.memory.totalJSHeapSize / (1024 * 1024)
                };
            }
        } catch (error) {
            // 静默处理，某些平台可能不支持
        }

        return { jsHeapSize: 0, totalMemory: 0 };
    }

    /**
     * 缩短路径显示
     */
    private shortenPath(path: string): string {
        if (path.length <= 25) return path;
        
        const parts = path.split('/');
        if (parts.length > 2) {
            return `${parts[0]}/.../${parts[parts.length - 1]}`;
        }
        
        return path.substring(0, 22) + '...';
    }

    /**
     * 设置警告样式
     */
    private setWarningStyle(): void {
        if (this.statsLabel && this.statsLabel.node) {
            this.statsLabel.node.setScale(1.05);
            // 可以添加颜色变化等效果
        }
    }

    /**
     * 设置正常样式
     */
    private setNormalStyle(): void {
        if (this.statsLabel && this.statsLabel.node) {
            this.statsLabel.node.setScale(1.0);
        }
    }

    /**
     * 手动触发内存清理
     */
    public triggerMemoryCleanup(): void {
        const resourceManager = MassiveResourceManager.getInstance();
        resourceManager.checkMemoryPressure();
        
        console.log('🧹 手动触发内存检查和清理');
        this.updateStats(); // 立即更新显示
    }

    /**
     * 切换显示/隐藏
     */
    public toggleDisplay(): void {
        this.node.active = !this.node.active;
    }

    /**
     * 设置更新间隔
     */
    public setUpdateInterval(interval: number): void {
        this.updateInterval = Math.max(0.1, interval);
    }
}