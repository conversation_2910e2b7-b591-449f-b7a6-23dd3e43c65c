namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 领取战斗奖励结果DTO
    /// </summary>
    public class ClaimRewardResultDTO
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 返回消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 获得的经验值
        /// </summary>
        public int ExperienceGained { get; set; }

        /// <summary>
        /// 获得的金币
        /// </summary>
        public int GoldGained { get; set; }

        /// <summary>
        /// 宠物是否升级
        /// </summary>
        public bool PetLeveledUp { get; set; }

        /// <summary>
        /// 宠物新等级
        /// </summary>
        public int NewPetLevel { get; set; }

        /// <summary>
        /// 玩家是否升级
        /// </summary>
        public bool PlayerLeveledUp { get; set; }

        /// <summary>
        /// 玩家新等级
        /// </summary>
        public int NewPlayerLevel { get; set; }

        /// <summary>
        /// 获得的道具列表
        /// </summary>
        public List<RewardItemDTO> ItemsGained { get; set; } = new List<RewardItemDTO>();
    }

    /// <summary>
    /// 奖励道具DTO
    /// </summary>
    public class RewardItemDTO
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 获得数量
        /// </summary>
        public int Quantity { get; set; }
    }
} 