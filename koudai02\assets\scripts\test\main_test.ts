import { _decorator, Component, Node, find } from 'cc';
const { ccclass, property } = _decorator;
import { notice_test } from './notice_test';
@ccclass('main_test')
export class main_test extends Component {

    public notice_tst: notice_test;

    start() {
        const notice = find("/Notice", this.node);
        this.notice_tst=notice.getComponent(notice_test);

    }

    update(deltaTime: number) {

    }

    public onClick() {
         this.notice_tst.onClick();
    }
}


