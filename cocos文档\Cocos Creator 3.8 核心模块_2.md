# Cocos Creator 3.8 核心模块相关接口规则文档


### 51. Eventify
#### 说明
生成一个类，该类继承自指定的基类，并以和 [[23859]] 等同的方式实现了 [[IEventified]] 的所有接口。用于为类添加事件管理功能。
#### 使用方式
```typescript
import { Eventify } from 'cc';
class MyBaseClass { 
    constructor() { 
    } 
} 
const MyEventifiedClass = Eventify(MyBaseClass); 
const instance = new MyEventifiedClass(); 
instance.on('event', () => { 
    console.log('事件触发'); 
}); 
instance.emit('event'); 
```
### 52. executeInEditMode
#### 说明
允许继承自 `Component` 的 `CCClass` 在编辑器里执行。默认情况下，所有 `Component` 都只会在运行时才会执行，也就是说它们的生命周期回调不会在编辑器里触发。
#### 使用方式
```typescript
import { executeInEditMode } from 'cc';
@executeInEditMode 
class MyComponent { 
    start() { 
        console.log('组件在编辑器中启动'); 
    } 
} 
```
### 53. executionOrder
#### 说明
设置脚本生命周期方法调用的优先级。优先级小于 0 的组件将会优先执行，优先级大于 0 的组件将会延后执行。优先级仅会影响 `onLoad`, `onEnable`, `start`, `update` 和 `lateUpdate`，而 `onDisable` 和 `onDestroy` 不受影响。
#### 使用方式
```typescript
import { executionOrder } from 'cc';
@executionOrder(-1) 
class MyComponent { 
    start() { 
        console.log('组件优先执行'); 
    } 
} 
```
### 54. expoIn
#### 说明
启动慢，加速快。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较慢，然后逐渐加速。
#### 使用方式
```typescript
import { expoIn } from 'cc';
const t = 0.5; 
const value = expoIn(t); 
console.log(value); 
```
### 55. expoInOut
#### 说明
在开始时加速动画，在结束时减慢动画的速度。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时逐渐加速，结束时逐渐减速。
#### 使用方式
```typescript
import { expoInOut } from 'cc';
const t = 0.5; 
const value = expoInOut(t); 
console.log(value); 
```
### 56. expoOut
#### 说明
起动迅速，减速慢。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较快，然后逐渐减速。
#### 使用方式
```typescript
import { expoOut } from 'cc';
const t = 0.5; 
const value = expoOut(t); 
console.log(value); 
```
### 57. expoOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的速度变化与 `expoInOut` 相反。
### 58. extend
#### 说明
将一个类型继承另一个类型。
#### 使用方式
```typescript
import { extend } from 'cc';
class ParentClass { 
    constructor() { 
    } 
} 
class ChildClass extends extend(ParentClass) { 
    constructor() { 
        super(); 
    } 
} 
```
### 59. extname
#### 说明
返回 `Path` 的扩展名，包括 '.'，例如 '.png'。
#### 使用方式
```typescript
import { extname } from 'cc';
const path = '/path/to/file.txt'; 
const ext = extname(path); 
console.log(ext); // '.txt' 
```
### 60. fade
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时会有一个淡入淡出的效果。
### 61. fastRemove
#### 说明
移除首个指定的数组元素。判定元素相等时相当于于使用了 `Array.prototype.indexOf`。此函数十分高效，但会改变数组的元素次序。
#### 使用方式
```typescript
import { fastRemove } from 'cc';
const array = [1, 2, 3]; 
fastRemove(array, 2); 
console.log(array); // [1, 3] 
```
### 62. fastRemoveAt
#### 说明
移除指定索引的数组元素。此函数十分高效，但会改变数组的元素次序。
#### 使用方式
```typescript
import { fastRemoveAt } from 'cc';
const array = [1, 2, 3]; 
fastRemoveAt(array, 1); 
console.log(array); // [1, 3] 
```
### 63. fillItems
暂未获取到详细说明，推测与填充数组元素相关，用于将数组的元素填充为指定的值。
### 64. float
#### 说明
将该属性标记为浮点数。
#### 使用方式
```typescript
import { float } from 'cc';
class MyClass { 
    @float 
    public myProperty: number = 0.5; 
} 
```
### 65. formatStr
#### 说明
根据格式字符串构造一个字符串。
#### 使用方式
```typescript
import { formatStr } from 'cc';
const str = formatStr('Hello, {0}!', 'World'); 
console.log(str); // 'Hello, World!' 
```
### 66. get
#### 说明
添加或修改属性的 `get`, `enumerable` 或者 `configurable`。
#### 使用方式
```typescript
import { get } from 'cc';
class MyClass { 
    private _value: number = 0; 
    @get(() => this._value) 
    public get value() { 
        return this._value; 
    } 
} 
```
### 67. getClassById
#### 说明
通过 id 获取已注册的类型。
#### 使用方式
```typescript
import { getClassById } from 'cc';
const classId = 'myClassId'; 
const clazz = getClassById(classId); 
```
### 68. getClassByName
#### 说明
通过类名获取已注册的类型。
#### 使用方式
```typescript
import { getClassByName } from 'cc';
const className = 'MyClass'; 
const clazz = getClassByName(className); 
```
### 69. getClassId
#### 说明
获取对象的 class id。
#### 使用方式
```typescript
import { getClassId } from 'cc';
const obj = new MyClass(); 
const classId = getClassId(obj); 
```
### 70. getClassName
#### 说明
获取对象的类型名称，如果对象是 {} 字面量，将会返回 ""。
#### 使用方式
```typescript
import { getClassName } from 'cc';
const obj = new MyClass(); 
const className = getClassName(obj); 
```
### 71. getError
#### 说明
通过 error id 和必要的参数来获取错误信息。
#### 使用方式
```typescript
import { getError } from 'cc';
const errorId = 'myErrorId'; 
const errorMsg = getError(errorId, '参数'); 
```
### 72. getPropertyDescriptor
#### 说明
根据属性名从一个对象或者它的原型链中获取属性描述符。
#### 使用方式
```typescript
import { getPropertyDescriptor } from 'cc';
const obj = { a: 1 }; 
const descriptor = getPropertyDescriptor(obj, 'a'); 
console.log(descriptor); 
```
### 73. getSeperator
#### 说明
获取不同平台的文件分割符。类 unix 系统是 `/` ，windows 系统是 `\` 。
#### 使用方式
```typescript
import { getSeperator } from 'cc';
const separator = getSeperator(); 
console.log(separator); 
```
### 74. getSerializationMetadata
暂未获取到详细说明，推测与获取序列化的元数据相关。
### 75. getset
#### 说明
添加或修改属性的 `get`, `set`, `enumerable` 或者 `configurable`。
#### 使用方式
```typescript
import { getset } from 'cc';
class MyClass { 
    private _value: number = 0; 
    @getset(() => this._value, (value) => this._value = value) 
    public get value() { 
        return this._value; 
    } 
    public set value(value: number) { 
        this._value = value; 
    } 
} 
```
### 76. getSuper
#### 说明
获取父类。
#### 使用方式
```typescript
import { getSuper } from 'cc';
class ParentClass { 
    constructor() { 
    } 
} 
class ChildClass extends ParentClass { 
    constructor() { 
        super(); 
    } 
} 
const superClass = getSuper(ChildClass); 
console.log(superClass); // ParentClass 
```
### 77. help
#### 说明
指定当前组件的帮助文档的 url，设置过后，在 **属性检查器** 中就会出现一个帮助图标，用户点击将打开指定的网页。
#### 使用方式
```typescript
import { help } from 'cc';
@help('https://example.com/help') 
class MyComponent { 
    constructor() { 
    } 
} 
```
### 78. icon
#### 说明
自定义当前组件在编辑器中显示的图标 url。
#### 使用方式
```typescript
import { icon } from 'cc';
@icon('https://example.com/icon.png') 
class MyComponent { 
    constructor() { 
    } 
} 
```
### 79. inspector
#### 说明
自定义当前组件在 **属性检查器** 中渲染时所用的 UI 页面描述。
#### 使用方式
```typescript
import { inspector } from 'cc';
@inspector('my-inspector') 
class MyComponent { 
    constructor() { 
    } 
} 
```
### 80. integer
#### 说明
将该属性标记为整数。
#### 使用方式
```typescript
import { integer } from 'cc';
class MyClass { 
    @integer 
    public myProperty: number = 1; 
} 
```
### 81. isCCObject
#### 说明
判断一个对象是否是 `CCObject` 类型。
#### 使用方式
```typescript
import { isCCObject } from 'cc';
const obj = new CCObject(); 
const result = isCCObject(obj); 
console.log(result); // true 
```
### 82. isChildClassOf
#### 说明
判断一类型是否是另一类型的子类或本身。
#### 使用方式
```typescript
import { isChildClassOf } from 'cc';
class ParentClass { 
    constructor() { 
    } 
} 
class ChildClass extends ParentClass { 
    constructor() { 
        super(); 
    } 
} 
const result = isChildClassOf(ChildClass, ParentClass); 
console.log(result); // true 
```
### 83. isDisplayStats
#### 说明
是否显示 FPS 信息和部分调试信息。
#### 使用方式
```typescript
import { isDisplayStats } from 'cc';
const isDisplay = isDisplayStats(); 
console.log(isDisplay); 
```
### 84. isDomNode
#### 说明
如果是 DOM 节点，返回 true；否则返回 false。
#### 使用方式
```typescript
import { isDomNode } from 'cc';
const domNode = document.createElement('div'); 
const result = isDomNode(domNode); 
console.log(result); // true 
```
### 85. isEmptyObject
#### 说明
检查对象是否为空对象。空对象的定义是：没有任何可被枚举的属性（包括从原型链继承的属性）的对象。
#### 使用方式
```typescript
import { isEmptyObject } from 'cc';
const obj = {}; 
const result = isEmptyObject(obj); 
console.log(result); // true 
```
### 86. isNumber
#### 说明
检查对象是否是 `number` 类型。
#### 使用方式
```typescript
import { isNumber } from 'cc';
const num = 1; 
const result = isNumber(num); 
console.log(result); // true 
```
### 87. isPlainEmptyObj_DEV
#### 说明
检查一个对象是否为空对象。
#### 使用方式
```typescript
import { isPlainEmptyObj_DEV } from 'cc';
const obj = {}; 
const result = isPlainEmptyObj_DEV(obj); 
console.log(result); // true 
```
### 88. isString
#### 说明
检查对象是否是 `string` 类型。
#### 使用方式
```typescript
import { isString } from 'cc';
const str = 'hello'; 
const result = isString(str); 
console.log(result); // true 
```
### 89. isValid
暂未获取到详细说明，推测与判断对象是否有效相关，可能用于检查对象是否为空、是否已被销毁等。
### 90. join
#### 说明
拼接字符串为路径。
#### 使用方式
```typescript
import { join } from 'cc';
const path = join('/path', 'to', 'file.txt'); 
console.log(path); // '/path/to/file.txt' 
```
### 91. linear
#### 说明
线性函数，f(k) = k。返回值和输入值一一对应。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现线性的缓动动画效果。
#### 使用方式
```typescript
import { linear } from 'cc';
const t = 0.5; 
const value = linear(t); 
console.log(value); // 0.5 
```
### 92. log
#### 说明
向控制台输出一条日志信息。这条信息可能是单个字符串（包括可选的替代字符串），也可能是一个或多个对象。
#### 使用方式
```typescript
import { log } from 'cc';
log('这是一条日志信息'); 
```
### 93. logID
暂未获取到详细说明，推测与日志 ID 相关，用于输出带有 ID 的日志信息。
### 94. mainFileName
#### 说明
获取文件名的主名称。
#### 使用方式
```typescript
import { mainFileName } from 'cc';
const path = '/path/to/file.txt'; 
const name = mainFileName(path); 
console.log(name); // 'file' 
```
### 95. markAsWarning
暂未获取到详细说明，推测与标记为警告相关，可能用于标记某些属性或方法为已过时或不推荐使用。
### 96. menu
#### 说明
将当前组件添加到组件菜单中，方便用户查找。例如 "Rendering/CameraCtrl"。
#### 使用方式
```typescript
import { menu } from 'cc';
@menu('Rendering/CameraCtrl') 
class MyComponent { 
    constructor() { 
    } 
} 
```
### 97. mixin
#### 说明
将 "sources" 中的所有属性从 "sources" 复制到 "object"。
#### 使用方式
```typescript
import { mixin } from 'cc';
const object = { a: 1 }; 
const sources = { b: 2 }; 
mixin(object, sources); 
console.log(object); // { a: 1, b: 2 } 
```
### 98. murmurhash2_32_gc
#### 说明
`MurmurHash2` 的 JS 实现。原始实现是 http://github.com/garycourt/murmurhash-js 。用于生成哈希值，常用于数据的快速查找和比较。
#### 使用方式
```typescript
import { murmurhash2_32_gc } from 'cc';
const str = 'hello'; 
const hash = murmurhash2_32_gc(str); 
console.log(hash); 
```
### 99. obsolete
#### 说明
废弃一个属性。如果被废弃属性还在使用的话，会打印警告消息。警告消息包含新的属性名。
#### 使用方式
```typescript
import { obsolete } from 'cc';
class MyClass { 
    @obsolete('newProperty') 
    public oldProperty: number = 0; 
} 
```
### 100. obsoletes
#### 说明
废弃一组属性。如果被废弃属性还在使用的话，会打印警告消息。警告消息包含新的属性名。
#### 使用方式
```typescript
import { obsoletes } from 'cc';
class MyClass { 
    @obsoletes({ oldProperty1: 'newProperty1', oldProperty2: 'newProperty2' }) 
    public oldProperty1: number = 0; 
    public oldProperty2: number = 0; 
} 
```
### 101. playOnFocus
#### 说明
当指定了 [[23556]] 以后，`playOnFocus` 可以在选中当前组件所在的节点时，提高编辑器的场景刷新频率到 60 FPS，否则场景就只会在必要的时候进行重绘。
#### 使用方式
```typescript
import { playOnFocus } from 'cc';
@playOnFocus 
class MyComponent { 
    constructor() { 
    } 
} 
```
### 102. point_plane
#### 说明
计算点和平面之间的距离。
#### 使用方式
```typescript
import { point_plane, Plane, Vec3 } from 'cc';
const plane = new Plane(); 
plane.normal.set(0, 1, 0); 
plane.d = 0; 
const point = new Vec3(0, 1, 0); 
const distance = point_plane(point, plane); 
console.log(distance); // 1 
```
### 103. property
#### 说明
声明属性为 `CCClass` 属性。
#### 使用方式
```typescript
import { property } from 'cc';
class MyClass { 
    @property 
    public myProperty: number = 0; 
} 
```
### 104. pt_point_aabb
#### 说明
计算 AABB 上最接近给定点的点。
#### 使用方式
```typescript
import { pt_point_aabb, AABB, Vec3 } from 'cc';
const aabb = new AABB(); 
aabb.center.set(0, 0, 0); 
aabb.halfExtents.set(1, 1, 1); 
const point = new Vec3(2, 0, 0); 
const closestPoint = pt_point_aabb(point, aabb); 
console.log(closestPoint); 
```
### 105. pt_point_line
#### 说明
计算给定点距离线段 AB 上最近的一点。
#### 使用方式
```typescript
import { pt_point_line, Line, Vec3 } from 'cc';
const line = new Line(); 
line.start.set(0, 0, 0); 
line.end.set(1, 0, 0); 
const point = new Vec3(0.5, 1, 0); 
const closestPoint = pt_point_line(point, line); 
console.log(closestPoint); 
```
### 106. pt_point_obb
#### 说明
计算 OBB 上最接近给定点的点。
#### 使用方式
```typescript
import { pt_point_obb, OBB, Vec3 } from 'cc';
const obb = new OBB(); 
obb.center.set(0, 0, 0); 
obb.halfExtents.set(1, 1, 1); 
obb.rotation.set(0, 0, 0, 1); 
const point = new Vec3(2, 0, 0); 
const closestPoint = pt_point_obb(point, obb); 
console.log(closestPoint); 
```
### 107. pt_point_plane
#### 说明
计算平面上最接近给定点的点。
#### 使用方式
```typescript
import { pt_point_plane, Plane, Vec3 } from 'cc';
const plane = new Plane(); 
plane.normal.set(0, 1, 0); 
plane.d = 0; 
const point = new Vec3(0, 1, 0); 
const closestPoint = pt_point_plane(point, plane); 
console.log(closestPoint); 
```
### 108. pushToMap
#### 说明
往 map 插入一个元素。同一个关键字对应的所有值存储在一个数组里。
#### 使用方式
```typescript
import { pushToMap } from 'cc';
const map = {}; 
pushToMap(map, 'key', 'value'); 
console.log(map); // { key: ['value'] } 
```
### 109. quadIn
#### 说明
一个二次方的函数，f(k) = k * k。插值开始时很慢，然后逐渐加快，直到结束，并突然停止。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较慢，然后逐渐加速。
#### 使用方式
```typescript
import { quadIn } from 'cc';
const t = 0.5; 
const value = quadIn(t); 
console.log(value); 
```
### 110. quadInOut
#### 说明
插值开始时很慢，接着加快，然后在接近尾声时减慢。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时逐渐加速，结束时逐渐减速。
#### 使用方式
```typescript
import { quadInOut } from 'cc';
const t = 0.5; 
const value = quadInOut(t); 
console.log(value); 
```
### 111. quadOut
#### 说明
一个二次方的函数，f(k) = k * (2 - k)。插值开始时很突然，然后在接近尾声时逐渐减慢。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较快，然后逐渐减速。
#### 使用方式
```typescript
import { quadOut } from 'cc';
const t = 0.5; 
const value = quadOut(t); 
console.log(value); 
```
### 112. quadOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的速度变化与 `quadInOut` 相反。
### 113. quartIn
#### 说明
启动慢，加速快。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较慢，然后逐渐加速。
#### 使用方式
```typescript
import { quartIn } from 'cc';
const t = 0.5; 
const value = quartIn(t); 
console.log(value); 
```
### 114. quartInOut
#### 说明
在开始时加速动画，在结束时减慢动画的速度。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时逐渐加速，结束时逐渐减速。
#### 使用方式
```typescript
import { quartInOut } from 'cc';
const t = 0.5; 
const value = quartInOut(t); 
console.log(value); 
```
### 115. quartOut
#### 说明
起动迅速，减速慢。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较快，然后逐渐减速。
#### 使用方式
```typescript
import { quartOut } from 'cc';
const t = 0.5; 
const value = quartOut(t); 
console.log(value); 
```
### 116. quartOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的速度变化与 `quartInOut` 相反。
### 117. quintIn
#### 说明
启动慢，加速快。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较慢，然后逐渐加速。
#### 使用方式
```typescript
import { quintIn } from 'cc';
const t = 0.5; 
const value = quintIn(t); 
console.log(value); 
```
### 118. quintInOut
#### 说明
在开始时加速动画，在结束时减慢动画的速度。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时逐渐加速，结束时逐渐减速。
#### 使用方式
```typescript
import { quintInOut } from 'cc';
const t = 0.5; 
const value = quintInOut(t); 
console.log(value); 
```
### 119. quintOut
#### 说明
起动迅速，减速慢。具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始时速度较快，然后逐渐减速。
#### 使用方式
```typescript
import { quintOut } from 'cc';
const t = 0.5; 
const value = quintOut(t); 
console.log(value); 
```
### 120. quintOutIn
具体效果可以参考[该文档](https://docs.cocos.com/creator/manual/zh/tween/tween-function.html)。用于实现缓动动画的效果，动画开始和结束时的速度变化与 `quintInOut` 相反。
### 121. radiansToDegrees
#### 说明
将弧度转换为角度。
#### 使用方式
```typescript
import { radiansToDegrees } from 'cc';
const radians = Math.PI / 2; 
const degrees = radiansToDegrees(radians); 
console.log(degrees); // 90 
```
### 122. remove
#### 说明
移除首个指定的数组元素。判定元素相等时相当于于使用了 `Array.prototype.indexOf`。
#### 使用方式
```typescript
import { remove } from 'cc';
const array = [1, 2, 3]; 
remove(array, 2); 
console.log(array); // [1, 3] 
```
### 123. removeArray
#### 说明
移除多个数组元素。
#### 使用方式
```typescript
import { removeArray } from 'cc';
const array = [1, 2, 3, 4, 5]; 
const elementsToRemove = [2, 4]; 
removeArray(array, elementsToRemove); 
console.log(array); // [1, 3, 5] 
```
### 124. removeAt
#### 说明
移除指定索引的数组元素。
#### 使用方式
```typescript
import { removeAt } from 'cc';
const array = [1, 2, 3]; 
removeAt(array, 1); 
console.log(array); // [1, 3] 
```
### 125. removeIf
#### 说明
移除首个使谓词满足的数组元素。
#### 使用方式
```typescript
import { removeIf } from 'cc';
const array = [1, 2, 3]; 
removeIf(array, (element) => element === 2); 
console.log(array); // [1, 3] 
```
### 126. removeProperty
暂未获取到详细说明，推测与移除对象的属性相关。
### 127. replaceProperty
暂未获取到详细说明，推测与替换对象的属性相关。
### 128. requireComponent
#### 说明
为声明为 `CCClass` 的组件添加依赖的其它组件。当组件添加到节点上时，如果依赖的组件不存在，引擎将会自动将依赖组件添加到同一个节点，防止脚本出错。该设置在运行时同样有效。
#### 使用方式
```typescript
import { requireComponent } from 'cc';
@requireComponent(MyDependencyComponent) 
class MyComponent { 
    constructor() { 
    } 
} 
```
### 129. set
#### 说明
添加或修改属性的 `get`, `enumerable` 或者 `configurable`。
#### 使用方式
```typescript
import { set } from 'cc';
class MyClass { 
    private _value: number = 0; 
    @set(() => this._value) 
    public get value() { 
        return this._value; 
    } 
} 
```
### 130. setClassAlias
#### 说明
为类设置别名。执行 `setClassAlias(target, alias)` 后，`alias` 将作为类 `target` 的 “单向 ID” 和 “单向名称”。因此，`getClassById(alias)` 和 `getClassByName(alias)` 都会得到 `target` 。这种映射是单向的，这意味着 `getClassName(target)` 和 `getClassId(target)` 将不会返回 `alias`。
#### 使用方式
```typescript
import { setClassAlias } from 'cc';
class MyClass { 
    constructor() { 
    } 
} 
setClassAlias(MyClass, 'MyAlias'); 
const clazz = getClassById('MyAlias'); 
```
### 131. setClassName
#### 说明
通过指定的名称手动注册类型。
#### 使用方式
```typescript
import { setClassName } from 'cc';
class MyClass { 
    constructor() { 
    } 
} 
setClassName(MyClass, 'MyClass'); 
```
### 132. setDefaultLogTimes
暂未获取到详细说明，推测与设置默认日志输出次数相关。
### 133. setDisplayStats
#### 说明
设置是否在左下角显示 FPS 和部分调试。
#### 使用方式
```typescript
import { setDisplayStats } from 'cc';
setDisplayStats(true); 
```
### 134. setPropertyEnumType
#### 说明
设置属性的枚举类型。
#### 使用方式
```typescript
import { setPropertyEnumType } from 'cc';
class MyClass { 
    public myProperty: number = 0; 
} 
setPropertyEnumType(MyClass, 'myProperty', MyEnum); 
```
### 135. setPropertyEnumTypeOnAttrs
暂未获取到详细说明，推测与在属性上设置枚举类型相关。
### 136. shiftArguments
#### 说明
新的参数数组，该数组不包含第一个参数。
#### 使用方式
```

```