﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///宝石合成记录表
    ///</summary>
    [SugarTable("gemstone_synthesis_log")]
    public partial class gemstone_synthesis_log
    {
           public gemstone_synthesis_log(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:宝石类型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string gemstone_type {get;set;}

           /// <summary>
           /// Desc:合成数量
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int synthesis_count {get;set;}

           /// <summary>
           /// Desc:合成时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? synthesis_time {get;set;}

    }
}
