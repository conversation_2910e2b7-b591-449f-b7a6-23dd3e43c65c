
import { _decorator, Component, Node, Sprite, Color, find, Label } from 'cc';
const { ccclass, property } = _decorator;

//背包Item项脚本
@ccclass('ScrollViewItem')
export class ScrollViewItem extends Component {

    public img: Sprite = null;//图标

    public articlename: Label;//名称

    public type: Label;//类型

    public number: Label;//数量

    public id: Label;//道具序号

    private articlenameNode: Node = null;//名称节点

    private typeNode: Node = null;//类型节点

    private numberNode: Node = null;//数量节点

    private idNode: Node = null;//道具序号节点

    private itemData: any = null;//存储完整的道具数据

    start() {

    }

    update(deltaTime: number) {

    }

    /**
     * 设置道具/装备数据 - 适配后端RESTful API数据格式
     */
    setData(data: any) {
        console.log("开始设置物品数据:", data);

        // 初始化UI节点
        this.initUINodes();

        // 检查UI组件是否正确获取
        if (!this.articlename) {
            console.error("articlename Label组件未找到！");
            return;
        }
        if (!this.type) {
            console.error("type Label组件未找到！");
            return;
        }
        if (!this.number) {
            console.error("number Label组件未找到！");
            return;
        }
        if (!this.id) {
            console.error("id Label组件未找到！");
            return;
        }

        // 设置物品数据 - 兼容道具和装备数据格式
        // 装备名称优先级：equipName > name > itemName > displayName
        const itemName = data.equipName || data.name || data.itemName || data.displayName || data['道具名字'] || "未知物品";
        // 装备类型优先级：typeName > type
        const itemType = data.typeName || data.type || "道具";
        const itemCount = (data.count || data.itemCount || data['道具数量'] || 1).toString();
        const itemId = (data.id || data.itemSeq || data['道具序号'] || 0).toString();

        console.log("准备设置的数据:", {
            itemName,
            itemType,
            itemCount,
            itemId
        });

        // 设置UI显示
        this.articlename.string = itemName;
        this.type.string = itemType;
        this.number.string = itemCount;
        this.id.string = itemId;

        // 存储完整的物品数据供其他方法使用
        this.itemData = data;

        console.log("物品数据设置完成:", {
            显示名称: this.articlename.string,
            显示类型: this.type.string,
            显示数量: this.number.string,
            显示ID: this.id.string,
            原始数据: data
        });
    }

    /**
     * 初始化UI节点
     */
    private initUINodes() {
        console.log("开始初始化UI节点，当前节点:", this.node.name);

        if (!this.articlenameNode) {
            this.articlenameNode = find("articlename", this.node);
            if (!this.articlenameNode) {
                console.error("背包项找不到名称节点！节点结构:", this.node.children.map(child => child.name));
                return;
            }
            this.articlename = this.articlenameNode.getComponent(Label);
            if (!this.articlename) {
                console.error("名称节点没有Label组件！");
                return;
            }
            console.log("名称节点初始化成功");
        }

        if (!this.typeNode) {
            this.typeNode = find("type", this.node);
            if (!this.typeNode) {
                console.error("背包项找不到类型节点！");
                return;
            }
            this.type = this.typeNode.getComponent(Label);
            if (!this.type) {
                console.error("类型节点没有Label组件！");
                return;
            }
            console.log("类型节点初始化成功");
        }

        if (!this.numberNode) {
            this.numberNode = find("number", this.node);
            if (!this.numberNode) {
                console.error("背包项找不到数量节点！");
                return;
            }
            this.number = this.numberNode.getComponent(Label);
            if (!this.number) {
                console.error("数量节点没有Label组件！");
                return;
            }
            console.log("数量节点初始化成功");
        }

        if (!this.idNode) {
            this.idNode = find("id", this.node);
            if (!this.idNode) {
                console.error("背包项找不到道具序号节点！");
                return;
            }
            this.id = this.idNode.getComponent(Label);
            if (!this.id) {
                console.error("ID节点没有Label组件！");
                return;
            }
            console.log("ID节点初始化成功");
        }

        console.log("所有UI节点初始化完成");
    }

    //点击后改变
    changeColor() {
        this.node.getComponent(Sprite).color = new Color().fromHEX('#EBC26A');
    }


    //还原
    reductionItemColor() {
        this.node.getComponent(Sprite).color = new Color().fromHEX('#FEF7E5');
    }

    //设置道具数量
    setNumber():number {
        let num: number = parseInt(this.number.string) - 1;
        if(num>0){
            this.number.string = num.toString();
            return num;
        }else{
            this.node.active=false;
            return num;
        }
  
    }

  
}
