using Microsoft.Extensions.Logging;
using SqlSugar;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Interface;
using WebApplication_HM.Services;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 装备获得服务
    /// 基于老项目DataProcess.cs中的装备获得脚本逻辑完整迁移
    /// </summary>
    public class EquipmentObtainService
    {
        private readonly ILogger<EquipmentObtainService> _logger;
        private readonly DbContext _dbContext;
        private readonly IEquipmentRepository _equipmentRepository;

        // 五行属性列表 (基于老项目)
        private static readonly string[] ELEMENT_LIST = { "金", "木", "水", "火", "土", "雷", "风" };

        public EquipmentObtainService(
            ILogger<EquipmentObtainService> logger,
            DbContext dbContext,
            IEquipmentRepository equipmentRepository)
        {
            _logger = logger;
            _dbContext = dbContext;
            _equipmentRepository = equipmentRepository;
        }

        /// <summary>
        /// 处理装备获得脚本
        /// 基于老项目"一定概率获得道具或装备"脚本逻辑
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="scriptContent">脚本内容</param>
        /// <returns></returns>
        public async Task<ApiResult<EquipmentObtainResult>> ProcessEquipmentObtainScriptAsync(int userId, string scriptContent)
        {
            try
            {
                // 解析脚本内容
                var scriptParts = scriptContent.Split('|');
                if (scriptParts.Length < 3)
                    return ApiResult<EquipmentObtainResult>.CreateError("脚本格式错误");

                var scriptType = scriptParts[0];
                if (scriptType != "一定概率获得道具或装备")
                    return ApiResult<EquipmentObtainResult>.CreateError("不支持的脚本类型");

                // 获取概率分母 (最后一个参数)
                if (!int.TryParse(scriptParts[scriptParts.Length - 1], out int probability))
                    return ApiResult<EquipmentObtainResult>.CreateError("概率参数错误");

                // 随机判断是否获得
                var random = new Random();
                int lucky = random.Next(0, probability);
                
                if (lucky != 0)
                {
                    // 没有获得任何物品
                    return ApiResult<EquipmentObtainResult>.CreateSuccess(new EquipmentObtainResult
                    {
                        Success = false,
                        Message = "很遗憾,您什么都没获得!",
                        ObtainedEquipment = null
                    }, "很遗憾,您什么都没获得!");
                }

                // 随机选择一个物品 (排除第一个和最后一个参数)
                var itemList = scriptParts.Skip(1).Take(scriptParts.Length - 2).ToArray();
                if (itemList.Length == 0)
                    return ApiResult<EquipmentObtainResult>.CreateError("没有可获得的物品");

                string selectedItem = itemList[random.Next(0, itemList.Length)];

                // 检查是否为装备 (以'z'开头表示装备)
                if (selectedItem.StartsWith("z"))
                {
                    // 获得装备
                    string equipId = selectedItem.Substring(1); // 移除'z'前缀
                    var equipmentResult = await CreateEquipmentAsync(userId, equipId);
                    return equipmentResult;
                }
                else
                {
                    // 获得道具 (这里应该调用道具服务，暂时返回成功)
                    return ApiResult<EquipmentObtainResult>.CreateSuccess(new EquipmentObtainResult
                    {
                        Success = true,
                        Message = $"获得道具 {selectedItem}",
                        ObtainedEquipment = null
                    }, $"获得道具 {selectedItem}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理装备获得脚本失败，用户ID: {UserId}", userId);
                return ApiResult<EquipmentObtainResult>.CreateError("处理装备获得脚本失败");
            }
        }

        /// <summary>
        /// 创建装备
        /// 基于老项目AddPlayerEquipment方法
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="equipId">装备类型ID</param>
        /// <returns></returns>
        public async Task<ApiResult<EquipmentObtainResult>> CreateEquipmentAsync(int userId, string equipId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备详细信息
                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipId);
                    if (equipDetail == null)
                        return ApiResult<EquipmentObtainResult>.CreateError($"装备配置不存在: {equipId}");

                    // 2. 获取装备主表信息（获取装备名称）
                    var equipMain = await _dbContext.Db.Queryable<equipment>()
                        .Where(x => x.equip_id == equipId)
                        .FirstAsync();
                    if (equipMain == null)
                        return ApiResult<EquipmentObtainResult>.CreateError($"装备主表配置不存在: {equipId}");

                    // 3. 创建用户装备
                    var userEquipment = new user_equipment
                    {
                        user_id = userId,
                        equip_id = equipId,
                        name = equipMain.name, // 从主表获取装备名称
                        equip_type_id = equipDetail.equip_type_id,
                        strengthen_level = 0, // 初始强化等级为0
                        slot = 1, // 默认1个宝石槽位
                        position = null, // 未装备
                        is_equipped = false, // 未装备状态
                        create_time = DateTime.Now,
                        update_time = DateTime.Now,
                        element = GenerateRandomElement(), // 随机五行属性
                        pet_id = null, // 未装备到宠物
                        gemstone_slots = 1, // 默认1个宝石槽位
                        suit_id = DetermineSuitId(equipId), // 判断套装ID
                        lssx = null, // 历史属性
                        special_effect = null // 特殊效果
                    };

                    // 3. 插入数据库
                    var insertedId = await _dbContext.Db.Insertable(userEquipment).ExecuteReturnIdentityAsync();
                    userEquipment.id = insertedId;

                    // 4. 记录操作日志
                    await LogEquipmentObtainAsync(userId, insertedId, equipId, "SCRIPT_OBTAIN");

                    // 5. 构建返回结果
                    var obtainResult = new EquipmentObtainResult
                    {
                        Success = true,
                        Message = $"获得装备 {equipMain.name}",
                        ObtainedEquipment = new ObtainedEquipmentInfo
                        {
                            UserEquipmentId = insertedId,
                            EquipId = equipId,
                            Name = equipMain.name,
                            Element = userEquipment.element,
                            StrengthenLevel = 0,
                            SuitId = userEquipment.suit_id
                        }
                    };

                    return ApiResult<EquipmentObtainResult>.CreateSuccess(obtainResult, $"获得装备 {equipMain.name}");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建装备失败，用户ID: {UserId}, 装备ID: {EquipId}", userId, equipId);
                return ApiResult<EquipmentObtainResult>.CreateError("创建装备失败");
            }
        }

        /// <summary>
        /// 直接获得指定装备
        /// 用于管理员命令或特殊奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="equipId">装备类型ID</param>
        /// <param name="element">指定五行属性（可选）</param>
        /// <param name="strengthenLevel">指定强化等级（可选）</param>
        /// <returns></returns>
        public async Task<ApiResult<EquipmentObtainResult>> GrantEquipmentAsync(int userId, string equipId, 
            string? element = null, int strengthenLevel = 0)
        {
            try
            {
                // 验证强化等级
                if (strengthenLevel < 0 || strengthenLevel > 20)
                    return ApiResult<EquipmentObtainResult>.CreateError("强化等级必须在0-20之间");

                // 验证五行属性
                if (!string.IsNullOrEmpty(element) && !ELEMENT_LIST.Contains(element))
                    return ApiResult<EquipmentObtainResult>.CreateError("无效的五行属性");

                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备详细信息
                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipId);
                    if (equipDetail == null)
                        return ApiResult<EquipmentObtainResult>.CreateError($"装备配置不存在: {equipId}");

                    // 2. 获取装备主表信息（获取装备名称）
                    var equipMain = await _dbContext.Db.Queryable<equipment>()
                        .Where(x => x.equip_id == equipId)
                        .FirstAsync();
                    if (equipMain == null)
                        return ApiResult<EquipmentObtainResult>.CreateError($"装备主表配置不存在: {equipId}");

                    // 3. 创建用户装备
                    var userEquipment = new user_equipment
                    {
                        user_id = userId,
                        equip_id = equipId,
                        name = equipMain.name, // 从主表获取装备名称
                        equip_type_id = equipDetail.equip_type_id,
                        strengthen_level = strengthenLevel,
                        slot = 1,
                        position = null,
                        is_equipped = false,
                        create_time = DateTime.Now,
                        update_time = DateTime.Now,
                        element = element ?? GenerateRandomElement(),
                        pet_id = null,
                        gemstone_slots = 1,
                        suit_id = DetermineSuitId(equipId),
                        lssx = null,
                        special_effect = null
                    };

                    // 3. 插入数据库
                    var insertedId = await _dbContext.Db.Insertable(userEquipment).ExecuteReturnIdentityAsync();
                    userEquipment.id = insertedId;

                    // 4. 记录操作日志
                    await LogEquipmentObtainAsync(userId, insertedId, equipId, "ADMIN_GRANT");

                    // 5. 构建返回结果
                    var obtainResult = new EquipmentObtainResult
                    {
                        Success = true,
                        Message = $"获得装备 {equipMain.name} +{strengthenLevel}",
                        ObtainedEquipment = new ObtainedEquipmentInfo
                        {
                            UserEquipmentId = insertedId,
                            EquipId = equipId,
                            Name = equipMain.name,
                            Element = userEquipment.element,
                            StrengthenLevel = strengthenLevel,
                            SuitId = userEquipment.suit_id
                        }
                    };

                    return ApiResult<EquipmentObtainResult>.CreateSuccess(obtainResult,
                        $"获得装备 {equipMain.name} +{strengthenLevel}");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放装备失败，用户ID: {UserId}, 装备ID: {EquipId}", userId, equipId);
                return ApiResult<EquipmentObtainResult>.CreateError("发放装备失败");
            }
        }

        /// <summary>
        /// 生成随机五行属性
        /// 基于老项目的五行生成逻辑
        /// </summary>
        private string GenerateRandomElement()
        {
            var random = new Random();
            return ELEMENT_LIST[random.Next(0, ELEMENT_LIST.Length)];
        }

        /// <summary>
        /// 根据装备ID判断套装ID
        /// 基于老项目的套装判断逻辑
        /// </summary>
        private string? DetermineSuitId(string equipId)
        {
            // 基于装备ID前缀判断套装
            if (equipId.StartsWith("2017070101")) return "2017070101"; // 天魔套装
            if (equipId.StartsWith("2017063001")) return "2017063001"; // 自然套装
            if (equipId.StartsWith("20170612")) return "20170612";     // 黑白套装
            if (equipId.StartsWith("2016123001")) return "2016123001"; // 盛世套装
            if (equipId.StartsWith("2017051801")) return "2017051801"; // 龙鳞套装
            if (equipId.StartsWith("2017042901")) return "2017042901"; // 凤羽套装

            return null; // 非套装装备
        }

        /// <summary>
        /// 记录装备获得日志
        /// </summary>
        private async Task LogEquipmentObtainAsync(int userId, int userEquipmentId, string equipId, string obtainType)
        {
            try
            {
                var log = new equipment_operation_log
                {
                    user_id = userId,
                    user_equipment_id = userEquipmentId,
                    operation_type = obtainType,
                    operation_data = $"获得装备: {equipId}",
                    result = "SUCCESS",
                    result_message = "装备获得成功",
                    cost_money = 0,
                    create_time = DateTime.Now
                };

                await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "记录装备获得日志失败");
            }
        }
    }

    /// <summary>
    /// 装备获得结果
    /// </summary>
    public class EquipmentObtainResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public ObtainedEquipmentInfo? ObtainedEquipment { get; set; }
    }

    /// <summary>
    /// 获得的装备信息
    /// </summary>
    public class ObtainedEquipmentInfo
    {
        public int UserEquipmentId { get; set; }
        public string EquipId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Element { get; set; }
        public int StrengthenLevel { get; set; }
        public string? SuitId { get; set; }
    }
}
