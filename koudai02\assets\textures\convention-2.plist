<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>MAP.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{789,312}</string>
                <key>spriteSourceSize</key>
                <string>{789,312}</string>
                <key>textureRect</key>
                <string>{{1,343},{789,312}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ProgressBarBk_s.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{403,229},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>content.jpg</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{803,369}</string>
                <key>spriteSourceSize</key>
                <string>{803,369}</string>
                <key>textureRect</key>
                <string>{{1,976},{803,369}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>golden.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{1,1},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>green.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{403,1},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>green_r.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{1,115},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>map6.jpg</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{787,317}</string>
                <key>spriteSourceSize</key>
                <string>{787,317}</string>
                <key>textureRect</key>
                <string>{{1,657},{787,317}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>orange.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{403,115},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>orange_r.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{1,229},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>convention-2.png</string>
            <key>size</key>
            <string>{805,1346}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:93c3259f429dca920d6eb347f1a9f120:ecb65d32632ee30fb2db9b7b5d4fae42:b3796d7b229a28a27416386515018810$</string>
            <key>textureFileName</key>
            <string>convention-2.png</string>
        </dict>
    </dict>
</plist>
