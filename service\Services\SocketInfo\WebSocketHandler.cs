using System.Net.WebSockets;
using System.Text;

namespace WebApplication_HM.Services.SocketInfo;

//基础的 WebSocket 消息处理
public class WebSocketHandler
{
    protected readonly WebSocketConnectionManager WebSocketConnectionManager;

    // 构造函数，初始化WebSocketConnectionManager
    public WebSocketHandler(WebSocketConnectionManager webSocketConnectionManager)
    {
        // 将传入的WebSocketConnectionManager赋值给成员变量WebSocketConnectionManager
        WebSocketConnectionManager = webSocketConnectionManager;
    }

    // 定义一个虚拟方法，用于处理WebSocket连接
    public virtual async Task OnConnected(WebSocket socket)
    {
        // 将连接的WebSocket添加到WebSocket连接管理器中
        WebSocketConnectionManager.AddSocket(socket);
    }

    // 定义一个虚拟的异步方法，用于处理WebSocket连接断开事件
    public virtual async Task OnDisconnected(string connectionId)
    {
        // 在另一个线程中执行断开连接的操作
        await Task.Run(() =>
        {
            // 根据连接ID获取WebSocket连接
            var connection = WebSocketConnectionManager.GetSocketById(connectionId);
            // 如果连接存在且状态为打开
            if (connection?.Socket.State == WebSocketState.Open)
            {
                // 关闭WebSocket连接
                connection.Socket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closed by the WebSocketHandler",
                    CancellationToken.None).Wait();
            }
            // 从WebSocket连接管理器中移除连接
            WebSocketConnectionManager.RemoveSocket(connectionId);
        });
    }

    // 异步发送消息
    public async Task SendMessageAsync(string connectionId, string message)
    {
        // 根据连接ID获取WebSocket连接
        var connection = WebSocketConnectionManager.GetSocketById(connectionId);
        // 如果连接存在且状态为打开
        if (connection?.Socket.State == WebSocketState.Open)
        {
            // 将消息转换为字节数组
            var buffer = Encoding.UTF8.GetBytes(message);
            // 将字节数组转换为ArraySegment
            var segment = new ArraySegment<byte>(buffer);
            // 异步发送消息
            await connection.Socket.SendAsync(segment, WebSocketMessageType.Text, true, CancellationToken.None);
        }
    }

    // 异步发送消息给所有连接
    public async Task SendMessageToAllAsync(string message)
    {
        // 遍历所有连接
        foreach (var connection in WebSocketConnectionManager.GetAll())
        {
            // 如果连接状态为打开
            if (connection.Value.Socket.State == WebSocketState.Open)
            {
                // 异步发送消息
                await SendMessageAsync(connection.Key, message);
            }
        }
    }

    // 异步接收WebSocket消息
    public async Task ReceiveAsync(System.Net.WebSockets.WebSocket socket, Action<WebSocketReceiveResult, string> handleMessage)
    {
        // 创建一个缓冲区，大小为4KB
        var buffer = new byte[1024 * 4];

        // 当WebSocket状态为Open时，循环接收消息
        while (socket.State == WebSocketState.Open)
        {
            // 异步接收消息
            var result = await socket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
            // 如果消息类型为文本
            if (result.MessageType == WebSocketMessageType.Text)
            {
                // 将消息转换为字符串
                var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                // 调用处理消息的方法
                handleMessage(result, message);
            }
            // 如果消息类型为关闭
            else if (result.MessageType == WebSocketMessageType.Close)
            {
                // 异步关闭WebSocket连接
                await socket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Connection closed by the client", CancellationToken.None);
                // 跳出循环
                break;
            }
        }
    }

} 