using System.ComponentModel.DataAnnotations;

namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 使用道具请求DTO
    /// </summary>
    public class UseItemRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        [Required(ErrorMessage = "道具ID不能为空")]
        public string ItemId { get; set; }

        /// <summary>
        /// 使用数量
        /// </summary>
        [Required(ErrorMessage = "使用数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "使用数量必须大于0")]
        public int UseCount { get; set; }
    }
} 