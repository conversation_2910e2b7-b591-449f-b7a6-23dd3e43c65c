import { director } from 'cc';
import { ResourceManager } from './ResourceManager';

/**
 * 场景管理器 - 处理场景切换和资源生命周期
 */
export class SceneManager {
    private static currentScene: string = '';

    /**
     * 加载场景并管理资源
     */
    public static async loadScene(sceneName: string): Promise<void> {
        console.log(`🔄 准备切换到场景: ${sceneName}`);
        
        try {
            // 预加载目标场景资源
            await this.preloadSceneAssets(sceneName);
            
            // 记录当前场景
            this.currentScene = director.getScene()?.name || '';
            
            // 切换场景
            director.loadScene(sceneName);
            
            console.log(`✅ 场景切换成功: ${this.currentScene} -> ${sceneName}`);
            
        } catch (error) {
            console.error(`❌ 场景切换失败: ${sceneName}`, error);
            throw error;
        }
    }

    /**
     * 预加载场景资源
     */
    private static async preloadSceneAssets(sceneName: string): Promise<void> {
        const resourceManager = ResourceManager.getInstance();
        
        switch (sceneName) {
            case 'Friom':
                console.log('📦 预加载主界面资源...');
                await resourceManager.preloadCriticalAssets();
                break;
                
            case 'Login':
                console.log('📦 预加载登录界面资源...');
                // 登录界面相对简单，不需要预加载太多
                break;
                
            default:
                console.log(`📦 预加载场景资源: ${sceneName}...`);
                break;
        }
    }

    /**
     * 获取当前场景名称
     */
    public static getCurrentScene(): string {
        return this.currentScene;
    }

    /**
     * 清理场景资源（可选）
     */
    public static cleanupScene(sceneName: string): void {
        const resourceManager = ResourceManager.getInstance();
        
        // 根据需要释放特定场景的资源
        switch (sceneName) {
            case 'Login':
                // 登录场景退出时可以释放一些临时资源
                console.log('🗑️ 清理登录场景资源');
                break;
                
            default:
                console.log(`🗑️ 清理场景资源: ${sceneName}`);
                break;
        }
    }
}