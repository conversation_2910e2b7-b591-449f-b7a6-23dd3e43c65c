/**
 * LRU (Least Recently Used) 资源缓存管理器
 * 专门用于处理大规模资源的智能缓存和释放
 */
export class LRUResourceCache {
    private cache: Map<string, any> = new Map();
    private accessTimes: Map<string, number> = new Map();
    private maxSize: number;
    private maxMemoryMB: number;
    private currentMemoryMB: number = 0;

    constructor(maxSize: number = 150, maxMemoryMB: number = 200) {
        this.maxSize = maxSize;
        this.maxMemoryMB = maxMemoryMB;
    }

    /**
     * 获取缓存资源
     */
    get(key: string): any {
        if (this.cache.has(key)) {
            // 更新访问时间和顺序
            const value = this.cache.get(key);
            this.accessTimes.set(key, Date.now());
            
            // 移到末尾（最新访问）
            this.cache.delete(key);
            this.cache.set(key, value);
            
            console.log(`📦 从LRU缓存获取: ${key}`);
            return value;
        }
        return null;
    }

    /**
     * 设置缓存资源
     */
    set(key: string, value: any, estimatedSizeMB: number = 1): void {
        // 如果已存在，先删除
        if (this.cache.has(key)) {
            this.cache.delete(key);
            this.accessTimes.delete(key);
        }

        // 检查内存限制
        while (this.currentMemoryMB + estimatedSizeMB > this.maxMemoryMB && this.cache.size > 0) {
            this.removeLeastRecentlyUsed();
        }

        // 检查数量限制
        while (this.cache.size >= this.maxSize && this.cache.size > 0) {
            this.removeLeastRecentlyUsed();
        }

        // 添加新项
        this.cache.set(key, value);
        this.accessTimes.set(key, Date.now());
        this.currentMemoryMB += estimatedSizeMB;

        console.log(`💾 LRU缓存新增: ${key} (预估${estimatedSizeMB}MB, 总计${this.currentMemoryMB.toFixed(1)}MB)`);
    }

    /**
     * 移除最近最少使用的资源
     */
    private removeLeastRecentlyUsed(): void {
        if (this.cache.size === 0) return;

        // 找到最久未访问的key
        let oldestKey = '';
        let oldestTime = Date.now();

        for (const [key, time] of this.accessTimes) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.remove(oldestKey);
        }
    }

    /**
     * 手动移除指定资源
     */
    remove(key: string): boolean {
        if (this.cache.has(key)) {
            // 释放资源
            const asset = this.cache.get(key);
            if (asset && asset.destroy) {
                asset.destroy();
            }

            this.cache.delete(key);
            this.accessTimes.delete(key);
            this.currentMemoryMB = Math.max(0, this.currentMemoryMB - 1); // 简化计算

            console.log(`🗑️ LRU缓存移除: ${key}`);
            return true;
        }
        return false;
    }

    /**
     * 检查是否存在
     */
    has(key: string): boolean {
        return this.cache.has(key);
    }

    /**
     * 清空所有缓存
     */
    clear(): void {
        // 释放所有资源
        for (const [key, asset] of this.cache) {
            if (asset && asset.destroy) {
                asset.destroy();
            }
        }

        this.cache.clear();
        this.accessTimes.clear();
        this.currentMemoryMB = 0;

        console.log('🗑️ LRU缓存已全部清空');
    }

    /**
     * 获取缓存统计信息
     */
    getStats(): {
        size: number;
        maxSize: number;
        memoryMB: number;
        maxMemoryMB: number;
        hitRate?: number;
    } {
        return {
            size: this.cache.size,
            maxSize: this.maxSize,
            memoryMB: this.currentMemoryMB,
            maxMemoryMB: this.maxMemoryMB
        };
    }

    /**
     * 强制清理内存
     */
    forceCleanup(targetMemoryMB: number): void {
        console.log(`🧹 强制清理内存，目标: ${targetMemoryMB}MB`);
        
        while (this.currentMemoryMB > targetMemoryMB && this.cache.size > 0) {
            this.removeLeastRecentlyUsed();
        }
    }

    /**
     * 获取最近访问的资源列表（调试用）
     */
    getRecentlyUsed(count: number = 10): string[] {
        const sorted = Array.from(this.accessTimes.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, count)
            .map(([key]) => key);
        
        return sorted;
    }
}