# Cocos Creator 3.8 输入系统模块相关接口规则文档

## 一、命名空间
### 1. Input
该命名空间可能包含与输入系统核心功能相关的类、方法和常量等。用于统一管理和处理各种输入事件，是输入系统的重要组成部分。
### 2. SystemEvent
此命名空间主要涉及系统事件相关的内容，可能定义了系统级别的输入事件类型、处理机制等，方便开发者对系统级输入进行监听和响应。

## 二、类
### 1. Acceleration
#### 说明
该类用于表示设备重力传感器传递的各个轴的数据。在开发需要利用重力感应的游戏或应用时，可以通过该类获取设备在不同轴上的加速度信息。
#### 使用方式
```typescript
import { Acceleration } from 'cc';
// 创建一个 Acceleration 实例
const acceleration = new Acceleration();
// 假设从重力传感器获取到了数据并赋值给 acceleration
acceleration.x = 0.1; 
acceleration.y = 0.2; 
acceleration.z = 0.3; 
console.log(`X 轴加速度: ${acceleration.x}, Y 轴加速度: ${acceleration.y}, Z 轴加速度: ${acceleration.z}`); 
```
### 2. Event
#### 说明
所有事件对象的基类，包含事件相关基本信息，如事件类型、事件触发的目标等。其他具体的事件类都继承自该类。
#### 使用方式
在自定义事件类时可以继承 `Event` 类，并添加额外的属性和方法。例如：
```typescript
import { Event } from 'cc';
class CustomEvent extends Event {
    constructor(type: string, bubbles?: boolean) {
        super(type, bubbles);
    }
}
```
### 3. EventAcceleration
#### 说明
加速计事件类，用于处理设备重力传感器触发的事件。当设备的重力状态发生变化时，会触发该事件。
#### 使用方式
```typescript
import { input, EventAcceleration } from 'cc';
// 监听加速计事件
input.on(EventAcceleration, (event: EventAcceleration) => {
    const acceleration = event.acceleration;
    console.log(`加速计事件: X 轴加速度 ${acceleration.x}, Y 轴加速度 ${acceleration.y}, Z 轴加速度 ${acceleration.z}`);
});
```
### 4. EventGamepad
#### 说明
手柄事件类，用于处理游戏手柄的输入事件，如按键按下、摇杆移动等。
#### 使用方式
```typescript
import { input, EventGamepad } from 'cc';
// 监听手柄事件
input.on(EventGamepad, (event: EventGamepad) => {
    console.log('手柄事件触发:', event);
});
```
### 5. EventHandheld
#### 说明
手持设备事件类，可能用于处理手持设备特有的输入事件，如设备的摇晃、倾斜等。
#### 使用方式
```typescript
import { input, EventHandheld } from 'cc';
// 监听手持设备事件
input.on(EventHandheld, (event: EventHandheld) => {
    console.log('手持设备事件触发:', event);
});
```
### 6. EventHandle
#### 说明
6DOF 手柄事件类，用于处理 6DOF（六自由度）手柄的输入事件，提供更精确的手柄操作反馈。
#### 使用方式
```typescript
import { input, EventHandle } from 'cc';
// 监听 6DOF 手柄事件
input.on(EventHandle, (event: EventHandle) => {
    console.log('6DOF 手柄事件触发:', event);
});
```
### 7. EventHMD
#### 说明
头戴显示器事件类，用于处理头戴显示器的输入事件，如头部的转动、位置变化等。
#### 使用方式
```typescript
import { input, EventHMD } from 'cc';
// 监听头戴显示器事件
input.on(EventHMD, (event: EventHMD) => {
    console.log('头戴显示器事件触发:', event);
});
```
### 8. EventKeyboard
#### 说明
键盘事件类，用于处理键盘的输入事件，如按键按下、释放等。
#### 使用方式
```typescript
import { input, EventKeyboard, KeyCode } from 'cc';
// 监听键盘按键按下事件
input.on(EventKeyboard.KEY_DOWN, (event: EventKeyboard) => {
    if (event.keyCode === KeyCode.KEY_A) {
        console.log('A 键被按下');
    }
});
```
### 9. EventMouse
#### 说明
鼠标事件类型类，包含了鼠标的各种事件类型，如鼠标按下、移动、抬起等。
#### 使用方式
```typescript
import { input, EventMouse } from 'cc';
// 监听鼠标按下事件
input.on(EventMouse.MOUSE_DOWN, (event: EventMouse) => {
    console.log('鼠标按下，位置:', event.getLocation());
});
```
### 10. EventTouch
#### 说明
触摸事件类，用于处理触摸屏幕的输入事件，如触摸开始、移动、结束等。
#### 使用方式
```typescript
import { input, EventTouch } from 'cc';
// 监听触摸开始事件
input.on(EventTouch.TOUCH_START, (event: EventTouch) => {
    const touch = event.getTouches()[0];
    console.log('触摸开始，位置:', touch.getLocation());
});
```
### 11. Input
#### 说明
该输入类管理所有的输入事件，包括触摸、鼠标、加速计、游戏手柄、6DOF 手柄、头戴显示器和键盘。可以通过 `input` 获取到 `Input` 的实例。
#### 使用方式
```typescript
import { input, EventKeyboard, KeyCode } from 'cc';
// 监听键盘按键释放事件
input.on(EventKeyboard.KEY_UP, (event: EventKeyboard) => {
    if (event.keyCode === KeyCode.KEY_B) {
        console.log('B 键被释放');
    }
});
```
### 12. SystemEvent
#### 说明
系统事件类，目前支持按键事件和重力感应事件。可以通过 `systemEvent` 获取到 `SystemEvent` 的实例。
#### 使用方式
```typescript
import { systemEvent, SystemEventType } from 'cc';
// 监听系统按键事件
systemEvent.on(SystemEventType.KEY_DOWN, (event) => {
    console.log('系统按键事件触发:', event);
});
```
### 13. Touch
#### 说明
封装了触点相关的信息，如触点的位置、触摸的状态等。在处理触摸事件时，可以通过该类获取触点的详细信息。
#### 使用方式
```typescript
import { input, EventTouch } from 'cc';
input.on(EventTouch.TOUCH_MOVE, (event: EventTouch) => {
    const touch = event.getTouches()[0];
    console.log('触摸移动，当前位置:', touch.getLocation());
});
```

## 三、枚举
### 1. KeyCode
#### 说明
按键事件的按键码枚举，定义了各种键盘按键对应的代码。在处理键盘事件时，可以通过该枚举来判断按下的是哪个按键。
#### 使用方式
```typescript
import { input, EventKeyboard, KeyCode } from 'cc';
input.on(EventKeyboard.KEY_DOWN, (event: EventKeyboard) => {
    if (event.keyCode === KeyCode.ARROW_UP) {
        console.log('上方向键被按下');
    }
});
```
### 2. SystemEventType
#### 说明
`SystemEvent` 支持的事件类型以及节点事件类型枚举，定义了系统事件的各种类型，方便开发者监听不同类型的系统事件。
#### 使用方式
```typescript
import { systemEvent, SystemEventType } from 'cc';
// 监听系统重力感应事件
systemEvent.on(SystemEventType.DEVICEMOTION, (event) => {
    console.log('系统重力感应事件触发:', event);
});
```

## 四、变量
### 1. input
输入类单例，该单例管理所有的输入事件，包括触摸、鼠标、加速计、游戏手柄、6DOF 手柄、头戴显示器和键盘。开发者可以通过该单例来监听和处理各种输入事件。
### 2. systemEvent
系统事件单例，方便全局使用。可以通过该单例监听系统级别的输入事件，如按键事件和重力感应事件。

综上所述，Cocos Creator 3.8 的输入系统模块提供了丰富的接口和工具，通过合理使用这些接口，可以实现玩家与游戏之间的各种交互。