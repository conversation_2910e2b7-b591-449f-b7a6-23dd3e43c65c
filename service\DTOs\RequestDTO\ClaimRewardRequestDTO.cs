namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 领取战斗奖励请求DTO
    /// </summary>
    public class ClaimRewardRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 地图ID
        /// </summary>
        public int MapId { get; set; }

        /// <summary>
        /// 获得的经验值
        /// </summary>
        public int Experience { get; set; }

        /// <summary>
        /// 获得的金币
        /// </summary>
        public int Gold { get; set; }

        /// <summary>
        /// 是否战斗胜利
        /// </summary>
        public bool IsWin { get; set; }
    }
} 