# Cocos2d-x Android 打包完整指南

## 目录
- [环境准备](#环境准备)
- [项目配置](#项目配置)
- [常见问题解决](#常见问题解决)
- [打包流程](#打包流程)
- [优化建议](#优化建议)

## 环境准备

### 1. 必需软件安装

#### Java Development Kit (JDK)
- **推荐版本**: JDK 17 (Android Gradle Plugin 8.x 要求)
- **下载地址**: [Microsoft OpenJDK](https://learn.microsoft.com/en-us/java/openjdk/download)
- **安装步骤**:
  ```powershell
  # 下载并安装 Microsoft JDK 17
  Invoke-WebRequest -Uri "https://aka.ms/download-jdk/microsoft-jdk-17.0.9-windows-x64.msi" -OutFile "$env:TEMP\microsoft-jdk-17.msi"
  Start-Process -FilePath "$env:TEMP\microsoft-jdk-17.msi" -Wait
  ```

#### Android SDK
- **安装方式**: 通过 Android Studio 或 Command Line Tools
- **必需组件**:
  - Android SDK Build-Tools 30.0.3+
  - Android SDK Platform-Tools
  - Android SDK Platform (API Level 30+)

#### Android NDK
- **推荐版本**: NDK r23c
- **配置路径**: 在 `gradle.properties` 中设置 `PROP_NDK_PATH`

### 2. 环境变量配置

```powershell
# 设置 JAVA_HOME (每次构建前执行)
$env:JAVA_HOME="C:\Program Files\Microsoft\jdk-********-hotspot"

# 验证 Java 版本
java -version
```

## 项目配置

### 1. 关键配置文件

#### `gradle.properties`
```properties
# Android SDK 版本配置
PROP_COMPILE_SDK_VERSION=34
PROP_MIN_SDK_VERSION=21
PROP_TARGET_SDK_VERSION=34
PROP_BUILD_TOOLS_VERSION=30.0.3

# NDK 路径
PROP_NDK_PATH=D:\\Android\\android-ndk-r23c

# 应用信息
PROP_APP_NAME=YourAppName
APPLICATION_ID=com.yourcompany.yourgame
PROP_APP_ABI=arm64-v8a

# 性能优化
android.useAndroidX=true
android.suppressUnsupportedCompileSdk=34
org.gradle.jvmargs=-Xmx4608m -Dfile.encoding=UTF-8

# 签名配置 (Debug)
RELEASE_STORE_FILE=debug.keystore
RELEASE_STORE_PASSWORD=123456
RELEASE_KEY_ALIAS=debug_keystore
RELEASE_KEY_PASSWORD=123456
```

#### `local.properties`
```properties
sdk.dir=D\:\\Android\\android-sdk
```

### 2. Gradle 版本兼容性

| Android Gradle Plugin | Gradle Version | Java Version |
|----------------------|----------------|--------------|
| 8.0.x                | 8.0+           | JDK 17       |
| 7.4.x                | 7.5+           | JDK 11       |
| 7.0.x                | 7.0+           | JDK 11       |

## 常见问题解决

### 1. SDK 许可证问题
```bash
# 错误信息
Failed to install the following Android SDK packages as some licences have not been accepted.
build-tools;30.0.3 Android SDK Build-Tools 30.0.3

# 解决方案 1: 手动创建许可证文件
echo "24333f8a63b6825ea9c5514f83c2829b004d1fee" | Out-File -FilePath "D:\Android\android-sdk\licenses\android-sdk-license" -Encoding ASCII

# 解决方案 2: 添加 gradle.properties 配置
android.suppressUnsupportedCompileSdk=34
```

### 2. Java 版本不兼容
```bash
# 错误信息
Android Gradle plugin requires Java 17 to run. You are currently using Java X.

# 解决方案: 设置正确的 JAVA_HOME
$env:JAVA_HOME="C:\Program Files\Microsoft\jdk-********-hotspot"
```

### 3. 构建卡在 extractDebugAnnotations
```bash
# 问题: 构建在 77% 处长时间停留
# 解决方案 1: 禁用调试注解提取
android.enableExtractAnnotations=false

# 解决方案 2: 使用 Release 构建
.\gradlew.bat assembleRelease

# 解决方案 3: 跳过该步骤
.\gradlew.bat assembleDebug -x extractDebugAnnotations
```

## 打包流程

### 1. 项目准备
```bash
# 1. 确保项目结构正确
YourProject/
├── build/
│   └── android/
│       └── proj/
│           ├── gradlew.bat
│           ├── build.gradle
│           ├── gradle.properties
│           └── local.properties

# 2. 检查 Cocos Creator 构建输出
# 确保已通过 Cocos Creator 构建 Android 平台
```

### 2. 执行构建命令
```powershell
# 进入项目目录
cd "YourProject\build\android\proj"

# 设置 Java 环境
$env:JAVA_HOME="C:\Program Files\Microsoft\jdk-********-hotspot"

# 构建 Debug APK
.\gradlew.bat assembleDebug

# 或构建 Release APK
.\gradlew.bat assembleRelease
```

### 3. 构建输出
```bash
# Debug APK 位置
build/koudai01/outputs/apk/debug/koudai01-debug.apk

# Release APK 位置
build/koudai01/outputs/apk/release/koudai01-release.apk
```

## 优化建议

### 1. 构建性能优化
```properties
# gradle.properties 中添加
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.jvmargs=-Xmx4608m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
```

### 2. APK 大小优化
```gradle
// build.gradle 中配置
android {
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    // 只构建需要的架构
    ndk {
        abiFilters 'arm64-v8a'  // 或 'armeabi-v7a', 'x86', 'x86_64'
    }
}
```

### 3. 签名配置 (Release)
```gradle
// 生产环境签名配置
android {
    signingConfigs {
        release {
            storeFile file('your-release-key.keystore')
            storePassword 'your-store-password'
            keyAlias 'your-key-alias'
            keyPassword 'your-key-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}
```

## 故障排除

### 1. 清理构建缓存
```bash
# 清理 Gradle 缓存
.\gradlew.bat clean

# 清理所有缓存
Remove-Item -Path "$env:USERPROFILE\.gradle\caches" -Recurse -Force
```

### 2. 检查构建日志
```bash
# 详细构建日志
.\gradlew.bat assembleDebug --info --stacktrace

# 调试模式
.\gradlew.bat assembleDebug --debug
```

### 3. 验证 APK
```bash
# 检查 APK 内容
# 使用 Android Studio 的 APK Analyzer 或
# 解压 APK 文件查看内容结构
```

## 快速检查清单

- [ ] Java 17 已安装并设置 JAVA_HOME
- [ ] Android SDK 已安装且许可证已接受
- [ ] NDK 路径在 gradle.properties 中正确配置
- [ ] 项目已通过 Cocos Creator 构建 Android 平台
- [ ] gradle.properties 配置正确
- [ ] local.properties 中 SDK 路径正确

## 常用命令参考

```bash
# 查看可用任务
.\gradlew.bat tasks

# 构建所有变体
.\gradlew.bat build

# 只构建 Debug
.\gradlew.bat assembleDebug

# 只构建 Release
.\gradlew.bat assembleRelease

# 安装到设备
.\gradlew.bat installDebug

# 清理构建
.\gradlew.bat clean
```

## 版本兼容性矩阵

### Cocos Creator 版本对应关系
| Cocos Creator | Android Gradle Plugin | Gradle | Java | Android API |
|---------------|----------------------|--------|------|-------------|
| 3.8.x         | 8.0.x                | 8.0+   | 17   | 30+         |
| 3.7.x         | 7.4.x                | 7.5+   | 11   | 30+         |
| 3.6.x         | 7.0.x                | 7.0+   | 11   | 29+         |

## 自动化脚本

### Windows PowerShell 一键构建脚本
```powershell
# build-android.ps1
param(
    [string]$ProjectPath = ".",
    [string]$BuildType = "Debug"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "开始构建 Cocos2d-x Android 项目..." -ForegroundColor Green

# 1. 检查 Java 环境
$javaHome = "C:\Program Files\Microsoft\jdk-********-hotspot"
if (Test-Path $javaHome) {
    $env:JAVA_HOME = $javaHome
    Write-Host "✓ Java 17 环境已设置" -ForegroundColor Green
} else {
    Write-Error "❌ Java 17 未找到，请先安装"
}

# 2. 进入构建目录
$buildPath = Join-Path $ProjectPath "build\android\proj"
if (Test-Path $buildPath) {
    Set-Location $buildPath
    Write-Host "✓ 进入构建目录: $buildPath" -ForegroundColor Green
} else {
    Write-Error "❌ 构建目录不存在，请先通过 Cocos Creator 构建项目"
}

# 3. 检查必要文件
$requiredFiles = @("gradlew.bat", "build.gradle", "gradle.properties")
foreach ($file in $requiredFiles) {
    if (!(Test-Path $file)) {
        Write-Error "❌ 缺少必要文件: $file"
    }
}
Write-Host "✓ 必要文件检查完成" -ForegroundColor Green

# 4. 执行构建
try {
    Write-Host "开始构建 $BuildType 版本..." -ForegroundColor Yellow
    if ($BuildType -eq "Release") {
        & .\gradlew.bat assembleRelease
    } else {
        & .\gradlew.bat assembleDebug
    }

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 构建成功完成！" -ForegroundColor Green

        # 查找生成的 APK
        $apkPath = "build\*\outputs\apk\*\*.apk"
        $apkFiles = Get-ChildItem $apkPath -Recurse
        if ($apkFiles) {
            Write-Host "生成的 APK 文件:" -ForegroundColor Cyan
            foreach ($apk in $apkFiles) {
                $size = [math]::Round($apk.Length / 1MB, 2)
                Write-Host "  📱 $($apk.Name) (${size} MB)" -ForegroundColor Cyan
                Write-Host "     路径: $($apk.FullName)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Error "❌ 构建失败，退出代码: $LASTEXITCODE"
    }
} catch {
    Write-Error "❌ 构建过程中发生错误: $($_.Exception.Message)"
}
```

### 使用方法
```powershell
# 构建 Debug 版本
.\build-android.ps1 -ProjectPath "D:\MyProject" -BuildType "Debug"

# 构建 Release 版本
.\build-android.ps1 -ProjectPath "D:\MyProject" -BuildType "Release"
```

## 高级配置

### 1. 多渠道打包
```gradle
// build.gradle
android {
    productFlavors {
        googleplay {
            applicationIdSuffix ".gp"
            versionNameSuffix "-gp"
        }
        huawei {
            applicationIdSuffix ".hw"
            versionNameSuffix "-hw"
        }
    }
}
```

### 2. 自定义构建配置
```gradle
// 自定义构建类型
android {
    buildTypes {
        staging {
            initWith debug
            applicationIdSuffix ".staging"
            debuggable true
            minifyEnabled true
        }
    }
}
```

### 3. 原生库优化
```gradle
// 分包配置
android {
    splits {
        abi {
            enable true
            reset()
            include 'arm64-v8a', 'armeabi-v7a'
            universalApk false
        }
    }
}
```

## 持续集成 (CI/CD)

### GitHub Actions 配置
```yaml
# .github/workflows/android.yml
name: Android Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'microsoft'

    - name: Setup Android SDK
      uses: android-actions/setup-android@v2

    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}

    - name: Grant execute permission for gradlew
      run: chmod +x build/android/proj/gradlew

    - name: Build Debug APK
      run: |
        cd build/android/proj
        ./gradlew assembleDebug

    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: debug-apk
        path: build/android/proj/build/**/outputs/apk/debug/*.apk
```

## 性能监控与分析

### 1. 构建时间分析
```bash
# 生成构建报告
.\gradlew.bat assembleDebug --profile

# 查看报告位置
# build/reports/profile/
```

### 2. APK 分析
```bash
# 使用 Android Studio APK Analyzer
# 或使用命令行工具
aapt dump badging your-app.apk
```

### 3. 内存使用监控
```properties
# gradle.properties
org.gradle.jvmargs=-Xmx8g -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError
```

---

**注意**: 本文档基于 Cocos Creator 3.8.3 和 Android Gradle Plugin 8.0.2 编写，不同版本可能需要调整配置。

**更新日期**: 2025年1月

**作者**: AI Assistant

**许可**: 本文档可自由使用和分发
