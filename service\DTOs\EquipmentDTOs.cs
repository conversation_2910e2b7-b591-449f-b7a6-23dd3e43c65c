using WebApplication_HM.DTOs.Common;

namespace WebApplication_HM.DTOs
{
    /// <summary>
    /// 用户装备DTO
    /// </summary>
    public class UserEquipmentDto
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string EquipId { get; set; }
        public string Name { get; set; }
        public string? EquipName { get; set; }
        public string? Icon { get; set; }
        public string EquipTypeId { get; set; }
        public string? TypeName { get; set; }
        public int StrengthenLevel { get; set; }
        public int Slot { get; set; }
        public string? Element { get; set; }
        public int? PetId { get; set; }
        public bool IsEquipped { get; set; }
        public int? Position { get; set; }
        public string? MainAttr { get; set; }
        public string? MainAttrValue { get; set; }
        public EquipmentDetailDto? Detail { get; set; }
        public List<GemstoneDto> Gemstones { get; set; } = new();
        public DateTime? CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
    }

    /// <summary>
    /// 装备详细属性DTO
    /// </summary>
    public class EquipmentDetailDto
    {
        public decimal Atk { get; set; }
        public decimal Hit { get; set; }
        public decimal Def { get; set; }
        public decimal Spd { get; set; }
        public decimal Dodge { get; set; }
        public decimal Hp { get; set; }
        public decimal Mp { get; set; }
        public decimal Deepen { get; set; }
        public decimal Offset { get; set; }
        public decimal Vamp { get; set; }
        public decimal VampMp { get; set; }
        public string? MainAttr { get; set; }
        public string? ElementLimit { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// 宝石DTO
    /// </summary>
    public class GemstoneDto
    {
        public int Id { get; set; }
        public string TypeName { get; set; }
        public string UpType { get; set; }
        public decimal UpNum { get; set; }
        public int Level { get; set; }
        public string? Color { get; set; }
        public string TypeClass { get; set; }
        public int Position { get; set; }
        public DateTime? CreateTime { get; set; }
    }

    /// <summary>
    /// 宝石配置DTO
    /// </summary>
    public class GemstoneConfigDto
    {
        public int Id { get; set; }
        public string TypeName { get; set; }
        public string UpType { get; set; }
        public decimal UpNum { get; set; }
        public int Level { get; set; }
        public string? Color { get; set; }
        public string? PropId { get; set; }
        public string TypeClass { get; set; }
        public List<string>? EquipTypes { get; set; }
        public List<int>? Positions { get; set; }
        public int OrderNum { get; set; }
    }

    /// <summary>
    /// 套装配置DTO
    /// </summary>
    public class SuitConfigDto
    {
        public string SuitId { get; set; }
        public string SuitName { get; set; }
        public List<string>? EquipmentList { get; set; }
        public string? Description { get; set; }
        public int TotalPieces { get; set; }
        public List<SuitAttributeDto> Attributes { get; set; } = new();
    }

    /// <summary>
    /// 套装属性DTO
    /// </summary>
    public class SuitAttributeDto
    {
        public int PieceCount { get; set; }
        public string AttributeType { get; set; }
        public string AttributeValue { get; set; }
        public bool IsPercentage { get; set; }
    }

    /// <summary>
    /// 套装激活状态DTO
    /// </summary>
    public class SuitActivationDto
    {
        public string SuitId { get; set; }
        public string SuitName { get; set; }
        public int ActivatedPieces { get; set; }
        public int TotalPieces { get; set; }
        public List<SuitAttributeDto> ActivatedAttributes { get; set; } = new();
        public List<SuitAttributeDto> AllAttributes { get; set; } = new();
    }

    /// <summary>
    /// 强化结果DTO
    /// </summary>
    public class StrengthenResult
    {
        public bool Success { get; set; }
        public int NewLevel { get; set; }
        public string Message { get; set; }
        public long CostMoney { get; set; }
        public Dictionary<string, int> CostItems { get; set; } = new();
    }

    /// <summary>
    /// 强化消耗DTO
    /// </summary>
    public class StrengthenCostDto
    {
        public int StoneCount { get; set; }
        public long MoneyCost { get; set; }
    }

    /// <summary>
    /// 分解结果DTO
    /// </summary>
    public class ResolveResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public Dictionary<string, int> RewardItems { get; set; } = new();
        public long RewardMoney { get; set; }
        public long CostMoney { get; set; } // 分解消耗的金币
    }

    /// <summary>
    /// 装备计算DTO
    /// </summary>
    public class EquipmentCalculationDto
    {
        public int UserEquipmentId { get; set; }
        public string EquipId { get; set; }
        public int StrengthenLevel { get; set; }
        public string? Element { get; set; }
        public EquipmentDetailDto Detail { get; set; }
    }

    /// <summary>
    /// 属性计算结果DTO
    /// </summary>
    public class AttributeCalculationResult
    {
        public Dictionary<string, double> BaseAttributes { get; set; } = new();
        public Dictionary<string, double> EquipmentAttributes { get; set; } = new();
        public Dictionary<string, double> SuitAttributes { get; set; } = new();
        public Dictionary<string, double> GemstoneAttributes { get; set; } = new();
        public Dictionary<string, double> TotalAttributes { get; set; } = new();
    }



    // 请求DTO
    public class EquipRequest
    {
        public int UserEquipmentId { get; set; }
        public int PetId { get; set; }
    }

    public class UnequipRequest
    {
        public int UserEquipmentId { get; set; }
    }

    public class StrengthenRequest
    {
        public int UserEquipmentId { get; set; }
        public bool UseProtection { get; set; } = false;
    }

    public class EmbedGemstoneRequest
    {
        public int UserEquipmentId { get; set; }
        public string GemstoneTypeId { get; set; }
        public int Position { get; set; }
    }

    public class RemoveGemstoneRequest
    {
        public int UserEquipmentId { get; set; }
        public int Position { get; set; }
    }

    public class TransformElementRequest
    {
        public int UserEquipmentId { get; set; }
    }

    public class ResolveRequest
    {
        public int UserEquipmentId { get; set; }
    }
}
