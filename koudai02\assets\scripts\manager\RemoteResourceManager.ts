import { assetManager } from 'cc';

/**
 * 远程资源管理器 - 处理远程图片、配置文件等资源的加载
 */
export class RemoteResourceManager {
    private static _instance: RemoteResourceManager;
    private _remoteCache: Map<string, any> = new Map();
    private _loadingPromises: Map<string, Promise<any>> = new Map();

    public static getInstance(): RemoteResourceManager {
        if (!RemoteResourceManager._instance) {
            RemoteResourceManager._instance = new RemoteResourceManager();
        }
        return RemoteResourceManager._instance;
    }

    /**
     * 加载远程图片
     */
    public async loadRemoteImage(url: string): Promise<any> {
        // 检查缓存
        if (this._remoteCache.has(url)) {
            console.log(`📦 从缓存加载远程图片: ${url}`);
            return this._remoteCache.get(url);
        }

        // 检查是否正在加载
        if (this._loadingPromises.has(url)) {
            console.log(`⏳ 等待远程图片加载完成: ${url}`);
            return this._loadingPromises.get(url);
        }

        // 开始加载
        const promise = new Promise((resolve, reject) => {
            assetManager.loadRemote(url, (err, texture) => {
                this._loadingPromises.delete(url);
                
                if (err) {
                    console.error(`❌ 远程图片加载失败: ${url}`, err);
                    reject(err);
                } else {
                    this._remoteCache.set(url, texture);
                    console.log(`✅ 远程图片加载成功: ${url}`);
                    resolve(texture);
                }
            });
        });

        this._loadingPromises.set(url, promise);
        return promise;
    }

    /**
     * 加载远程配置文件
     */
    public async loadRemoteConfig(url: string): Promise<any> {
        try {
            console.log(`🔄 开始加载远程配置: ${url}`);
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const config = await response.json();
            console.log(`✅ 远程配置加载成功: ${url}`);
            return config;
            
        } catch (error) {
            console.error(`❌ 远程配置加载失败: ${url}`, error);
            throw error;
        }
    }

    /**
     * 清理远程资源缓存
     */
    public clearCache(): void {
        this._remoteCache.clear();
        console.log('🗑️ 清理远程资源缓存');
    }

    /**
     * 获取缓存状态
     */
    public getCacheStats(): { cached: number, loading: number } {
        return {
            cached: this._remoteCache.size,
            loading: this._loadingPromises.size
        };
    }
}