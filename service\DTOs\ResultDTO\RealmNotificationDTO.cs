namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 境界突破通知DTO
    /// </summary>
    public class RealmNotificationDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "realm_breakthrough";

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 玩家昵称
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string PetName { get; set; }

        /// <summary>
        /// 突破前境界
        /// </summary>
        public string OldRealm { get; set; }

        /// <summary>
        /// 突破后境界
        /// </summary>
        public string NewRealm { get; set; }

        /// <summary>
        /// 境界等级提升
        /// </summary>
        public int RealmLevelIncrease { get; set; }

        /// <summary>
        /// 属性提升信息
        /// </summary>
        public RealmAttributeBonus AttributeBonus { get; set; }

        /// <summary>
        /// 是否是重大突破（如大境界突破）
        /// </summary>
        public bool MajorBreakthrough { get; set; }

        /// <summary>
        /// 消耗的材料或资源
        /// </summary>
        public List<ConsumedResource> ConsumedResources { get; set; } = new List<ConsumedResource>();

        /// <summary>
        /// 突破时间
        /// </summary>
        public DateTime BreakthroughTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否广播给所有玩家
        /// </summary>
        public bool Broadcast { get; set; }

        /// <summary>
        /// 特殊效果描述
        /// </summary>
        public string SpecialEffect { get; set; }
    }

    /// <summary>
    /// 境界属性加成信息
    /// </summary>
    public class RealmAttributeBonus
    {
        /// <summary>
        /// 攻击力提升
        /// </summary>
        public int AttackBonus { get; set; }

        /// <summary>
        /// 防御力提升
        /// </summary>
        public int DefenseBonus { get; set; }

        /// <summary>
        /// 生命值提升
        /// </summary>
        public int HpBonus { get; set; }

        /// <summary>
        /// 法力值提升
        /// </summary>
        public int MpBonus { get; set; }

        /// <summary>
        /// 速度提升
        /// </summary>
        public int SpeedBonus { get; set; }

        /// <summary>
        /// 总战力提升
        /// </summary>
        public int PowerIncrease { get; set; }
    }

    /// <summary>
    /// 消耗的资源信息
    /// </summary>
    public class ConsumedResource
    {
        /// <summary>
        /// 资源类型 (gold/yuanbao/item)
        /// </summary>
        public string ResourceType { get; set; }

        /// <summary>
        /// 资源ID（如果是道具）
        /// </summary>
        public int? ResourceId { get; set; }

        /// <summary>
        /// 资源名称
        /// </summary>
        public string ResourceName { get; set; }

        /// <summary>
        /// 消耗数量
        /// </summary>
        public int Amount { get; set; }
    }
} 