﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///用户境界表
    ///</summary>
    [SugarTable("user_realm")]
    public partial class user_realm
    {
           public user_realm(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:用户ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int user_id {get;set;}

           /// <summary>
           /// Desc:境界等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? realm_level {get;set;}

           /// <summary>
           /// Desc:境界经验
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? realm_exp {get;set;}

           /// <summary>
           /// Desc:突破次数
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? breakthrough_count {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
