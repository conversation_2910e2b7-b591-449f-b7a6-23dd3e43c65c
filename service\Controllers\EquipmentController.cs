using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Services;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 装备管理控制�?    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class EquipmentController : ControllerBase
    {
        private readonly IEquipmentService _equipmentService;
        private readonly IGemstoneService _gemstoneService;
        private readonly ISuitService _suitService;
        private readonly EquipmentEnhanceService _enhanceService;
        private readonly EquipmentGemstoneService _equipmentGemstoneService;
        private readonly EquipmentObtainService _obtainService;
        private readonly SuitEffectService _suitEffectService;
        private readonly ElementTransformService _elementTransformService;
        private readonly ILogger<EquipmentController> _logger;

        public EquipmentController(
            IEquipmentService equipmentService,
            IGemstoneService gemstoneService,
            ISuitService suitService,
            EquipmentEnhanceService enhanceService,
            EquipmentGemstoneService equipmentGemstoneService,
            EquipmentObtainService obtainService,
            SuitEffectService suitEffectService,
            ElementTransformService elementTransformService,
            ILogger<EquipmentController> logger)
        {
            _equipmentService = equipmentService;
            _gemstoneService = gemstoneService;
            _suitService = suitService;
            _enhanceService = enhanceService;
            _equipmentGemstoneService = equipmentGemstoneService;
            _obtainService = obtainService;
            _suitEffectService = suitEffectService;
            _elementTransformService = elementTransformService;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户所有装�?        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>装备列表</returns>
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<ApiResult<List<UserEquipmentDto>>>> GetUserEquipments(int userId)
        {
            try
            {
                var equipments = await _equipmentService.GetUserEquipmentsAsync(userId);
                return Ok(ApiResult<List<UserEquipmentDto>>.CreateSuccess(equipments));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户装备失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<List<UserEquipmentDto>>.CreateError("获取装备列表失败"));
            }
        }

        /// <summary>
        /// 获取装备详情
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns>装备详情</returns>
        [HttpGet("{userEquipmentId}")]
        public async Task<ActionResult<ApiResult<UserEquipmentDto>>> GetEquipmentById(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentService.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return NotFound(ApiResult<UserEquipmentDto>.CreateError("装备不存在"));

                return Ok(ApiResult<UserEquipmentDto>.CreateSuccess(equipment));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备详情失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult<UserEquipmentDto>.CreateError("获取装备详情失败"));
            }
        }

        /// <summary>
        /// 装备到宠物 (旧版本)
        /// </summary>
        /// <param name="request">装备请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("equip-legacy")]
        public async Task<ActionResult<ApiResult>> EquipToPetLegacy([FromBody] EquipRequest request)
        {
            try
            {
                var result = await _equipmentService.EquipToPetAsync(request.UserEquipmentId, request.PetId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备穿戴失败，装备ID: {EquipmentId}, 宠物ID: {PetId}", 
                    request.UserEquipmentId, request.PetId);
                return StatusCode(500, ApiResult.CreateError("装备穿戴失败"));
            }
        }

        /// <summary>
        /// 卸下装备 (旧版本)
        /// </summary>
        /// <param name="request">卸装请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("unequip-legacy")]
        public async Task<ActionResult<ApiResult>> UnequipFromPetLegacy([FromBody] UnequipRequest request)
        {
            try
            {
                var result = await _equipmentService.UnequipFromPetAsync(request.UserEquipmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备卸下失败，装备ID: {EquipmentId}", request.UserEquipmentId);
                return StatusCode(500, ApiResult.CreateError("装备卸下失败"));
            }
        }

        /// <summary>
        /// 获取宠物装备
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>宠物装备列表</returns>
        [HttpGet("pet/{petId}")]
        public async Task<ActionResult<ApiResult<List<UserEquipmentDto>>>> GetPetEquipments(int petId)
        {
            try
            {
                var equipments = await _equipmentService.GetPetEquipmentsAsync(petId);
                return Ok(ApiResult<List<UserEquipmentDto>>.CreateSuccess(equipments));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物装备失败，宠物ID: {PetId}", petId);
                return StatusCode(500, ApiResult<List<UserEquipmentDto>>.CreateError("获取宠物装备失败"));
            }
        }

        /// <summary>
        /// 获取用户未使用装�?        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>未使用装备列�?/returns>
        [HttpGet("user/{userId}/unused")]
        public async Task<ActionResult<ApiResult<List<UserEquipmentDto>>>> GetUnusedEquipments(int userId)
        {
            try
            {
                var equipments = await _equipmentService.GetUnusedEquipmentsAsync(userId);
                return Ok(ApiResult<List<UserEquipmentDto>>.CreateSuccess(equipments));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取未使用装备失败，用户ID: {UserId}", userId);
                return StatusCode(500, ApiResult<List<UserEquipmentDto>>.CreateError("获取未使用装备失败"));
            }
        }

        /// <summary>
        /// 添加装备
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="equipTypeId">装备类型ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("user/{userId}/add/{equipTypeId}")]
        public async Task<ActionResult<ApiResult>> AddEquipment(int userId, string equipTypeId)
        {
            try
            {
                var result = await _equipmentService.AddEquipmentAsync(userId, equipTypeId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加装备失败，用户ID: {UserId}, 装备类型: {EquipTypeId}", 
                    userId, equipTypeId);
                return StatusCode(500, ApiResult.CreateError("添加装备失败"));
            }
        }

        /// <summary>
        /// 删除装备
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{userEquipmentId}")]
        public async Task<ActionResult<ApiResult>> DeleteEquipment(int userEquipmentId)
        {
            try
            {
                var result = await _equipmentService.DeleteEquipmentAsync(userEquipmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除装备失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult.CreateError("删除装备失败"));
            }
        }

        /// <summary>
        /// 强化装备
        /// </summary>
        /// <param name="request">强化请求</param>
        /// <returns>强化结果</returns>
        [HttpPost("strengthen")]
        public async Task<ActionResult<ApiResult<StrengthenResult>>> StrengthenEquipment(
            [FromBody] StrengthenRequest request)
        {
            try
            {
                var result = await _equipmentService.StrengthenEquipmentAsync(request.UserEquipmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备强化失败，装备ID: {EquipmentId}", request.UserEquipmentId);
                return StatusCode(500, ApiResult<StrengthenResult>.CreateError("装备强化失败"));
            }
        }

        /// <summary>
        /// 获取强化消耗预�?        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns>强化消耗信�?/returns>
        [HttpGet("strengthen/cost/{userEquipmentId}")]
        public async Task<ActionResult<ApiResult<StrengthenCostDto>>> GetStrengthenCost(int userEquipmentId)
        {
            try
            {
                var cost = await _equipmentService.CalculateStrengthenCostAsync(userEquipmentId);
                return Ok(ApiResult<StrengthenCostDto>.CreateSuccess(cost));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取强化消耗失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult<StrengthenCostDto>.CreateError("获取强化消耗失败"));
            }
        }

        /// <summary>
        /// 镶嵌宝石
        /// </summary>
        /// <param name="request">镶嵌请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("gemstone/embed")]
        public async Task<ActionResult<ApiResult>> EmbedGemstone([FromBody] EmbedGemstoneRequest request)
        {
            try
            {
                var result = await _equipmentService.EmbedGemstoneAsync(
                    request.UserEquipmentId, request.GemstoneTypeId, request.Position);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石镶嵌失败");
                return StatusCode(500, ApiResult.CreateError("宝石镶嵌失败"));
            }
        }

        /// <summary>
        /// 拆卸宝石
        /// </summary>
        /// <param name="request">拆卸请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("gemstone/remove")]
        public async Task<ActionResult<ApiResult>> RemoveGemstone([FromBody] RemoveGemstoneRequest request)
        {
            try
            {
                var result = await _equipmentService.RemoveGemstoneAsync(
                    request.UserEquipmentId, request.Position);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石拆卸失败");
                return StatusCode(500, ApiResult.CreateError("宝石拆卸失败"));
            }
        }

        /// <summary>
        /// 获取所有宝石配�?        /// </summary>
        /// <returns>宝石配置列表</returns>
        [HttpGet("gemstone/configs")]
        public async Task<ActionResult<ApiResult<List<GemstoneConfigDto>>>> GetGemstoneConfigs()
        {
            try
            {
                var configs = await _gemstoneService.GetAllGemstonesAsync();
                return Ok(ApiResult<List<GemstoneConfigDto>>.CreateSuccess(configs));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宝石配置失败");
                return StatusCode(500, ApiResult<List<GemstoneConfigDto>>.CreateError("获取宝石配置失败"));
            }
        }

        /// <summary>
        /// 获取套装激活状�?        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>套装激活状�?/returns>
        [HttpGet("suit/activation/{petId}")]
        public async Task<ActionResult<ApiResult<List<SuitActivationDto>>>> GetSuitActivations(int petId)
        {
            try
            {
                var activations = await _suitService.GetPetSuitActivationsAsync(petId);
                return Ok(ApiResult<List<SuitActivationDto>>.CreateSuccess(activations));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装激活状态失败，宠物ID: {PetId}", petId);
                return StatusCode(500, ApiResult<List<SuitActivationDto>>.CreateError("获取套装激活状态失败"));
            }
        }

        /// <summary>
        /// 五行点化 (旧版本)
        /// </summary>
        /// <param name="request">点化请求</param>
        /// <returns>操作结果</returns>
        [HttpPost("element/transform-legacy")]
        public async Task<ActionResult<ApiResult>> TransformElementLegacy([FromBody] TransformElementRequest request)
        {
            try
            {
                var result = await _equipmentService.TransformElementAsync(request.UserEquipmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "五行点化失败");
                return StatusCode(500, ApiResult.CreateError("五行点化失败"));
            }
        }

        /// <summary>
        /// 装备分解
        /// </summary>
        /// <param name="request">分解请求</param>
        /// <returns>分解结果</returns>
        [HttpPost("resolve")]
        public async Task<ActionResult<ApiResult<ResolveResult>>> ResolveEquipment([FromBody] ResolveRequest request)
        {
            try
            {
                var result = await _equipmentService.ResolveEquipmentAsync(request.UserEquipmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备分解失败");
                return StatusCode(500, ApiResult<ResolveResult>.CreateError("装备分解失败"));
            }
        }

        #region 装备穿戴系统 (新增)

        /// <summary>
        /// 装备穿戴到宠物
        /// </summary>
        /// <param name="request">穿戴请求</param>
        /// <returns></returns>
        [HttpPost("equip")]
        public async Task<ActionResult<ApiResult>> EquipToPet([FromBody] EquipToPetRequest request)
        {
            try
            {
                if (request.UserEquipmentId <= 0 || request.PetId <= 0)
                    return BadRequest(ApiResult.CreateError("参数无效"));

                var result = await _equipmentService.EquipToPetAsync(request.UserEquipmentId, request.PetId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备穿戴失败，装备ID: {EquipmentId}, 宠物ID: {PetId}",
                    request.UserEquipmentId, request.PetId);
                return StatusCode(500, ApiResult.CreateError("装备穿戴失败"));
            }
        }

        /// <summary>
        /// 卸下装备
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        [HttpPost("unequip/{userEquipmentId}")]
        public async Task<ActionResult<ApiResult>> UnequipFromPet(int userEquipmentId)
        {
            try
            {
                if (userEquipmentId <= 0)
                    return BadRequest(ApiResult.CreateError("装备ID无效"));

                var result = await _equipmentService.UnequipFromPetAsync(userEquipmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "卸下装备失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult.CreateError("卸下装备失败"));
            }
        }

        /// <summary>
        /// 卸下宠物所有装备
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns></returns>
        [HttpPost("unequip-all/{petId}")]
        public async Task<ActionResult<ApiResult>> UnequipAllFromPet(int petId)
        {
            try
            {
                if (petId <= 0)
                    return BadRequest(ApiResult.CreateError("宠物ID无效"));

                var result = await _equipmentService.UnequipAllFromPetAsync(petId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量卸下装备失败，宠物ID: {PetId}", petId);
                return StatusCode(500, ApiResult.CreateError("批量卸下装备失败"));
            }
        }

        #endregion

        #region 装备强化系统 (新增)

        /// <summary>
        /// 装备强化
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        [HttpPost("enhance/{userEquipmentId}")]
        public async Task<ActionResult<ApiResult>> EnhanceEquipment(int userEquipmentId)
        {
            try
            {
                if (userEquipmentId <= 0)
                    return BadRequest(ApiResult.CreateError("装备ID无效"));

                var result = await _enhanceService.EnhanceEquipmentAsync(userEquipmentId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "装备强化失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult.CreateError("装备强化失败"));
            }
        }

        #endregion

        #region 宝石镶嵌系统 (新增)

        /// <summary>
        /// 宝石操作 (镶嵌、清空、开孔等)
        /// </summary>
        /// <param name="request">宝石操作请求</param>
        /// <returns></returns>
        [HttpPost("gemstone-operation")]
        public async Task<ActionResult<ApiResult>> GemstoneOperation([FromBody] GemstoneOperationRequest request)
        {
            try
            {
                if (request.UserEquipmentId <= 0 || string.IsNullOrEmpty(request.ItemId))
                    return BadRequest(ApiResult.CreateError("参数无效"));

                var result = await _equipmentGemstoneService.SetGemstoneAsync(request.UserEquipmentId, request.ItemId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石操作失败，装备ID: {EquipmentId}, 道具ID: {ItemId}",
                    request.UserEquipmentId, request.ItemId);
                return StatusCode(500, ApiResult.CreateError("宝石操作失败"));
            }
        }

        #endregion

        #region 装备获得系统 (新增)

        /// <summary>
        /// 处理装备获得脚本
        /// </summary>
        /// <param name="request">装备获得脚本请求</param>
        /// <returns></returns>
        [HttpPost("obtain-script")]
        public async Task<ActionResult<ApiResult<EquipmentObtainResult>>> ProcessObtainScript([FromBody] EquipmentObtainScriptRequest request)
        {
            try
            {
                if (request.UserId <= 0 || string.IsNullOrEmpty(request.ScriptContent))
                    return BadRequest(ApiResult<EquipmentObtainResult>.CreateError("参数无效"));

                var result = await _obtainService.ProcessEquipmentObtainScriptAsync(request.UserId, request.ScriptContent);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理装备获得脚本失败，用户ID: {UserId}", request.UserId);
                return StatusCode(500, ApiResult<EquipmentObtainResult>.CreateError("处理装备获得脚本失败"));
            }
        }

        /// <summary>
        /// 直接发放装备 (管理员功能)
        /// </summary>
        /// <param name="request">装备发放请求</param>
        /// <returns></returns>
        [HttpPost("grant")]
        public async Task<ActionResult<ApiResult<EquipmentObtainResult>>> GrantEquipment([FromBody] EquipmentGrantRequest request)
        {
            try
            {
                if (request.UserId <= 0 || string.IsNullOrEmpty(request.EquipId))
                    return BadRequest(ApiResult<EquipmentObtainResult>.CreateError("参数无效"));

                var result = await _obtainService.GrantEquipmentAsync(request.UserId, request.EquipId,
                    request.Element, request.StrengthenLevel);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放装备失败，用户ID: {UserId}, 装备ID: {EquipId}",
                    request.UserId, request.EquipId);
                return StatusCode(500, ApiResult<EquipmentObtainResult>.CreateError("发放装备失败"));
            }
        }

        /// <summary>
        /// 创建指定装备
        /// </summary>
        /// <param name="request">装备创建请求</param>
        /// <returns></returns>
        [HttpPost("create")]
        public async Task<ActionResult<ApiResult<EquipmentObtainResult>>> CreateEquipment([FromBody] EquipmentCreateRequest request)
        {
            try
            {
                if (request.UserId <= 0 || string.IsNullOrEmpty(request.EquipId))
                    return BadRequest(ApiResult<EquipmentObtainResult>.CreateError("参数无效"));

                var result = await _obtainService.CreateEquipmentAsync(request.UserId, request.EquipId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建装备失败，用户ID: {UserId}, 装备ID: {EquipId}",
                    request.UserId, request.EquipId);
                return StatusCode(500, ApiResult<EquipmentObtainResult>.CreateError("创建装备失败"));
            }
        }

        #endregion

        #region 宝石镶嵌系统 (新增)

        /// <summary>
        /// 宝石镶嵌/开孔/清空等操作
        /// </summary>
        /// <param name="request">宝石操作请求</param>
        /// <returns></returns>
        [HttpPost("gemstone")]
        public async Task<ActionResult<ApiResult>> SetGemstone([FromBody] GemstoneOperationRequest request)
        {
            try
            {
                if (request.UserEquipmentId <= 0 || string.IsNullOrEmpty(request.ItemId))
                    return BadRequest(ApiResult.CreateError("参数无效"));

                var result = await _equipmentGemstoneService.SetGemstoneAsync(request.UserEquipmentId, request.ItemId);

                if (result.Success)
                    return Ok(result);
                else
                    return StatusCode(500, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "宝石操作失败，装备ID: {EquipmentId}, 道具ID: {ItemId}", request.UserEquipmentId, request.ItemId);
                return StatusCode(500, ApiResult.CreateError("宝石操作失败"));
            }
        }

        /// <summary>
        /// 获取装备宝石信息
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        [HttpGet("gemstones/{userEquipmentId}")]
        public async Task<ActionResult<ApiResult<List<GemstoneDto>>>> GetEquipmentGemstones(int userEquipmentId)
        {
            try
            {
                if (userEquipmentId <= 0)
                    return BadRequest(ApiResult<List<GemstoneDto>>.CreateError("装备ID无效"));

                var gemstones = await _gemstoneService.GetEquipmentGemstonesAsync(userEquipmentId);
                return Ok(ApiResult<List<GemstoneDto>>.CreateSuccess(gemstones, "获取宝石信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备宝石信息失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult<List<GemstoneDto>>.CreateError("获取宝石信息失败"));
            }
        }

        /// <summary>
        /// 移除指定位置的宝石
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <param name="position">宝石位置</param>
        /// <returns></returns>
        [HttpDelete("gemstone/{userEquipmentId}/{position}")]
        public async Task<ActionResult<ApiResult>> RemoveGemstone(int userEquipmentId, int position)
        {
            try
            {
                if (userEquipmentId <= 0 || position <= 0)
                    return BadRequest(ApiResult.CreateError("参数无效"));

                // 暂时返回成功，具体实现待完善
                var result = ApiResult.CreateSuccess("移除宝石功能待实现");

                if (result.Success)
                    return Ok(result);
                else
                    return StatusCode(500, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除宝石失败，装备ID: {EquipmentId}, 位置: {Position}", userEquipmentId, position);
                return StatusCode(500, ApiResult.CreateError("移除宝石失败"));
            }
        }

        #endregion

        #region 五行点化系统 (新增)

        /// <summary>
        /// 五行点化
        /// </summary>
        /// <param name="request">点化请求</param>
        /// <returns></returns>
        [HttpPost("element/transform")]
        public async Task<ActionResult<ApiResult<ElementTransformResult>>> TransformElement([FromBody] ElementTransformRequest request)
        {
            try
            {
                if (request.UserEquipmentId <= 0)
                    return BadRequest(ApiResult<ElementTransformResult>.CreateError("装备ID无效"));

                var result = await _elementTransformService.TransformElementAsync(request.UserEquipmentId);

                if (result.Success)
                    return Ok(result);
                else
                    return StatusCode(500, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "五行点化失败，装备ID: {EquipmentId}", request.UserEquipmentId);
                return StatusCode(500, ApiResult<ElementTransformResult>.CreateError("五行点化失败"));
            }
        }

        /// <summary>
        /// 获取装备五行信息
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        [HttpGet("element/info/{userEquipmentId}")]
        public async Task<ActionResult<ApiResult<ElementInfoResult>>> GetElementInfo(int userEquipmentId)
        {
            try
            {
                if (userEquipmentId <= 0)
                    return BadRequest(ApiResult<ElementInfoResult>.CreateError("装备ID无效"));

                var result = await _elementTransformService.GetElementInfoAsync(userEquipmentId);

                if (result.Success)
                    return Ok(result);
                else
                    return StatusCode(500, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取五行信息失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult<ElementInfoResult>.CreateError("获取五行信息失败"));
            }
        }

        /// <summary>
        /// 获取五行相生相克关系
        /// </summary>
        /// <returns></returns>
        [HttpGet("element/relationships")]
        public ActionResult<ApiResult<ElementRelationshipsResult>> GetElementRelationships()
        {
            try
            {
                var relationships = new ElementRelationshipsResult
                {
                    ElementGeneration = new Dictionary<string, string>
                    {
                        {"生命", "金"}, {"魔法", "木"}, {"攻击", "火"}, {"防御", "土"},
                        {"命中", "雷"}, {"闪避", "水"}, {"速度", "风"}
                    },
                    ElementRestraint = new Dictionary<string, string>
                    {
                        {"生命", "火"}, {"魔法", "金"}, {"攻击", "水"}, {"防御", "木"},
                        {"命中", "风"}, {"闪避", "土"}, {"速度", "雷"}
                    },
                    ElementList = new List<string> { "金", "木", "火", "土", "雷", "水", "风" }
                };

                return Ok(ApiResult<ElementRelationshipsResult>.CreateSuccess(relationships, "获取五行关系成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取五行关系失败");
                return StatusCode(500, ApiResult<ElementRelationshipsResult>.CreateError("获取五行关系失败"));
            }
        }

        #endregion

        #region 套装效果系统 (新增)

        /// <summary>
        /// 计算宠物套装效果
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns></returns>
        [HttpGet("suit-effects/{petId}")]
        public async Task<ActionResult<ApiResult<SuitEffectResult>>> GetPetSuitEffects(int petId)
        {
            try
            {
                if (petId <= 0)
                    return BadRequest(ApiResult<SuitEffectResult>.CreateError("宠物ID无效"));

                var result = await _suitEffectService.CalculatePetSuitEffectsAsync(petId);

                if (result.Success)
                    return Ok(ApiResult<SuitEffectResult>.CreateSuccess(result, "获取套装效果成功"));
                else
                    return StatusCode(500, ApiResult<SuitEffectResult>.CreateError(result.ErrorMessage ?? "计算套装效果失败"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物套装效果失败，宠物ID: {PetId}", petId);
                return StatusCode(500, ApiResult<SuitEffectResult>.CreateError("获取套装效果失败"));
            }
        }

        /// <summary>
        /// 获取装备套装显示信息
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <param name="petId">宠物ID（可选）</param>
        /// <returns></returns>
        [HttpGet("suit-display/{equipId}")]
        public async Task<ActionResult<ApiResult<string>>> GetSuitDisplayInfo(string equipId, [FromQuery] int? petId = null)
        {
            try
            {
                if (string.IsNullOrEmpty(equipId))
                    return BadRequest(ApiResult<string>.CreateError("装备ID无效"));

                var result = await _suitEffectService.GetSuitDisplayInfoAsync(equipId, petId);
                return Ok(ApiResult<string>.CreateSuccess(result, "获取套装显示信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装显示信息失败，装备ID: {EquipId}", equipId);
                return StatusCode(500, ApiResult<string>.CreateError("获取套装显示信息失败"));
            }
        }

        /// <summary>
        /// 获取所有套装配置
        /// </summary>
        /// <returns></returns>
        [HttpGet("suits")]
        public async Task<ActionResult<ApiResult<List<SuitConfigDto>>>> GetAllSuits()
        {
            try
            {
                var result = await _suitService.GetAllSuitsAsync();
                return Ok(ApiResult<List<SuitConfigDto>>.CreateSuccess(result, "获取套装配置成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装配置失败");
                return StatusCode(500, ApiResult<List<SuitConfigDto>>.CreateError("获取套装配置失败"));
            }
        }

        /// <summary>
        /// 获取指定套装配置
        /// </summary>
        /// <param name="suitId">套装ID</param>
        /// <returns></returns>
        [HttpGet("suits/{suitId}")]
        public async Task<ActionResult<ApiResult<SuitConfigDto>>> GetSuitById(string suitId)
        {
            try
            {
                if (string.IsNullOrEmpty(suitId))
                    return BadRequest(ApiResult<SuitConfigDto>.CreateError("套装ID无效"));

                var result = await _suitService.GetSuitByIdAsync(suitId);
                if (result == null)
                    return NotFound(ApiResult<SuitConfigDto>.CreateError("套装不存在"));

                return Ok(ApiResult<SuitConfigDto>.CreateSuccess(result, "获取套装配置成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装配置失败，套装ID: {SuitId}", suitId);
                return StatusCode(500, ApiResult<SuitConfigDto>.CreateError("获取套装配置失败"));
            }
        }

        #endregion
    }

    #region 请求模型 (新增)

    /// <summary>
    /// 装备穿戴请求
    /// </summary>
    public class EquipToPetRequest
    {
        /// <summary>
        /// 用户装备ID
        /// </summary>
        public int UserEquipmentId { get; set; }

        /// <summary>
        /// 宠物ID
        /// </summary>
        public int PetId { get; set; }
    }

    /// <summary>
    /// 宝石操作请求
    /// </summary>
    public class GemstoneOperationRequest
    {
        /// <summary>
        /// 用户装备ID
        /// </summary>
        public int UserEquipmentId { get; set; }

        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 装备获得脚本请求
    /// </summary>
    public class EquipmentObtainScriptRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 脚本内容
        /// </summary>
        public string ScriptContent { get; set; } = string.Empty;
    }

    /// <summary>
    /// 装备发放请求
    /// </summary>
    public class EquipmentGrantRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public string EquipId { get; set; } = string.Empty;

        /// <summary>
        /// 指定五行属性（可选）
        /// </summary>
        public string? Element { get; set; }

        /// <summary>
        /// 指定强化等级（可选，默认0）
        /// </summary>
        public int StrengthenLevel { get; set; } = 0;
    }

    /// <summary>
    /// 装备创建请求
    /// </summary>
    public class EquipmentCreateRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 装备类型ID
        /// </summary>
        public string EquipId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 五行点化请求模型
    /// </summary>
    public class ElementTransformRequest
    {
        /// <summary>
        /// 用户装备ID
        /// </summary>
        public int UserEquipmentId { get; set; }
    }

    /// <summary>
    /// 五行关系结果模型
    /// </summary>
    public class ElementRelationshipsResult
    {
        /// <summary>
        /// 五行相生关系
        /// </summary>
        public Dictionary<string, string> ElementGeneration { get; set; } = new();

        /// <summary>
        /// 五行相克关系
        /// </summary>
        public Dictionary<string, string> ElementRestraint { get; set; } = new();

        /// <summary>
        /// 五行列表
        /// </summary>
        public List<string> ElementList { get; set; } = new();
    }

    #endregion
}
