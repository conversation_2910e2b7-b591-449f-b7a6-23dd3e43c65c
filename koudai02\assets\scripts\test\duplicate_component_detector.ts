import { _decorator, Component, find, Node } from 'cc';
const { ccclass } = _decorator;

/**
 * 🔍 重复组件检测器
 * 用于检测场景中是否有重复的fightState组件
 */
@ccclass('DuplicateComponentDetector')
export class DuplicateComponentDetector extends Component {

    start() {
        console.log("🔍 开始检测重复的fightState组件...");
        this.detectDuplicateFightStateComponents();
    }

    /**
     * 检测重复的fightState组件
     */
    private detectDuplicateFightStateComponents(): void {
        const canvas = find("Canvas");
        if (!canvas) {
            console.error("❌ 找不到Canvas节点");
            return;
        }

        const fightStateNodes: Node[] = [];
        
        // 递归搜索所有包含fightState组件的节点
        this.searchFightStateNodes(canvas, fightStateNodes);
        
        console.log(`🔍 检测结果：找到 ${fightStateNodes.length} 个fightState组件`);
        
        if (fightStateNodes.length > 1) {
            console.warn("⚠️ 发现重复的fightState组件！");
            fightStateNodes.forEach((node, index) => {
                console.log(`📍 fightState组件 ${index + 1}:`);
                console.log(`   - 节点名称: ${node.name}`);
                console.log(`   - 节点路径: ${this.getNodePath(node)}`);
                console.log(`   - 节点激活状态: ${node.active}`);
                console.log(`   - 组件状态: ${node.getComponent('fightState') ? '存在' : '不存在'}`);
            });
            
            // 给出修复建议
            this.provideFix(fightStateNodes);
        } else if (fightStateNodes.length === 1) {
            console.log("✅ 只找到一个fightState组件，这是正常的");
            const node = fightStateNodes[0];
            console.log(`📍 fightState组件位置: ${this.getNodePath(node)}`);
        } else {
            console.warn("⚠️ 没有找到fightState组件");
        }
    }

    /**
     * 递归搜索包含fightState组件的节点
     */
    private searchFightStateNodes(node: Node, results: Node[]): void {
        // 检查当前节点是否有fightState组件
        const fightStateComponent = node.getComponent('fightState');
        if (fightStateComponent) {
            results.push(node);
        }

        // 递归搜索子节点
        for (let i = 0; i < node.children.length; i++) {
            this.searchFightStateNodes(node.children[i], results);
        }
    }

    /**
     * 获取节点的完整路径
     */
    private getNodePath(node: Node): string {
        const path: string[] = [];
        let current = node;
        
        while (current && current.name !== "Canvas") {
            path.unshift(current.name);
            current = current.parent;
        }
        
        return "Canvas/" + path.join("/");
    }

    /**
     * 提供修复建议
     */
    private provideFix(fightStateNodes: Node[]): void {
        console.log("🔧 修复建议：");
        console.log("1. 检查场景配置，确保只有一个节点挂载fightState组件");
        console.log("2. 推荐保留的节点：包含fightStatePanel的节点");
        console.log("3. 删除多余的fightState组件或节点");
        
        // 尝试识别哪个是正确的节点
        const correctNode = fightStateNodes.find(node => 
            node.name.includes("fightState") || 
            this.getNodePath(node).includes("fightStatePanel")
        );
        
        if (correctNode) {
            console.log(`💡 建议保留的节点: ${this.getNodePath(correctNode)}`);
        }
        
        // 自动禁用多余的节点（除了第一个）
        for (let i = 1; i < fightStateNodes.length; i++) {
            const node = fightStateNodes[i];
            console.log(`🔧 自动禁用多余的节点: ${this.getNodePath(node)}`);
            node.active = false;
        }
    }

    /**
     * 手动修复重复组件（供外部调用）
     */
    public fixDuplicateComponents(): void {
        console.log("🔧 开始手动修复重复组件...");
        this.detectDuplicateFightStateComponents();
    }
} 