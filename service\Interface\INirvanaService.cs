using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Models;
using WebApplication_HM.Utils;

namespace WebApplication_HM.Interface
{
    /// <summary>
    /// 转生服务接口
    /// </summary>
    public interface INirvanaService
    {
        /// <summary>
        /// 执行转生
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <returns>转生结果</returns>
        Task<ApiResult<NirvanaResultDto>> ExecuteNirvanaAsync(NirvanaRequestDto request);

        /// <summary>
        /// 执行变脸
        /// </summary>
        /// <param name="request">变脸请求</param>
        /// <returns>变脸结果</returns>
        Task<ApiResult<FaceChangeResultDto>> ExecuteFaceChangeAsync(FaceChangeRequestDto request);

        /// <summary>
        /// 获取转生配置列表
        /// </summary>
        /// <returns>配置列表</returns>
        Task<ApiResult<List<PetNirvanaConfigDto>>> GetNirvanaConfigsAsync();

        /// <summary>
        /// 预览转生结果
        /// </summary>
        /// <param name="request">预览请求</param>
        /// <returns>预览结果</returns>
        Task<ApiResult<NirvanaPreviewDto>> PreviewNirvanaAsync(NirvanaPreviewRequestDto request);

        /// <summary>
        /// 获取用户转生记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="page">页码</param>
        /// <param name="size">页大小</param>
        /// <returns>转生记录</returns>
        Task<ApiResult<PagedResult<NirvanaRecordDto>>> GetUserRecordsAsync(int userId, int page = 1, int size = 20);

        /// <summary>
        /// 获取转生统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>统计信息</returns>
        Task<ApiResult<NirvanaStatisticsDto>> GetStatisticsAsync(DateTime? startDate, DateTime? endDate);
    }

    /// <summary>
    /// 转生计算服务接口
    /// </summary>
    public interface INirvanaCalculationService
    {
        /// <summary>
        /// 计算成长获得
        /// </summary>
        /// <param name="mainPet">主宠</param>
        /// <param name="subPet">副宠</param>
        /// <param name="effectBonus">效果加成</param>
        /// <param name="user">用户信息</param>
        /// <returns>获得成长</returns>
        Task<decimal> CalculateGrowthGainAsync(user_pet mainPet, user_pet subPet, decimal effectBonus, user user);

        /// <summary>
        /// 计算成功率
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <param name="config">转生配置</param>
        /// <returns>成功率</returns>
        Task<decimal> CalculateSuccessRateAsync(NirvanaRequestDto request, PetNirvanaConfig config);

        /// <summary>
        /// 验证转生条件
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <returns>验证结果</returns>
        Task<(bool isValid, List<string> errors)> ValidateNirvanaConditionsAsync(NirvanaRequestDto request);

        /// <summary>
        /// 计算VIP加成
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>VIP加成比例</returns>
        decimal CalculateVipBonus(user user);

        /// <summary>
        /// 计算转生消耗金币
        /// </summary>
        /// <param name="config">转生配置</param>
        /// <param name="user">用户信息</param>
        /// <returns>消耗金币</returns>
        long CalculateCostGold(PetNirvanaConfig config, user user);
    }

    /// <summary>
    /// 变脸服务接口
    /// </summary>
    public interface IFaceChangeService
    {
        /// <summary>
        /// 执行变脸
        /// </summary>
        /// <param name="request">变脸请求</param>
        /// <returns>变脸结果</returns>
        Task<ApiResult<FaceChangeResultDto>> ExecuteFaceChangeAsync(FaceChangeRequestDto request);

        /// <summary>
        /// 验证变脸条件
        /// </summary>
        /// <param name="request">变脸请求</param>
        /// <returns>验证结果</returns>
        Task<(bool isValid, List<string> errors)> ValidateFaceChangeConditionsAsync(FaceChangeRequestDto request);

        /// <summary>
        /// 转移技能
        /// </summary>
        /// <param name="sourcePet">源宠物</param>
        /// <param name="targetPet">目标宠物</param>
        /// <param name="transferNonTalent">是否转移非天赋技能</param>
        /// <returns>转移的技能数量</returns>
        Task<int> TransferSkillsAsync(user_pet sourcePet, user_pet targetPet, bool transferNonTalent);

        /// <summary>
        /// 计算属性继承
        /// </summary>
        /// <param name="sourcePet">源宠物</param>
        /// <param name="targetPet">目标宠物</param>
        /// <param name="inheritRatio">继承比例</param>
        /// <returns>继承后的宠物</returns>
        Task<user_pet> CalculateAttributeInheritanceAsync(user_pet sourcePet, user_pet targetPet, decimal inheritRatio);
    }

    /// <summary>
    /// 转生配置服务接口
    /// </summary>
    public interface INirvanaConfigService
    {
        /// <summary>
        /// 获取活跃配置
        /// </summary>
        /// <returns>配置列表</returns>
        Task<List<PetNirvanaConfig>> GetActiveConfigsAsync();

        /// <summary>
        /// 查找匹配的配置
        /// </summary>
        /// <param name="mainPetNo">主宠编号</param>
        /// <param name="subPetNo">副宠编号</param>
        /// <param name="nirvanaPetNo">涅槃兽编号</param>
        /// <returns>匹配的配置</returns>
        Task<PetNirvanaConfig> FindMatchingConfigAsync(int mainPetNo, int subPetNo, int nirvanaPetNo);

        /// <summary>
        /// 根据主宠获取配置
        /// </summary>
        /// <param name="mainPetNo">主宠编号</param>
        /// <returns>配置列表</returns>
        Task<List<PetNirvanaConfig>> GetConfigsByMainPetAsync(int mainPetNo);

        /// <summary>
        /// 更新配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateConfigAsync(PetNirvanaConfig config);

        /// <summary>
        /// 创建配置
        /// </summary>
        /// <param name="config">配置信息</param>
        /// <returns>创建的配置</returns>
        Task<PetNirvanaConfig> CreateConfigAsync(PetNirvanaConfig config);

        /// <summary>
        /// 删除配置
        /// </summary>
        /// <param name="id">配置ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteConfigAsync(int id);
    }

    /// <summary>
    /// 转生反作弊服务接口
    /// </summary>
    public interface INirvanaAntiCheatService
    {
        /// <summary>
        /// 验证转生请求
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateNirvanaRequestAsync(NirvanaRequestDto request);

        /// <summary>
        /// 验证冷却时间
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否通过验证</returns>
        Task<bool> ValidateCooldownAsync(int userId);

        /// <summary>
        /// 验证宠物有效性
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <returns>是否通过验证</returns>
        Task<bool> ValidatePetsAsync(NirvanaRequestDto request);

        /// <summary>
        /// 验证资源
        /// </summary>
        /// <param name="request">转生请求</param>
        /// <param name="costGold">消耗金币</param>
        /// <returns>是否通过验证</returns>
        Task<bool> ValidateResourcesAsync(NirvanaRequestDto request, long costGold);

        /// <summary>
        /// 验证频率
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否通过验证</returns>
        Task<bool> ValidateFrequencyAsync(int userId);

        /// <summary>
        /// 记录转生尝试
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>任务</returns>
        Task RecordNirvanaAttemptAsync(int userId);
    }
}
