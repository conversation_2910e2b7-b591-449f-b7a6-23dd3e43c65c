import { SpriteAtlas, SpriteFrame, Prefab, AudioClip, resources, JsonAsset, Asset } from 'cc';
import { LRUResourceCache } from './LRUResourceCache';

/**
 * 大规模资源管理器
 * 专门处理500+宠物和1000+怪物的资源加载优化
 */
export class MassiveResourceManager {
    private static _instance: MassiveResourceManager;
    private lruCache: LRUResourceCache;
    private loadingPromises: Map<string, Promise<any>> = new Map();
    private resourceConfig: any = null;
    private preloadQueue: string[] = [];
    private isPreloading: boolean = false;
    
    // 资源分组配置
    private resourceGroups = {
        pets: {
            starter: { ids: Array.from({length: 10}, (_, i) => i + 1), atlas: 'pets/starter_atlas', enabled: true },
            common: { ids: Array.from({length: 100}, (_, i) => i + 11), atlas: 'pets/starter_atlas', enabled: true },
            rare: { ids: Array.from({length: 200}, (_, i) => i + 111), atlas: 'pets/rare_atlas', enabled: false },
            epic: { ids: Array.from({length: 190}, (_, i) => i + 311), atlas: 'pets/epic_atlas', enabled: false }
        },
        monsters: {
            normal: { ids: Array.from({length: 500}, (_, i) => i + 1), atlas: 'monsters/normal_atlas', enabled: true },
            elite: { ids: Array.from({length: 300}, (_, i) => i + 501), atlas: 'monsters/elite_atlas', enabled: false },
            boss: { ids: Array.from({length: 200}, (_, i) => i + 801), atlas: 'monsters/boss_atlas', enabled: false }
        }
    };

    public static getInstance(): MassiveResourceManager {
        if (!MassiveResourceManager._instance) {
            MassiveResourceManager._instance = new MassiveResourceManager();
        }
        return MassiveResourceManager._instance;
    }

    constructor() {
        this.lruCache = new LRUResourceCache(150, 200); // 最多150个资源，200MB内存
        this.loadResourceConfig();
    }

    /**
     * 加载资源配置文件
     */
    private async loadResourceConfig(): Promise<void> {
        try {
            this.resourceConfig = await this.loadJson('config/resource_groups');
            console.log('📋 资源配置加载完成');
        } catch (error) {
            console.warn('⚠️ 资源配置加载失败，使用默认配置');
            this.resourceConfig = this.resourceGroups;
        }
    }

    /**
     * 智能预加载 - 根据用户等级和当前场景
     */
    public async smartPreload(userLevel: number, currentScene: string): Promise<void> {
        console.log(`🧠 开始智能预加载 (等级: ${userLevel}, 场景: ${currentScene})`);
        
        const preloadList: string[] = [];

        // 简化的预加载逻辑：根据用户等级决定预加载哪些图集
        if (userLevel <= 10) {
            preloadList.push('pets/starter_atlas', 'pets/common_atlas');
        } else if (userLevel <= 30) {
            preloadList.push('pets/common_atlas', 'pets/rare_atlas');
        } else {
            preloadList.push('pets/common_atlas', 'pets/rare_atlas', 'pets/epic_atlas');
        }

        // 根据场景预加载对应怪物
        switch (currentScene) {
            case 'Friom':
                preloadList.push('monsters/normal_atlas');
                break;
            case 'Battle':
                preloadList.push('monsters/normal_atlas', 'monsters/elite_atlas');
                break;
            case 'Boss':
                preloadList.push('monsters/boss_atlas', 'monsters/elite_atlas');
                break;
        }

        // 分批预加载，避免卡顿
        await this.batchPreload(preloadList, 3); // 每次最多3个并发
    }

    /**
     * 分批预加载
     */
    private async batchPreload(resourceList: string[], batchSize: number = 3): Promise<void> {
        this.isPreloading = true;
        
        for (let i = 0; i < resourceList.length; i += batchSize) {
            const batch = resourceList.slice(i, i + batchSize);
            const promises = batch.map(path => this.preloadSingle(path));
            
            try {
                await Promise.all(promises);
                console.log(`✅ 批次预加载完成: ${batch.join(', ')}`);
                
                // 每批之间间隔50ms，避免阻塞主线程
                await new Promise(resolve => setTimeout(resolve, 50));
            } catch (error) {
                console.error(`❌ 批次预加载失败:`, error);
            }
        }
        
        this.isPreloading = false;
        console.log('🎉 智能预加载全部完成');
    }

    /**
     * 预加载单个资源
     */
    private preloadSingle(path: string): Promise<void> {
        return new Promise((resolve, reject) => {
            resources.preload(path, (err) => {
                if (err) {
                    console.error(`❌ 预加载失败: ${path}`, err);
                    reject(err);
                } else {
                    console.log(`✅ 预加载成功: ${path}`);
                    resolve();
                }
            });
        });
    }

    /**
     * 根据ID获取宠物资源组
     */
    public getPetGroup(petId: number): { group: string; atlas: string; enabled: boolean } {
        const config = this.resourceConfig || this.resourceGroups;
        
        for (const groupName in config.pets) {
            const groupData = config.pets[groupName];
            let ids = groupData.ids;
            
            // 检查图集是否启用
            const enabled = groupData.enabled !== false; // 默认为true，只有明确设置false才禁用
            
            // 如果IDs是字符串格式"11-110"，需要解析为数组
            if (typeof ids === 'string' && ids.indexOf('-') !== -1) {
                const parts = ids.split('-');
                const start = parseInt(parts[0]);
                const end = parseInt(parts[1]);
                ids = [];
                for (let i = start; i <= end; i++) {
                    ids.push(i);
                }
            }
            
            // 检查宠物ID是否在当前分组中
            if (Array.isArray(ids) && ids.indexOf(petId) !== -1) {
                if (!enabled) {
                    console.warn(`⚠️ 宠物${petId}对应的图集${groupData.atlas}已被禁用`);
                } else {
                    console.log(`🐾 宠物${petId}使用图集: ${groupData.atlas}`);
                }
                return { 
                    group: groupName, 
                    atlas: groupData.atlas,
                    enabled: enabled 
                };
            }
        }
        
        console.warn(`⚠️ 宠物${petId}未找到对应分组，使用默认图集`);
        return { group: 'starter', atlas: 'pets/starter_atlas', enabled: true }; // 默认使用启用的starter图集
    }

    /**
     * 根据ID获取怪物资源组
     */
    public getMonsterGroup(monsterId: number): { group: string; atlas: string; enabled: boolean } {
        const config = this.resourceConfig || this.resourceGroups;
        
        for (const groupName in config.monsters) {
            const groupData = config.monsters[groupName];
            let ids = groupData.ids;
            
            // 检查图集是否启用
            const enabled = groupData.enabled !== false; // 默认为true，只有明确设置false才禁用
            
            // 如果IDs是字符串格式"1-500"，需要解析为数组
            if (typeof ids === 'string' && ids.indexOf('-') !== -1) {
                const parts = ids.split('-');
                const start = parseInt(parts[0]);
                const end = parseInt(parts[1]);
                ids = [];
                for (let i = start; i <= end; i++) {
                    ids.push(i);
                }
            }
            
            // 检查怪物ID是否在当前分组中
            if (Array.isArray(ids) && ids.indexOf(monsterId) !== -1) {
                if (!enabled) {
                    console.warn(`⚠️ 怪物${monsterId}对应的图集${groupData.atlas}已被禁用`);
                } else {
                    console.log(`👹 怪物${monsterId}使用图集: ${groupData.atlas}`);
                }
                return { 
                    group: groupName, 
                    atlas: groupData.atlas,
                    enabled: enabled 
                };
            }
        }
        
        console.warn(`⚠️ 怪物${monsterId}未找到对应分组，使用默认图集`);
        return { group: 'normal', atlas: 'monsters/normal_atlas', enabled: true }; // 默认使用启用的normal图集
    }

    /**
     * 通用宠物资源加载方法
     * 支持不同类型的宠物资源：g、S(攻击)、z(动态)、q、k、t(头像)
     */
    public async loadPetResource(petId: number, resourceType: string): Promise<SpriteFrame> {
        const { atlas, enabled } = this.getPetGroup(petId);
        const cacheKey = `pet_${petId}_${resourceType}`;
        
        // 检查图集是否启用
        if (!enabled) {
            console.warn(`⚠️ 宠物${petId}的图集${atlas}已被禁用，使用默认资源`);
            return await this.getDefaultResource(resourceType);
        }
        
        // 检查LRU缓存
        const cached = this.lruCache.get(cacheKey);
        if (cached) {
            return cached;
        }

        try {
            const spriteAtlas = await this.ensureAtlasLoaded(atlas);
            
            // 🔧 修复：根据资源类型使用不同的帧名格式
            let frameName: string;
            if (resourceType === 't' || resourceType === 'k' || resourceType === 'q') {
                // 头像类型：单张图片格式 t1, k1, q1
                frameName = `${resourceType}${petId}`;
            } else {
                // 动作类型：序列格式 z1-0, g1-0, S1-0 (默认取第一帧)
                frameName = `${resourceType}${petId}-0`;
            }
            
            const spriteFrame = spriteAtlas.getSpriteFrame(frameName);
            
            if (spriteFrame) {
                this.lruCache.set(cacheKey, spriteFrame, 0.1); // 宠物资源占用较少内存
                console.log(`✅ 宠物资源加载成功: ${frameName} from ${atlas}`);
                return spriteFrame;
            } else {
                throw new Error(`宠物资源帧未找到: ${frameName} in ${atlas}`);
            }
        } catch (error) {
            console.error(`❌ 宠物资源加载失败 (ID: ${petId}, Type: ${resourceType}):`, error);
            return await this.getDefaultResource(resourceType);
        }
    }

    /**
     * 加载宠物头像（从对应图集中加载）
     */
    public async loadPetHead(petId: number): Promise<SpriteFrame> {
        return this.loadPetResource(petId, 't');
    }

    /**
     * 加载宠物攻击资源
     */
    public async loadPetAttack(petId: number, attackType: 'g' | 'S' = 'g'): Promise<SpriteFrame> {
        return this.loadPetResource(petId, attackType);
    }

    /**
     * 加载宠物动态资源
     */
    public async loadPetDynamic(petId: number): Promise<SpriteFrame> {
        return this.loadPetResource(petId, 'z');
    }

    /**
     * 加载宠物其他头像类型 (q, k)
     */
    public async loadPetHeadType(petId: number, headType: 'q' | 'k' | 't'): Promise<SpriteFrame> {
        return this.loadPetResource(petId, headType);
    }

    /**
     * 通用怪物资源加载方法
     * 支持不同类型的怪物资源：g、S(攻击)、z(动态)、q、k、t(头像)
     */
    public async loadMonsterResource(monsterId: number, resourceType: string): Promise<SpriteFrame> {
        const { atlas, enabled } = this.getMonsterGroup(monsterId);
        const cacheKey = `monster_${monsterId}_${resourceType}`;
        
        // 检查图集是否启用
        if (!enabled) {
            console.warn(`⚠️ 怪物${monsterId}的图集${atlas}已被禁用，使用默认资源`);
            return await this.getDefaultResource(resourceType);
        }
        
        // 检查LRU缓存
        const cached = this.lruCache.get(cacheKey);
        if (cached) {
            return cached;
        }

        try {
            const spriteAtlas = await this.ensureAtlasLoaded(atlas);
            
            // 🔧 修复：根据资源类型使用不同的帧名格式
            let frameName: string;
            if (resourceType === 't' || resourceType === 'k' || resourceType === 'q') {
                // 头像类型：单张图片格式 t3, k3, q3
                frameName = `${resourceType}${monsterId}`;
            } else {
                // 动作类型：序列格式 z3-0, g3-0, S3-0 (默认取第一帧)
                frameName = `${resourceType}${monsterId}-0`;
            }
            
            const spriteFrame = spriteAtlas.getSpriteFrame(frameName);
            
            if (spriteFrame) {
                this.lruCache.set(cacheKey, spriteFrame, 0.1); // 怪物资源占用较少内存
                console.log(`✅ 怪物资源加载成功: ${frameName} from ${atlas}`);
                return spriteFrame;
            } else {
                throw new Error(`怪物资源帧未找到: ${frameName} in ${atlas}`);
            }
        } catch (error) {
            console.error(`❌ 怪物资源加载失败 (ID: ${monsterId}, Type: ${resourceType}):`, error);
            return await this.getDefaultResource(resourceType);
        }
    }

    /**
     * 加载怪物头像（从对应图集中加载）
     */
    public async loadMonsterHead(monsterId: number): Promise<SpriteFrame> {
        return this.loadMonsterResource(monsterId, 't');
    }

    /**
     * 加载怪物攻击资源
     */
    public async loadMonsterAttack(monsterId: number, attackType: 'g' | 'S' = 'g'): Promise<SpriteFrame> {
        return this.loadMonsterResource(monsterId, attackType);
    }

    /**
     * 加载怪物动态资源
     */
    public async loadMonsterDynamic(monsterId: number): Promise<SpriteFrame> {
        return this.loadMonsterResource(monsterId, 'z');
    }

    /**
     * 加载怪物其他头像类型 (q, k)
     */
    public async loadMonsterHeadType(monsterId: number, headType: 'q' | 'k' | 't'): Promise<SpriteFrame> {
        return this.loadMonsterResource(monsterId, headType);
    }

    /**
     * 获取默认资源（根据资源类型）
     */
    private async getDefaultResource(resourceType: string): Promise<SpriteFrame> {
        const cacheKey = `default_${resourceType}`;
        const cached = this.lruCache.get(cacheKey);
        if (cached) {
            return cached;
        }

        try {
            // 根据资源类型选择不同的默认资源
            let defaultPath: string;
            switch (resourceType) {
                case 't':
                case 'q':
                case 'k':
                    defaultPath = 'head/t32'; // 默认头像
                    break;
                case 'g':
                case 'S':
                    defaultPath = 'pets/default_attack'; // 默认攻击资源
                    break;
                case 'z':
                    defaultPath = 'pets/default_dynamic'; // 默认动态资源
                    break;
                default:
                    defaultPath = 'head/t32'; // 其他情况使用默认头像
                    break;
            }
            
            const defaultResource = await this.loadSpriteFrame(defaultPath);
            this.lruCache.set(cacheKey, defaultResource, 0.1);
            return defaultResource;
        } catch (error) {
            console.error(`❌ 默认${resourceType}资源加载失败`);
            throw error;
        }
    }

    /**
     * 获取默认头像 (保持向后兼容)
     */
    private async getDefaultHead(): Promise<SpriteFrame> {
        return this.getDefaultResource('t');
    }

    /**
     * 检查指定图集组是否启用
     */
    public isAtlasGroupEnabled(category: 'pets' | 'monsters', groupName: string): boolean {
        const config = this.resourceConfig || this.resourceGroups;
        const group = config[category]?.[groupName];
        if (!group) {
            console.warn(`⚠️ 未找到${category}分组: ${groupName}`);
            return false;
        }
        return group.enabled !== false; // 默认为true
    }

    /**
     * 设置指定图集组的启用状态
     */
    public setAtlasGroupEnabled(category: 'pets' | 'monsters', groupName: string, enabled: boolean): void {
        const config = this.resourceConfig || this.resourceGroups;
        if (config[category]?.[groupName]) {
            config[category][groupName].enabled = enabled;
            console.log(`🔧 ${enabled ? '启用' : '禁用'}${category}分组: ${groupName}`);
        } else {
            console.warn(`⚠️ 未找到${category}分组: ${groupName}`);
        }
    }

    /**
     * 获取所有启用的图集列表
     */
    public getEnabledAtlases(): { pets: string[], monsters: string[] } {
        const config = this.resourceConfig || this.resourceGroups;
        const result = { pets: [], monsters: [] };

        // 收集启用的宠物图集
        for (const groupName in config.pets) {
            const group = config.pets[groupName];
            if (group.enabled !== false) {
                result.pets.push(group.atlas);
            }
        }

        // 收集启用的怪物图集
        for (const groupName in config.monsters) {
            const group = config.monsters[groupName];
            if (group.enabled !== false) {
                result.monsters.push(group.atlas);
            }
        }

        return result;
    }

    /**
     * 批量启用/禁用图集组
     */
    public batchSetAtlasEnabled(settings: { 
        pets?: { [groupName: string]: boolean }, 
        monsters?: { [groupName: string]: boolean } 
    }): void {
        console.log('🔧 批量设置图集启用状态...');
        
        if (settings.pets) {
            for (const groupName in settings.pets) {
                this.setAtlasGroupEnabled('pets', groupName, settings.pets[groupName]);
            }
        }

        if (settings.monsters) {
            for (const groupName in settings.monsters) {
                this.setAtlasGroupEnabled('monsters', groupName, settings.monsters[groupName]);
            }
        }

        console.log('✅ 批量设置完成');
        const enabledAtlases = this.getEnabledAtlases();
        console.log('📊 当前启用的图集:', enabledAtlases);
    }

    /**
     * 战斗资源预加载 - 只加载当前战斗需要的
     */
    public async preloadBattleResources(playerPets: number[], enemyIds: number[]): Promise<void> {
        console.log('⚔️ 开始预加载战斗资源...');
        
        const loadPromises: Promise<any>[] = [];

        // 加载玩家宠物资源
        for (const petId of playerPets) {
            const { atlas, enabled } = this.getPetGroup(petId);
            if (enabled) {
                loadPromises.push(this.ensureAtlasLoaded(atlas));
                console.log(`🐾 预加载宠物${petId}图集: ${atlas}`);
            } else {
                console.warn(`⚠️ 跳过禁用的宠物${petId}图集: ${atlas}`);
            }
        }

        // 加载敌方资源
        for (const enemyId of enemyIds) {
            const { atlas, enabled } = this.getMonsterGroup(enemyId);
            if (enabled) {
                loadPromises.push(this.ensureAtlasLoaded(atlas));
                console.log(`👹 预加载怪物${enemyId}图集: ${atlas}`);
            } else {
                console.warn(`⚠️ 跳过禁用的怪物${enemyId}图集: ${atlas}`);
            }
        }

        try {
            await Promise.all(loadPromises);
            console.log('✅ 战斗资源预加载完成');
        } catch (error) {
            console.error('❌ 战斗资源预加载失败:', error);
        }
    }

    /**
     * 确保图集已加载
     */
    private async ensureAtlasLoaded(atlasPath: string): Promise<SpriteAtlas> {
        const cached = this.lruCache.get(atlasPath);
        if (cached) {
            return cached;
        }

        const atlas = await this.loadAtlas(atlasPath);
        this.lruCache.set(atlasPath, atlas, 5); // 图集估计5MB
        return atlas;
    }

    /**
     * 通用资源加载方法（带LRU缓存）
     */
    public async loadAtlas(path: string): Promise<SpriteAtlas> {
        return this.loadResourceWithCache<SpriteAtlas>(path, SpriteAtlas, 5);
    }

    public async loadSpriteFrame(path: string): Promise<SpriteFrame> {
        return this.loadResourceWithCache<SpriteFrame>(path, SpriteFrame, 0.5);
    }

    public async loadPrefab(path: string): Promise<Prefab> {
        return this.loadResourceWithCache<Prefab>(path, Prefab, 2);
    }

    public async loadAudio(path: string): Promise<AudioClip> {
        return this.loadResourceWithCache<AudioClip>(path, AudioClip, 3);
    }

    public async loadJson(path: string): Promise<any> {
        const jsonAsset = await this.loadResourceWithCache<JsonAsset>(path, JsonAsset, 0.1);
        return jsonAsset.json;
    }

    /**
     * 带缓存的资源加载核心方法
     */
    private async loadResourceWithCache<T>(
        path: string, 
        type: typeof Asset, 
        estimatedSizeMB: number
    ): Promise<T> {
        // 检查LRU缓存
        const cached = this.lruCache.get(path);
        if (cached) {
            return cached;
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(path)) {
            return this.loadingPromises.get(path);
        }

        // 开始加载
        const promise = new Promise<T>((resolve, reject) => {
            resources.load(path, type, (err, asset) => {
                this.loadingPromises.delete(path);
                
                if (err) {
                    console.error(`❌ 资源加载失败: ${path}`, err);
                    reject(err);
                } else {
                    // 存入LRU缓存
                    this.lruCache.set(path, asset, estimatedSizeMB);
                    console.log(`✅ 资源加载成功: ${path}`);
                    resolve(asset as T);
                }
            });
        });

        this.loadingPromises.set(path, promise);
        return promise;
    }

    /**
     * 批量加载目录资源
     */
    public async loadDir(path: string, type?: typeof Asset): Promise<any[]> {
        return new Promise((resolve, reject) => {
            resources.loadDir(path, type, (err, assets) => {
                if (err) {
                    console.error(`❌ 批量加载失败: ${path}`, err);
                    reject(err);
                } else {
                    // 将所有资源存入LRU缓存
                    assets.forEach((asset) => {
                        const assetPath = `${path}/${asset.name}`;
                        this.lruCache.set(assetPath, asset, 1); // 默认1MB估算
                    });
                    console.log(`✅ 批量加载成功: ${path}，共${assets.length}个资源`);
                    resolve(assets);
                }
            });
        });
    }

    /**
     * 内存压力检查和清理
     */
    public checkMemoryPressure(): void {
        const stats = this.lruCache.getStats();
        
        if (stats.memoryMB > stats.maxMemoryMB * 0.8) {
            console.warn(`⚠️ 内存使用率过高: ${stats.memoryMB.toFixed(1)}MB / ${stats.maxMemoryMB}MB`);
            
            // 强制清理到70%
            const targetMemory = stats.maxMemoryMB * 0.7;
            this.lruCache.forceCleanup(targetMemory);
        }
    }

    /**
     * 场景切换时的资源清理
     */
    public onSceneChange(newScene: string): void {
        console.log(`🔄 场景切换到: ${newScene}`);
        
        // 保留核心资源，清理场景特定资源
        const keepPatterns = ['core/', 'ui/common', 'config/'];
        const stats = this.lruCache.getStats();
        
        if (stats.size > 50) { // 如果缓存资源过多
            // 强制清理到合理水平
            this.lruCache.forceCleanup(stats.maxMemoryMB * 0.5);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public getCacheStats(): any {
        const stats = this.lruCache.getStats();
        const recentlyUsed = this.lruCache.getRecentlyUsed(5);
        
        return {
            ...stats,
            loadingCount: this.loadingPromises.size,
            isPreloading: this.isPreloading,
            recentlyUsed
        };
    }

    /**
     * 紧急内存清理
     */
    public emergencyCleanup(): void {
        console.log('🚨 执行紧急内存清理');
        this.lruCache.forceCleanup(50); // 保留最多50MB
    }

    /**
     * 销毁管理器
     */
    public destroy(): void {
        this.lruCache.clear();
        this.loadingPromises.clear();
        this.preloadQueue = [];
        console.log('🗑️ 大规模资源管理器已销毁');
    }
}