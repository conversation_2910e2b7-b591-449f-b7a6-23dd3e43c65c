﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///地图怪物表
    ///</summary>
    [SugarTable("map_monster")]
    public partial class map_monster
    {
           public map_monster(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:地图ID（关联map_config.map_id）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int map_id {get;set;}

           /// <summary>
           /// Desc:怪物序号
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? monster_id {get;set;}

           /// <summary>
           /// Desc:怪物名字
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? monster_name {get;set;}

           /// <summary>
           /// Desc:怪物成长
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? growth {get;set;}

           /// <summary>
           /// Desc:怪物五行
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? element {get;set;}

           /// <summary>
           /// Desc:最大等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? max_level {get;set;}

           /// <summary>
           /// Desc:最小等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? min_level {get;set;}

           /// <summary>
           /// Desc:最大掉落
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? max_drop {get;set;}

           /// <summary>
           /// Desc:经验值
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? exp {get;set;}

           /// <summary>
           /// Desc:生命
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? hp {get;set;}

           /// <summary>
           /// Desc:魔法
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? mp {get;set;}

           /// <summary>
           /// Desc:最大生命
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? max_hp {get;set;}

           /// <summary>
           /// Desc:最大魔法
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? max_mp {get;set;}

           /// <summary>
           /// Desc:攻击
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? atk {get;set;}

           /// <summary>
           /// Desc:防御
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? def {get;set;}

           /// <summary>
           /// Desc:闪避
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? dodge {get;set;}

           /// <summary>
           /// Desc:速度
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? spd {get;set;}

           /// <summary>
           /// Desc:命中
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public long? hit {get;set;}

           /// <summary>
           /// Desc:加深伤害
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? deepen {get;set;}

           /// <summary>
           /// Desc:抵消伤害
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? offset {get;set;}

           /// <summary>
           /// Desc:吸血比例
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vamp {get;set;}

           /// <summary>
           /// Desc:吸魔比例
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vamp_mp {get;set;}

           /// <summary>
           /// Desc:技能列表(逗号分隔)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? skill_list {get;set;}

           /// <summary>
           /// Desc:掉落道具配置
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? drop_items {get;set;}

    }
}
