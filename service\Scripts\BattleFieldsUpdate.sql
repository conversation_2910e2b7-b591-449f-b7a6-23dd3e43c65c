-- 战斗模块字段补充脚本
-- 执行日期: 2025-01-19
-- 说明: 为现有表结构添加战斗必需的字段

-- =====================================================
-- 1. 为 map_monster 表添加战斗相关字段
-- =====================================================

-- 添加命中字段
ALTER TABLE map_monster ADD COLUMN hit INTEGER DEFAULT 0;

-- 添加加深伤害字段
ALTER TABLE map_monster ADD COLUMN deepen REAL DEFAULT 0.0;

-- 添加抵消伤害字段
ALTER TABLE map_monster ADD COLUMN offset REAL DEFAULT 0.0;

-- 添加吸血比例字段
ALTER TABLE map_monster ADD COLUMN vamp REAL DEFAULT 0.0;

-- 添加吸魔比例字段
ALTER TABLE map_monster ADD COLUMN vamp_mp REAL DEFAULT 0.0;

-- 添加技能列表字段
ALTER TABLE map_monster ADD COLUMN skill_list TEXT;

-- 添加掉落道具配置字段
ALTER TABLE map_monster ADD COLUMN drop_items TEXT;

-- =====================================================
-- 2. 为 user_pet 表添加战斗相关字段
-- =====================================================

-- 添加命中字段
ALTER TABLE user_pet ADD COLUMN hit INTEGER DEFAULT 0;

-- 添加加深伤害字段
ALTER TABLE user_pet ADD COLUMN deepen REAL DEFAULT 0.0;

-- 添加抵消伤害字段
ALTER TABLE user_pet ADD COLUMN offset REAL DEFAULT 0.0;

-- 添加吸血比例字段
ALTER TABLE user_pet ADD COLUMN vamp REAL DEFAULT 0.0;

-- 添加吸魔比例字段
ALTER TABLE user_pet ADD COLUMN vamp_mp REAL DEFAULT 0.0;

-- 添加自定义宠物名称字段
ALTER TABLE user_pet ADD COLUMN custom_name TEXT;

-- 添加法宝状态字段
ALTER TABLE user_pet ADD COLUMN talisman_state TEXT;

-- =====================================================
-- 3. 创建战斗记录表
-- =====================================================

CREATE TABLE IF NOT EXISTS battle_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    pet_id INTEGER NOT NULL,
    map_monster_id INTEGER NOT NULL,
    map_id INTEGER NOT NULL,
    battle_result TEXT NOT NULL CHECK (battle_result IN ('WIN', 'LOSE', 'ESCAPE')),
    rounds INTEGER DEFAULT 0,
    damage_dealt INTEGER DEFAULT 0,
    damage_received INTEGER DEFAULT 0,
    experience_gained INTEGER DEFAULT 0,
    money_gained INTEGER DEFAULT 0,
    yuanbao_gained INTEGER DEFAULT 0,
    items_gained TEXT,
    battle_duration INTEGER DEFAULT 0,
    damage_amplified INTEGER DEFAULT 0,
    damage_reduced INTEGER DEFAULT 0,
    hp_healed INTEGER DEFAULT 0,
    mp_healed INTEGER DEFAULT 0,
    is_first_strike INTEGER DEFAULT 0,
    anti_cheat_score REAL DEFAULT 0.0,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user(id),
    FOREIGN KEY (pet_id) REFERENCES user_pet(id),
    FOREIGN KEY (map_monster_id) REFERENCES map_monster(id)
);

-- 为战斗记录表创建索引
CREATE INDEX IF NOT EXISTS idx_battle_records_user_id ON battle_records(user_id);
CREATE INDEX IF NOT EXISTS idx_battle_records_pet_id ON battle_records(pet_id);
CREATE INDEX IF NOT EXISTS idx_battle_records_map_monster_id ON battle_records(map_monster_id);
CREATE INDEX IF NOT EXISTS idx_battle_records_map_id ON battle_records(map_id);
CREATE INDEX IF NOT EXISTS idx_battle_records_create_time ON battle_records(create_time);

-- =====================================================
-- 4. 创建反作弊日志表
-- =====================================================

CREATE TABLE IF NOT EXISTS anti_cheat_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    cheat_type TEXT NOT NULL CHECK (cheat_type IN ('SPEED', 'SCRIPT', 'ABNORMAL', 'TIMING')),
    detection_data TEXT,
    risk_score REAL DEFAULT 0.0,
    action_taken TEXT DEFAULT 'NONE' CHECK (action_taken IN ('NONE', 'WARNING', 'TEMP_BAN', 'PERMANENT_BAN')),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user(id)
);

-- 为反作弊日志表创建索引
CREATE INDEX IF NOT EXISTS idx_anti_cheat_logs_user_id ON anti_cheat_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_anti_cheat_logs_cheat_type ON anti_cheat_logs(cheat_type);
CREATE INDEX IF NOT EXISTS idx_anti_cheat_logs_create_time ON anti_cheat_logs(create_time);

-- =====================================================
-- 5. 创建战斗配置表
-- =====================================================

CREATE TABLE IF NOT EXISTS battle_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type TEXT DEFAULT 'STRING' CHECK (config_type IN ('STRING', 'INTEGER', 'REAL', 'BOOLEAN', 'JSON')),
    description TEXT,
    is_enabled INTEGER DEFAULT 1,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 为战斗配置表创建索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_battle_config_key ON battle_config(config_key);

-- =====================================================
-- 6. 创建自动战斗状态表
-- =====================================================

CREATE TABLE IF NOT EXISTS auto_battle_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    battle_type TEXT NOT NULL CHECK (battle_type IN ('HELL', 'TOWER', 'DUNGEON', 'MAP')),
    target_floor INTEGER DEFAULT 0,
    current_floor INTEGER DEFAULT 0,
    is_active INTEGER DEFAULT 0,
    start_time DATETIME,
    end_time DATETIME,
    battles_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user(id)
);

-- 为自动战斗状态表创建索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_auto_battle_user_type ON auto_battle_status(user_id, battle_type);
CREATE INDEX IF NOT EXISTS idx_auto_battle_user_id ON auto_battle_status(user_id);

-- =====================================================
-- 7. 插入战斗基础配置数据
-- =====================================================

INSERT OR IGNORE INTO battle_config (config_key, config_value, config_type, description) VALUES
('battle.speed_check.enabled', 'true', 'BOOLEAN', '是否启用变速检测'),
('battle.speed_check.min_interval', '2500', 'INTEGER', '最小战斗间隔(毫秒)'),
('battle.speed_check.avg_threshold', '820', 'INTEGER', '平均时间阈值(毫秒)'),
('battle.script_check.enabled', 'true', 'BOOLEAN', '是否启用脚本检测'),
('battle.script_check.repeat_limit', '6', 'INTEGER', '重复怪物检测阈值'),
('battle.attribute_check.enabled', 'true', 'BOOLEAN', '是否启用属性异常检测'),
('battle.attribute_check.max_value', '99999999999', 'INTEGER', '属性最大值限制'),
('battle.growth_check.max_value', '20000000', 'INTEGER', '成长值上限'),
('battle.damage.random_min', '-0.05', 'REAL', '伤害随机下限'),
('battle.damage.random_max', '0.10', 'REAL', '伤害随机上限'),
('battle.vip.drop_bonus.enabled', 'true', 'BOOLEAN', '是否启用VIP掉落加成'),
('battle.hell.blood_reduction', '0.5', 'REAL', '地狱模式吸血减半'),
('battle.tower.offset_reduction', '0.8', 'REAL', '通天塔抵消减少');

-- =====================================================
-- 8. 验证表结构
-- =====================================================

-- 查看 map_monster 表结构
-- PRAGMA table_info(map_monster);

-- 查看 user_pet 表结构  
-- PRAGMA table_info(user_pet);

-- 查看新创建的表
-- SELECT name FROM Mysql_master WHERE type='table' AND name LIKE '%battle%' OR name LIKE '%anti_cheat%' OR name LIKE '%auto_battle%';

-- =====================================================
-- 脚本执行完成
-- =====================================================
