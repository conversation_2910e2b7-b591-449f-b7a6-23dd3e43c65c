using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Interface;
using WebApplication_HM.Utils;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 实时系统控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]/[action]")]
    public class RealTimeController : ControllerBase
    {
        private readonly IRealTimeService _realTimeService;

        public RealTimeController(IRealTimeService realTimeService)
        {
            _realTimeService = realTimeService;
        }

        /// <summary>
        /// 获取在线玩家列表
        /// </summary>
        /// <returns>在线玩家信息</returns>
        [HttpGet]
        public ActionResult<List<PlayerStatusDTO>> GetOnlinePlayers()
        {
            try
            {
                var players = _realTimeService.GetOnlinePlayers();
                return Ok(players);
            }
            catch (Exception ex)
            {
                return BadRequest($"获取在线玩家失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取在线玩家数量
        /// </summary>
        /// <returns>在线玩家数量</returns>
        [HttpGet]
        public ActionResult<int> GetOnlinePlayerCount()
        {
            try
            {
                var count = _realTimeService.GetOnlinePlayerCount();
                return Ok(count);
            }
            catch (Exception ex)
            {
                return BadRequest($"获取在线玩家数量失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送系统公告
        /// </summary>
        /// <param name="request">系统公告请求</param>
        /// <returns>发送结果</returns>
        [HttpPost]
        public ActionResult<bool> SendSystemNotice([FromBody] SystemNoticeRequestDTO request)
        {
            try
            {
                var notice = new SystemNoticeDTO
                {
                    NoticeId = Guid.NewGuid().ToString(),
                    Title = request.Title,
                    Content = request.Content,
                    NoticeType = request.NoticeType,
                    Priority = request.Priority,
                    ShowPopup = request.ShowPopup,
                    Sender = request.Sender,
                    TargetPlayerIds = request.TargetPlayerIds,
                    ExpireTime = request.ExpireTime
                };

                if (request.TargetPlayerIds?.Any() == true)
                {
                    // 发送给指定玩家
                    foreach (var playerId in request.TargetPlayerIds)
                    {
                        _realTimeService.SendSystemNotice(playerId, notice);
                    }
                }
                else
                {
                    // 广播给所有玩家
                    _realTimeService.BroadcastSystemNotice(notice);
                }

                return Ok(true);
            }
            catch (Exception ex)
            {
                return BadRequest($"发送系统公告失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送游戏事件通知
        /// </summary>
        /// <param name="request">游戏事件请求</param>
        /// <returns>发送结果</returns>
        [HttpPost]
        public ActionResult<bool> SendGameEvent([FromBody] GameEventRequestDTO request)
        {
            try
            {
                var eventNotice = new GameEventNoticeDTO
                {
                    EventType = request.EventType,
                    PlayerId = request.PlayerId,
                    PlayerName = request.PlayerName,
                    Description = request.Description,
                    Broadcast = request.Broadcast
                };

                if (request.Broadcast)
                {
                    _realTimeService.BroadcastGameEvent(eventNotice);
                }
                else
                {
                    _realTimeService.SendGameEvent(request.PlayerId, eventNotice);
                }

                return Ok(true);
            }
            catch (Exception ex)
            {
                return BadRequest($"发送游戏事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新玩家状态
        /// </summary>
        /// <param name="request">玩家状态更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPost]
        public ActionResult<bool> UpdatePlayerStatus([FromBody] PlayerStatusUpdateRequestDTO request)
        {
            try
            {
                _realTimeService.UpdatePlayerStatus(request.PlayerId, request.Status);
                return Ok(true);
            }
            catch (Exception ex)
            {
                return BadRequest($"更新玩家状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送心跳检测
        /// </summary>
        /// <param name="playerId">玩家ID</param>
        /// <returns>心跳结果</returns>
        [HttpPost]
        public ActionResult<bool> SendHeartbeat(int playerId)
        {
            try
            {
                _realTimeService.SendHeartbeat(playerId);
                return Ok(true);
            }
            catch (Exception ex)
            {
                return BadRequest($"发送心跳失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 广播消息
        /// </summary>
        /// <param name="request">广播消息请求</param>
        /// <returns>广播结果</returns>
        [HttpPost]
        public ActionResult<bool> BroadcastMessage([FromBody] BroadcastMessageRequestDTO request)
        {
            try
            {
                _realTimeService.BroadcastMessage(request.Message, request.MessageType);
                return Ok(true);
            }
            catch (Exception ex)
            {
                return BadRequest($"广播消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送私聊消息
        /// </summary>
        /// <param name="request">私聊消息请求</param>
        /// <returns>发送结果</returns>
        [HttpPost]
        public ActionResult<bool> SendPrivateMessage([FromBody] PrivateMessageRequestDTO request)
        {
            try
            {
                _realTimeService.SendMessage(request.TargetPlayerId, request.Message);
                return Ok(true);
            }
            catch (Exception ex)
            {
                return BadRequest($"发送私聊消息失败: {ex.Message}");
            }
        }
    }
} 