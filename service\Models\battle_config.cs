﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///战斗配置表
    ///</summary>
    [SugarTable("battle_config")]
    public partial class battle_config
    {
           public battle_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:配置键
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string config_key {get;set;}

           /// <summary>
           /// Desc:配置值
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string config_value {get;set;}

           /// <summary>
           /// Desc:配置类型
           /// Default:STRING
           /// Nullable:True
           /// </summary>           
           public string? config_type {get;set;}

           /// <summary>
           /// Desc:配置描述
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? description {get;set;}

           /// <summary>
           /// Desc:是否启用
           /// Default:true
           /// Nullable:True
           /// </summary>           
           public bool? is_enabled {get;set;}

           /// <summary>
           /// Desc:更新时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? update_time {get;set;}

    }
}
