using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 原项目战斗算法适配器
    /// 完整迁移WindowsFormsApplication7的战斗计算逻辑
    /// </summary>
    public static class OriginalBattleAdapter
    {
        /// <summary>
        /// 计算玩家伤害 (基于原项目Fight.cs第617-653行)
        /// </summary>
        /// <param name="playerAttr">玩家属性</param>
        /// <param name="monsterAttr">怪物属性</param>
        /// <param name="skillMultiplier">技能倍数</param>
        /// <param name="talismanState">护符状态</param>
        /// <returns>伤害值</returns>
        public static long CalculatePlayerDamage(
            AttributeResultDTO playerAttr, 
            AttributeResultDTO monsterAttr, 
            double skillMultiplier = 1.0,
            string talismanState = "")
        {
            long damage = 0;
            double outputBonus = Convert.ToDouble(playerAttr.Hit) - Convert.ToDouble(monsterAttr.Dodge);
            
            // 处理护符状态 - 会心一击
            string tempAttack = playerAttr.Atk.ToString();
            if (talismanState.Contains("会心一击"))
            {
                string[] cfg = talismanState.Split('|');
                if (new Random().Next(0, 100) < Convert.ToInt16(cfg[1]))
                {
                    tempAttack = Convert.ToInt64(Convert.ToInt64(tempAttack) * (1 + Convert.ToDouble(cfg[2]))).ToString();
                }
            }
            
            if (outputBonus > 0)
            {
                // 正输出加成计算
                outputBonus = outputBonus / Convert.ToDouble(playerAttr.Hit);
                outputBonus = outputBonus / 2;
                outputBonus = Math.Pow(outputBonus, 2);
                damage = (long)(Convert.ToInt64(tempAttack) * skillMultiplier * (1 + outputBonus));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / skillMultiplier / (1 + outputBonus));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    damage = long.MaxValue; // 标记为异常
                }
                else
                {
                    // 添加随机波动 (-5% ~ +10%)
                    damage = (long)(damage * (1.0 + new Random().Next(-5000, 10001) / 100000.0));
                }
            }
            else
            {
                // 负输出加成计算
                damage = (long)((Convert.ToInt64(tempAttack) - Convert.ToInt64(monsterAttr.Def)) *
                         (1 - (Convert.ToInt64(monsterAttr.Dodge) - Convert.ToInt64(playerAttr.Hit)) / Convert.ToDouble(monsterAttr.Dodge)));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / (1 - (Convert.ToInt64(monsterAttr.Dodge) - Convert.ToInt64(playerAttr.Hit)) / Convert.ToDouble(monsterAttr.Dodge)) +
                              Convert.ToInt64(monsterAttr.Def));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    // 检查成长差距
                    if (Convert.ToInt64(monsterAttr.Growth ?? "0") - Convert.ToInt64(playerAttr.Growth ?? "0") >= 5000)
                    {
                        damage = 1;
                    }
                    else
                    {
                        damage = long.MaxValue;
                    }
                }
            }
            
            return damage;
        }
        
        /// <summary>
        /// 计算怪物伤害 (基于原项目Fight.cs第785-811行)
        /// </summary>
        /// <param name="monsterAttr">怪物属性</param>
        /// <param name="playerAttr">玩家属性</param>
        /// <param name="talismanState">护符状态</param>
        /// <returns>伤害值</returns>
        public static long CalculateMonsterDamage(
            AttributeResultDTO monsterAttr, 
            AttributeResultDTO playerAttr,
            string talismanState = "")
        {
            long damage = 0;
            double outputBonus = Convert.ToDouble(monsterAttr.Hit) - Convert.ToDouble(playerAttr.Dodge);
            
            // 处理护符状态 - 伤害抵御
            string tempAttack = monsterAttr.Atk.ToString();
            if (talismanState.Contains("伤害抵御"))
            {
                string[] cfg = talismanState.Split('|');
                if (new Random().Next(0, 100) < Convert.ToInt16(cfg[1]))
                {
                    tempAttack = Convert.ToInt64(Convert.ToInt64(tempAttack) * (1 - Convert.ToDouble(cfg[2]))).ToString();
                }
            }
            
            if (outputBonus > 0)
            {
                // 正输出加成计算 (怪物使用1.5次方)
                outputBonus = outputBonus / Convert.ToDouble(monsterAttr.Hit);
                outputBonus = outputBonus / 2;
                outputBonus = Math.Pow(outputBonus, 1.5);
                damage = (long)(Convert.ToInt64(tempAttack) * (1 + outputBonus));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / (1 + outputBonus));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    damage = long.MaxValue;
                }
            }
            else
            {
                // 负输出加成计算
                damage = (long)((Convert.ToInt64(tempAttack) - Convert.ToInt64(playerAttr.Def)) *
                         (1 - (Convert.ToInt64(playerAttr.Dodge) - Convert.ToInt64(monsterAttr.Hit)) / Convert.ToDouble(playerAttr.Dodge)));
                
                // 反作弊验证
                long verification = damage;
                long restored = (long)(verification / (1 - (Convert.ToInt64(playerAttr.Dodge) - Convert.ToInt64(monsterAttr.Hit)) / Convert.ToDouble(playerAttr.Dodge)) +
                              Convert.ToInt64(playerAttr.Def));
                if (Math.Abs(Convert.ToDouble(tempAttack) - restored) >= 10)
                {
                    damage = long.MaxValue;
                }
            }
            
            return damage;
        }
        
        /// <summary>
        /// 计算加深伤害 (基于原项目Fight.cs第658-673行)
        /// </summary>
        /// <param name="baseDamage">基础伤害</param>
        /// <param name="amplifyRate">加深率</param>
        /// <returns>加深伤害值</returns>
        public static long CalculateAmplifiedDamage(long baseDamage, double amplifyRate)
        {
            if (amplifyRate <= 0) return -1;
            
            if (baseDamage != long.MaxValue)
            {
                long amplified = (long)(baseDamage * amplifyRate);
                return amplified < 0 && amplified != -2 ? 1 : amplified;
            }
            else if (amplifyRate > 0)
            {
                return -2; // 异常标记
            }
            
            return -1;
        }
        
        /// <summary>
        /// 计算抵消伤害 (基于原项目Fight.cs第820-842行)
        /// </summary>
        /// <param name="incomingDamage">受到的伤害</param>
        /// <param name="reductionRate">抵消率</param>
        /// <param name="isTT">是否通天塔</param>
        /// <param name="mapId">地图ID</param>
        /// <returns>抵消的伤害值</returns>
        public static long CalculateReducedDamage(long incomingDamage, double reductionRate, bool isTT = false, string mapId = "")
        {
            if (reductionRate <= 0) return -1;
            
            double finalReduction = reductionRate;
            
            // 分段计算公式
            if (reductionRate > 0 && reductionRate <= 0.3)
            {
                finalReduction = reductionRate;
            }
            else if (reductionRate > 0.3 && reductionRate <= 1)
            {
                finalReduction = 2.0 * reductionRate / 7.0 + 0.21428571;
                if (isTT) finalReduction = 2.0 * reductionRate / 7.0 + 0.16428571;
            }
            else
            {
                finalReduction = 0.5;
                if (isTT) finalReduction = 0.5 * 0.9;
            }
            
            // 地图特殊限制
            if (mapId == "23") // 冰岛
            {
                finalReduction *= 0.3;
            }
            
            long reduced = (long)(incomingDamage * finalReduction);
            return Math.Max(0, reduced);
        }
        
        /// <summary>
        /// 计算吸血回复 (基于原项目Fight.cs第714-762行)
        /// </summary>
        /// <param name="damage">造成的伤害</param>
        /// <param name="lifeStealRate">吸血率</param>
        /// <param name="isTT">是否通天塔</param>
        /// <param name="mapId">地图ID</param>
        /// <returns>吸血回复值</returns>
        public static long CalculateLifeSteal(long damage, double lifeStealRate, bool isTT = false, string mapId = "")
        {
            if (lifeStealRate <= 0) return 0;
            
            double finalLifeSteal = lifeStealRate;
            
            // 分段计算公式
            if (lifeStealRate > 0 && lifeStealRate <= 0.4)
            {
                finalLifeSteal = lifeStealRate;
                if (isTT) finalLifeSteal *= 0.50;
            }
            else if (lifeStealRate > 0.4 && lifeStealRate <= 1.6)
            {
                if (isTT)
                {
                    finalLifeSteal *= 0.50;
                }
                else
                {
                    finalLifeSteal = 0.5 * lifeStealRate + 0.2;
                }
            }
            else
            {
                finalLifeSteal = 1.0;
                if (isTT) finalLifeSteal *= 0.50;
            }
            
            // 地图特殊限制
            if (mapId == "23") // 冰岛
            {
                finalLifeSteal *= 0.2;
            }
            else if (mapId == "201901") // 赫拉神殿
            {
                return 0; // 完全禁止吸血
            }
            
            long lifeSteal = (long)(damage * finalLifeSteal);
            
            // 异常检测
            if (lifeSteal <= -10000)
            {
                return -2;
            }
            
            return lifeSteal;
        }
        
        /// <summary>
        /// 计算吸魔回复 (基于原项目Fight.cs第764-772行)
        /// </summary>
        /// <param name="damage">造成的伤害</param>
        /// <param name="manaStealRate">吸魔率</param>
        /// <returns>吸魔回复值</returns>
        public static long CalculateManaSteal(long damage, double manaStealRate)
        {
            if (manaStealRate <= 0) return 0;
            
            return (long)(damage * manaStealRate);
        }
    }
}
