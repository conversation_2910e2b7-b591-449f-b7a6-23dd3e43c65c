using Microsoft.Extensions.Logging;
using SqlSugar;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.DTOs;
using WebApplication_HM.Interface;
using WebApplication_HM.Services;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 套装效果计算服务
    /// 基于老项目PetCalc.cs中的套装计算逻辑完整迁移
    /// </summary>
    public class SuitEffectService
    {
        private readonly ILogger<SuitEffectService> _logger;
        private readonly DbContext _dbContext;
        private readonly IEquipmentRepository _equipmentRepository;
        private readonly ISuitService _suitService;

        // 套装属性类型列表 (基于老项目)
        private static readonly string[] SUIT_ATTRIBUTE_TYPES = 
        {
            "攻击", "命中", "防御", "速度", "闪避", "生命", "魔法", 
            "加深", "抵消", "吸血", "吸魔"
        };

        public SuitEffectService(
            ILogger<SuitEffectService> logger,
            DbContext dbContext,
            IEquipmentRepository equipmentRepository,
            ISuitService suitService)
        {
            _logger = logger;
            _dbContext = dbContext;
            _equipmentRepository = equipmentRepository;
            _suitService = suitService;
        }

        /// <summary>
        /// 计算宠物套装效果
        /// 基于老项目PetCalc.cs中的套装计算逻辑完整迁移
        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>套装属性加成</returns>
        public async Task<SuitEffectResult> CalculatePetSuitEffectsAsync(int petId)
        {
            try
            {
                var result = new SuitEffectResult();

                // 1. 获取宠物装备信息
                var equipments = await GetPetEquipmentsAsync(petId);
                if (!equipments.Any())
                {
                    return result;
                }

                // 2. 统计套装装备数量 (基于老项目逻辑)
                var suitCounts = new Dictionary<string, int>();
                var suitEquipments = new Dictionary<string, List<user_equipment>>();

                foreach (var equipment in equipments)
                {
                    var suitId = DetermineSuitIdByEquipId(equipment.equip_id);
                    if (!string.IsNullOrEmpty(suitId))
                    {
                        if (!suitCounts.ContainsKey(suitId))
                        {
                            suitCounts[suitId] = 0;
                            suitEquipments[suitId] = new List<user_equipment>();
                        }
                        suitCounts[suitId]++;
                        suitEquipments[suitId].Add(equipment);
                    }
                }

                // 3. 计算每个套装的激活效果 (基于老项目逻辑)
                var calculatedSuits = new List<string>(); // 已计算的套装，避免重复计算
                var totalAttributes = InitializeAttributeDictionary();

                foreach (var equipment in equipments)
                {
                    var suitId = DetermineSuitIdByEquipId(equipment.equip_id);
                    if (string.IsNullOrEmpty(suitId) || calculatedSuits.Contains(suitId))
                        continue;

                    calculatedSuits.Add(suitId);

                    // 获取套装配置
                    var suitConfig = await _suitService.GetSuitByIdAsync(suitId);
                    if (suitConfig == null)
                        continue;

                    var equippedCount = suitCounts[suitId];
                    var suitActivation = new SuitActivationInfo
                    {
                        SuitId = suitId,
                        SuitName = suitConfig.SuitName,
                        EquippedCount = equippedCount,
                        TotalCount = suitConfig.EquipmentList.Count,
                        ActivatedAttributes = new List<SuitAttributeInfo>()
                    };

                    // 4. 计算激活的套装属性 (基于老项目逻辑)
                    for (int i = 0; i < equippedCount - 1 && i < suitConfig.Attributes.Count; i++)
                    {
                        var suitAttr = suitConfig.Attributes[i];
                        var attrInfo = new SuitAttributeInfo
                        {
                            AttributeType = suitAttr.AttributeType,
                            AttributeValue = suitAttr.AttributeValue,
                            IsPercentage = suitAttr.IsPercentage,
                            RequiredPieces = i + 2 // 套装从2件开始激活
                        };

                        suitActivation.ActivatedAttributes.Add(attrInfo);

                        // 累加到总属性中
                        if (totalAttributes.ContainsKey(suitAttr.AttributeType))
                        {
                            var value = suitAttr.IsPercentage
                                ? double.Parse(suitAttr.AttributeValue)
                                : double.Parse(suitAttr.AttributeValue);
                            totalAttributes[suitAttr.AttributeType] += value;
                        }
                    }

                    result.SuitActivations.Add(suitActivation);
                }

                result.TotalAttributes = totalAttributes;
                result.Success = true;

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算宠物套装效果失败，宠物ID: {PetId}", petId);
                return new SuitEffectResult { Success = false, ErrorMessage = "计算套装效果失败" };
            }
        }

        /// <summary>
        /// 获取套装显示信息
        /// 基于老项目DataProcess.cs中的套装显示逻辑
        /// </summary>
        /// <param name="equipId">装备ID</param>
        /// <param name="petId">宠物ID（可选）</param>
        /// <returns>套装显示HTML</returns>
        public async Task<string> GetSuitDisplayInfoAsync(string equipId, int? petId = null)
        {
            try
            {
                var suitId = DetermineSuitIdByEquipId(equipId);
                if (string.IsNullOrEmpty(suitId))
                    return string.Empty;

                var suitConfig = await _suitService.GetSuitByIdAsync(suitId);
                if (suitConfig == null)
                    return string.Empty;

                var html = "";
                int equippedCount = 0;

                // 如果指定了宠物ID，计算已装备的套装数量
                if (petId.HasValue)
                {
                    var equipments = await GetPetEquipmentsAsync(petId.Value);
                    equippedCount = equipments.Count(e => DetermineSuitIdByEquipId(e.equip_id) == suitId);
                }

                // 套装名称和进度 (基于老项目格式)
                html += $"<br/><span style='color:#FED625;'>{suitConfig.SuitName}({equippedCount + 1}/{suitConfig.EquipmentList.Count})</span>";

                // 套装属性列表 (基于老项目格式)
                for (int i = 0; i < suitConfig.Attributes.Count; i++)
                {
                    var attr = suitConfig.Attributes[i];
                    var displayValue = attr.AttributeValue;
                    var color = "#A8A7A4"; // 默认灰色（未激活）

                    // 百分比属性显示处理
                    if (attr.IsPercentage)
                    {
                        displayValue = (double.Parse(attr.AttributeValue) * 100) + "%";
                    }

                    // 已激活属性显示为绿色
                    if (i < equippedCount - 1)
                    {
                        color = "#68da72";
                    }

                    html += $"<br/><span style='color:{color}'>({i + 2})套装：+{displayValue}&nbsp{attr.AttributeType}</span>";
                }

                return html;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取套装显示信息失败，装备ID: {EquipId}", equipId);
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取宠物装备列表
        /// </summary>
        private async Task<List<user_equipment>> GetPetEquipmentsAsync(int petId)
        {
            return await _dbContext.Db.Queryable<user_equipment>()
                .Where(x => x.pet_id == petId && x.is_equipped == true)
                .ToListAsync();
        }

        /// <summary>
        /// 根据装备ID判断套装ID
        /// 基于老项目的套装判断逻辑
        /// </summary>
        private string? DetermineSuitIdByEquipId(string equipId)
        {
            // 基于装备ID前缀判断套装 (完全基于老项目逻辑)
            if (equipId.StartsWith("2017070101")) return "2017070101"; // 天魔套装
            if (equipId.StartsWith("2017063001")) return "2017063001"; // 自然套装
            if (equipId.StartsWith("20170612")) return "20170612";     // 黑白套装
            if (equipId.StartsWith("2016123001")) return "2016123001"; // 盛世套装
            if (equipId.StartsWith("20161230")) return "20161230";     // 创世套装
            if (equipId.StartsWith("2016123101")) return "2016123101"; // 七宗罪套装
            if (equipId.StartsWith("20170214")) return "20170214";     // 情人套装
            if (equipId.StartsWith("2017082201")) return "2017082201"; // EFS套装
            if (equipId.StartsWith("2018032001")) return "2018032001"; // 神圣套装
            if (equipId.StartsWith("2018060401")) return "2018060401"; // 神圣卡牌套装
            if (equipId.StartsWith("2018092901")) return "2018092901"; // 刀剑套装
            if (equipId.StartsWith("2019100101")) return "2019100101"; // 魔童套装
            if (equipId.StartsWith("2018102701")) return "2018102701"; // 沉睡套装
            if (equipId.StartsWith("2018102702")) return "2018102702"; // 沉睡卡牌套装
            if (equipId.StartsWith("2019020101")) return "2019020101"; // 佩奇套装
            if (equipId.StartsWith("2019060801")) return "2019060801"; // 通天卡牌套装
            if (equipId.StartsWith("2019051901")) return "2019051901"; // 通天套装
            if (equipId.StartsWith("2019061501")) return "2019061501"; // 次元卡牌套装
            if (equipId.StartsWith("2019061502")) return "2019061502"; // 次元套装
            if (equipId.StartsWith("2020092801")) return "2020092801"; // 圣战卡牌套装
            if (equipId.StartsWith("2020090801")) return "2020090801"; // 圣战卡牌套装
            if (equipId.StartsWith("2020092802")) return "2020092802"; // 圣战套装

            return null; // 非套装装备
        }

        /// <summary>
        /// 初始化属性字典
        /// </summary>
        private Dictionary<string, double> InitializeAttributeDictionary()
        {
            var result = new Dictionary<string, double>();
            foreach (var attrType in SUIT_ATTRIBUTE_TYPES)
            {
                result[attrType] = 0.0;
            }
            return result;
        }
    }

    /// <summary>
    /// 套装效果计算结果
    /// </summary>
    public class SuitEffectResult
    {
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public List<SuitActivationInfo> SuitActivations { get; set; } = new();
        public Dictionary<string, double> TotalAttributes { get; set; } = new();
    }

    /// <summary>
    /// 套装激活信息
    /// </summary>
    public class SuitActivationInfo
    {
        public string SuitId { get; set; } = string.Empty;
        public string SuitName { get; set; } = string.Empty;
        public int EquippedCount { get; set; }
        public int TotalCount { get; set; }
        public List<SuitAttributeInfo> ActivatedAttributes { get; set; } = new();
    }

    /// <summary>
    /// 套装属性信息
    /// </summary>
    public class SuitAttributeInfo
    {
        public string AttributeType { get; set; } = string.Empty;
        public string AttributeValue { get; set; } = string.Empty;
        public bool IsPercentage { get; set; }
        public int RequiredPieces { get; set; }
    }
}
