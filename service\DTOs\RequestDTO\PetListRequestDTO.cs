namespace WebApplication_HM.DTOs.RequestDTO
{
    /// <summary>
    /// 宠物列表查询请求DTO
    /// </summary>
    public class PetListRequestDTO
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 宠物状态过滤（可选）
        /// 可选值：牧场、携带、null（查询所有非丢弃状态）
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 是否只查询主战宠物（可选）
        /// </summary>
        public bool? OnlyMainPet { get; set; }
    }
} 