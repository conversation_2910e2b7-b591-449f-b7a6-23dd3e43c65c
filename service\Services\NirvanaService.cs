using Microsoft.Extensions.Logging;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Utils;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 转生服务
    /// </summary>
    public class NirvanaService : INirvanaService
    {
        private readonly ISqlSugarClient _db;
        private readonly INirvanaCalculationService _calculationService;
        private readonly INirvanaConfigService _configService;
        private readonly INirvanaAntiCheatService _antiCheatService;
        private readonly ILogger<NirvanaService> _logger;

        public NirvanaService(
            ISqlSugarClient db,
            INirvanaCalculationService calculationService,
            INirvanaConfigService configService,
            INirvanaAntiCheatService antiCheatService,
            ILogger<NirvanaService> logger)
        {
            _db = db;
            _calculationService = calculationService;
            _configService = configService;
            _antiCheatService = antiCheatService;
            _logger = logger;
        }

        /// <summary>
        /// 执行转生
        /// </summary>
        public async Task<ApiResult<NirvanaResultDto>> ExecuteNirvanaAsync(NirvanaRequestDto request)
        {
            try
            {
                _logger.LogInformation("开始执行转生 - 用户ID:{UserId}", request.UserId);

                // 1. 反作弊验证
                if (!await _antiCheatService.ValidateNirvanaRequestAsync(request))
                {
                    return ApiResult<NirvanaResultDto>.CreateError("转生请求验证失败");
                }

                // 2. 条件验证
                var (isValid, errors) = await _calculationService.ValidateNirvanaConditionsAsync(request);
                if (!isValid)
                {
                    return ApiResult<NirvanaResultDto>.CreateError(string.Join(", ", errors));
                }

                // 3. 获取相关数据
                var user = await _db.Queryable<user>().FirstAsync(u => u.id == request.UserId);
                var mainPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.MainPetId);
                var subPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.SubPetId);
                var nirvanaPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.NirvanaPetId);

                // 4. 获取转生配置
                var config = await _configService.FindMatchingConfigAsync(mainPet.pet_no, subPet.pet_no, nirvanaPet.pet_no);
                if (config == null)
                {
                    return ApiResult<NirvanaResultDto>.CreateError("未找到对应的转生配置");
                }

                // 5. 计算成功率和消耗
                var successRate = await _calculationService.CalculateSuccessRateAsync(request, config);
                var costGold = _calculationService.CalculateCostGold(config, user);
                var vipBonus = _calculationService.CalculateVipBonus(user);

                // 6. 验证资源
                if (!await _antiCheatService.ValidateResourcesAsync(request, costGold))
                {
                    return ApiResult<NirvanaResultDto>.CreateError("金币不足");
                }

                // 7. 开始事务执行转生
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    // 扣除金币
                    var currentGold = user.gold ?? 0;
                    var newGold = currentGold - costGold;
                    await _db.Updateable<user>()
                        .SetColumns(u => u.gold == newGold)
                        .SetColumns(u => u.nirvana_cd == DateTime.Now)
                        .Where(u => u.id == request.UserId)
                        .ExecuteCommandAsync();

                    // 判断是否成功
                    var random = new Random();
                    var isSuccess = random.NextDouble() <= (double)successRate;

                    NirvanaResultDto nirvanaResult;

                    if (isSuccess)
                    {
                        // 转生成功
                        nirvanaResult = await ExecuteSuccessfulNirvanaAsync(request, config, mainPet, subPet, nirvanaPet, user, vipBonus);
                    }
                    else
                    {
                        // 转生失败
                        nirvanaResult = await ExecuteFailedNirvanaAsync(request, mainPet, subPet, nirvanaPet);
                    }

                    // 记录转生日志
                    await RecordNirvanaLogAsync(request, config, mainPet, subPet, nirvanaPet, 
                        successRate, costGold, vipBonus, nirvanaResult);

                    // 记录反作弊信息
                    await _antiCheatService.RecordNirvanaAttemptAsync(request.UserId);

                    nirvanaResult.CostGold = costGold;
                    nirvanaResult.SuccessRate = successRate;
                    nirvanaResult.VipBonus = vipBonus - 1; // 转换为加成百分比

                    return nirvanaResult;
                });

                _logger.LogInformation("转生执行完成 - 用户ID:{UserId}, 结果:{IsSuccess}",
                    request.UserId, result.IsSuccess);

                return ApiResult<NirvanaResultDto>.CreateSuccess(result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行转生失败 - 用户ID:{UserId}", request.UserId);
                return ApiResult<NirvanaResultDto>.CreateError("转生执行失败");
            }
        }

        /// <summary>
        /// 执行成功的转生
        /// </summary>
        private async Task<NirvanaResultDto> ExecuteSuccessfulNirvanaAsync(
            NirvanaRequestDto request, PetNirvanaConfig config, 
            user_pet mainPet, user_pet subPet, user_pet nirvanaPet, 
            user user, decimal vipBonus)
        {
            // 计算获得成长
            var growthGain = await _calculationService.CalculateGrowthGainAsync(mainPet, subPet, 0, user);

            // 创建新宠物
            var newPet = new user_pet
            {
                user_id = request.UserId,
                pet_no = config.ResultPetNo,
                name = $"转生{GetPetName(config.ResultPetNo)}",
                level = 1,
                growth = (decimal)growthGain,
                atk = CalculateNewAttribute(mainPet.atk, subPet.atk, config.MainGrowthInherit, config.SubGrowthInherit),
                def = CalculateNewAttribute(mainPet.def, subPet.def, config.MainGrowthInherit, config.SubGrowthInherit),
                hp = CalculateNewAttribute(mainPet.hp, subPet.hp, config.MainGrowthInherit, config.SubGrowthInherit),
                mp = CalculateNewAttribute(mainPet.mp, subPet.mp, config.MainGrowthInherit, config.SubGrowthInherit),
                spd = CalculateNewAttribute(mainPet.spd, subPet.spd, config.MainGrowthInherit, config.SubGrowthInherit),
                exp = 0,
                status = "牧场",
                is_main = false,
                create_time = DateTime.Now,
                nirvana_count = (mainPet.nirvana_count ?? 0) + 1,
                parent_main_pet_id = mainPet.id,
                parent_sub_pet_id = subPet.id,
                original_pet_no = mainPet.original_pet_no ?? mainPet.pet_no
            };

            // 插入新宠物
            var insertedPet = await _db.Insertable(newPet).ExecuteReturnEntityAsync();

            // 删除原宠物
            await _db.Deleteable<user_pet>().Where(p => p.id == mainPet.id).ExecuteCommandAsync();
            await _db.Deleteable<user_pet>().Where(p => p.id == subPet.id).ExecuteCommandAsync();
            await _db.Deleteable<user_pet>().Where(p => p.id == nirvanaPet.id).ExecuteCommandAsync();

            return new NirvanaResultDto
            {
                IsSuccess = true,
                Message = "转生成功！",
                ResultGrowth = growthGain,
                ResultPetNo = config.ResultPetNo,
                ResultPet = insertedPet,
                NirvanaType = request.NirvanaType.ToString()
            };
        }

        /// <summary>
        /// 执行失败的转生
        /// </summary>
        private async Task<NirvanaResultDto> ExecuteFailedNirvanaAsync(
            NirvanaRequestDto request, user_pet mainPet, user_pet subPet, user_pet nirvanaPet)
        {
            // 转生失败，删除副宠和涅槃兽，保留主宠
            await _db.Deleteable<user_pet>().Where(p => p.id == subPet.id).ExecuteCommandAsync();
            await _db.Deleteable<user_pet>().Where(p => p.id == nirvanaPet.id).ExecuteCommandAsync();

            return new NirvanaResultDto
            {
                IsSuccess = false,
                Message = "转生失败，副宠和涅槃兽已消失",
                ResultGrowth = null,
                ResultPetNo = null,
                ResultPet = mainPet,
                NirvanaType = request.NirvanaType.ToString()
            };
        }

        /// <summary>
        /// 记录转生日志
        /// </summary>
        private async Task RecordNirvanaLogAsync(
            NirvanaRequestDto request, PetNirvanaConfig config,
            user_pet mainPet, user_pet subPet, user_pet nirvanaPet,
            decimal successRate, long costGold, decimal vipBonus, NirvanaResultDto result)
        {
            var log = new PetNirvanaLog
            {
                UserId = request.UserId,
                MainPetId = request.MainPetId,
                SubPetId = request.SubPetId,
                NirvanaPetId = request.NirvanaPetId,
                MainPetNo = mainPet.pet_no,
                SubPetNo = subPet.pet_no,
                NirvanaPetNo = nirvanaPet.pet_no,
                MainGrowth = Convert.ToDecimal(mainPet.growth),
                SubGrowth = Convert.ToDecimal(subPet.growth),
                MainLevel = Convert.ToInt32(mainPet.level),
                SubLevel = Convert.ToInt32(subPet.level),
                ResultPetNo = result.ResultPetNo,
                ResultGrowth = result.ResultGrowth,
                SuccessRate = successRate * 100, // 转换为百分比
                UsedItemId = request.UsedItemId,
                CostGold = costGold,
                IsSuccess = result.IsSuccess ? 1 : 0,
                VipBonus = (vipBonus - 1) * 100, // 转换为加成百分比
                NirvanaType = request.NirvanaType.ToString(),
                CreateTime = DateTime.Now
            };

            await _db.Insertable(log).ExecuteCommandAsync();
        }

        /// <summary>
        /// 计算新属性值
        /// </summary>
        private long CalculateNewAttribute(long? mainAttr, long? subAttr, decimal mainInherit, decimal subInherit)
        {
            try
            {
                var mainValue = mainAttr ?? 0;
                var subValue = subAttr ?? 0;

                var newValue = (long)(mainValue * mainInherit + subValue * subInherit);
                return Math.Max(1, newValue);
            }
            catch
            {
                return 1;
            }
        }

        /// <summary>
        /// 获取宠物名称
        /// </summary>
        private string GetPetName(int petNo)
        {
            // 这里应该从宠物配置表获取名称，暂时返回默认值
            return $"宠物{petNo}";
        }

        /// <summary>
        /// 执行变脸
        /// </summary>
        public async Task<ApiResult<FaceChangeResultDto>> ExecuteFaceChangeAsync(FaceChangeRequestDto request)
        {
            // 变脸功能将在下一个文件中实现
            return ApiResult<FaceChangeResultDto>.CreateError("变脸功能暂未实现");
        }

        /// <summary>
        /// 获取转生配置列表
        /// </summary>
        public async Task<ApiResult<List<PetNirvanaConfigDto>>> GetNirvanaConfigsAsync()
        {
            try
            {
                var configs = await _configService.GetActiveConfigsAsync();
                var configDtos = configs.Select(c => new PetNirvanaConfigDto
                {
                    Id = c.Id,
                    MainPetNo = c.MainPetNo,
                    SubPetNo = c.SubPetNo,
                    NirvanaPetNo = c.NirvanaPetNo,
                    ResultPetNo = c.ResultPetNo,
                    RequiredLevel = c.RequiredLevel,
                    BaseSuccessRate = c.BaseSuccessRate,
                    CostGold = c.CostGold,
                    MainGrowthInherit = c.MainGrowthInherit,
                    SubGrowthInherit = c.SubGrowthInherit,
                    SpecialRule = c.SpecialRule,
                    IsActive = c.IsActive == 1
                }).ToList();

                return ApiResult<List<PetNirvanaConfigDto>>.CreateSuccess(configDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生配置列表失败");
                return ApiResult<List<PetNirvanaConfigDto>>.CreateError("获取转生配置失败");
            }
        }

        /// <summary>
        /// 预览转生结果
        /// </summary>
        public async Task<ApiResult<NirvanaPreviewDto>> PreviewNirvanaAsync(NirvanaPreviewRequestDto request)
        {
            try
            {
                var preview = new NirvanaPreviewDto();

                // 验证条件
                var nirvanaRequest = new NirvanaRequestDto
                {
                    UserId = request.UserId,
                    MainPetId = request.MainPetId,
                    SubPetId = request.SubPetId,
                    NirvanaPetId = request.NirvanaPetId,
                    UsedItemId = request.UsedItemId
                };

                var (isValid, errors) = await _calculationService.ValidateNirvanaConditionsAsync(nirvanaRequest);
                preview.CanNirvana = isValid;
                preview.ValidationErrors = errors;

                if (isValid)
                {
                    // 获取相关数据
                    var user = await _db.Queryable<user>().FirstAsync(u => u.id == request.UserId);
                    var mainPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.MainPetId);
                    var subPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.SubPetId);
                    var nirvanaPet = await _db.Queryable<user_pet>().FirstAsync(p => p.id == request.NirvanaPetId);

                    var config = await _configService.FindMatchingConfigAsync(mainPet.pet_no, subPet.pet_no, nirvanaPet.pet_no);
                    
                    if (config != null)
                    {
                        preview.EstimatedSuccessRate = await _calculationService.CalculateSuccessRateAsync(nirvanaRequest, config);
                        preview.EstimatedCostGold = _calculationService.CalculateCostGold(config, user);
                        preview.VipBonus = _calculationService.CalculateVipBonus(user) - 1;

                        // 计算成长范围
                        var minGrowth = await _calculationService.CalculateGrowthGainAsync(mainPet, subPet, -0.1m, user);
                        var maxGrowth = await _calculationService.CalculateGrowthGainAsync(mainPet, subPet, 0.1m, user);
                        preview.EstimatedGrowthRange = $"{minGrowth:F2} - {maxGrowth:F2}";

                        preview.ConfigInfo = new PetNirvanaConfigDto
                        {
                            Id = config.Id,
                            MainPetNo = config.MainPetNo,
                            SubPetNo = config.SubPetNo,
                            NirvanaPetNo = config.NirvanaPetNo,
                            ResultPetNo = config.ResultPetNo,
                            RequiredLevel = config.RequiredLevel,
                            BaseSuccessRate = config.BaseSuccessRate,
                            CostGold = config.CostGold,
                            IsActive = config.IsActive == 1
                        };
                    }
                }

                return ApiResult<NirvanaPreviewDto>.CreateSuccess(preview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "预览转生结果失败");
                return ApiResult<NirvanaPreviewDto>.CreateError("预览转生结果失败");
            }
        }

        /// <summary>
        /// 获取用户转生记录
        /// </summary>
        public async Task<ApiResult<PagedResult<NirvanaRecordDto>>> GetUserRecordsAsync(int userId, int page = 1, int size = 20)
        {
            try
            {
                var totalCount = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId)
                    .CountAsync();

                var records = await _db.Queryable<PetNirvanaLog>()
                    .Where(l => l.UserId == userId)
                    .OrderBy(l => l.CreateTime, OrderByType.Desc)
                    .ToPageListAsync(page, size);

                var recordDtos = records.Select(r => new NirvanaRecordDto
                {
                    Id = r.Id,
                    UserId = r.UserId,
                    MainPetId = r.MainPetId,
                    SubPetId = r.SubPetId,
                    NirvanaPetId = r.NirvanaPetId,
                    IsSuccess = r.IsSuccess == 1,
                    ResultGrowth = r.ResultGrowth,
                    CostGold = r.CostGold,
                    VipBonus = r.VipBonus,
                    NirvanaType = r.NirvanaType,
                    CreateTime = r.CreateTime
                }).ToList();

                var pagedResult = new PagedResult<NirvanaRecordDto>
                {
                    Items = recordDtos,
                    TotalCount = totalCount,
                    PageIndex = page,
                    PageSize = size
                };

                return ApiResult<PagedResult<NirvanaRecordDto>>.CreateSuccess(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户转生记录失败");
                return ApiResult<PagedResult<NirvanaRecordDto>>.CreateError("获取转生记录失败");
            }
        }

        /// <summary>
        /// 获取转生统计信息
        /// </summary>
        public async Task<ApiResult<NirvanaStatisticsDto>> GetStatisticsAsync(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                var query = _db.Queryable<PetNirvanaLog>();

                if (startDate.HasValue)
                    query = query.Where(l => l.CreateTime >= startDate.Value);
                
                if (endDate.HasValue)
                    query = query.Where(l => l.CreateTime <= endDate.Value);

                var logs = await query.ToListAsync();

                var statistics = new NirvanaStatisticsDto
                {
                    TotalAttempts = logs.Count,
                    SuccessCount = logs.Count(l => l.IsSuccess == 1),
                    TotalCostGold = logs.Sum(l => l.CostGold),
                    FaceChangeCount = logs.Count(l => l.NirvanaType == "FACE_CHANGE"),
                    TimeRange = $"{startDate:yyyy-MM-dd} 至 {endDate:yyyy-MM-dd}"
                };

                if (statistics.TotalAttempts > 0)
                {
                    statistics.SuccessRate = (decimal)statistics.SuccessCount / statistics.TotalAttempts;
                    
                    var successfulLogs = logs.Where(l => l.IsSuccess == 1 && l.ResultGrowth.HasValue).ToList();
                    if (successfulLogs.Any())
                    {
                        statistics.AverageGrowthGained = successfulLogs.Average(l => l.ResultGrowth.Value);
                    }
                }

                return ApiResult<NirvanaStatisticsDto>.CreateSuccess(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取转生统计信息失败");
                return ApiResult<NirvanaStatisticsDto>.CreateError("获取统计信息失败");
            }
        }
    }
}
