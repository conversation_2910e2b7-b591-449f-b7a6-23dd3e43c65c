using WebApplication_HM.Services;
using WebApplication_HM.Services.PropScript;
using WebApplication_HM.Sugar;
using Microsoft.OpenApi.Models;
using System.Reflection;
using WebApplication_HM.Services.tool;
using SqlSugar;
using WebApplication_HM.Services.SocketInfo;
using WebApplication_HM.IServices;
using WebApplication_HM.Interface;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// 配置 Kestrel
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.ListenAnyIP(5000); // HTTP端口
    //serverOptions.ListenAnyIP(5001, listenOptions =>
    //{
    //    listenOptions.UseHttps(); // HTTPS端口
    //});
});

// 添加内存缓存服务
builder.Services.AddMemoryCache();

// 注册SqlSugar服务
builder.Services.AddScoped<DbContext>();

// 注册生成实体服务
builder.Services.AddScoped<IDbFirstService, DbFirstService>();

// 注册道具相关服务
builder.Services.AddScoped<IPropRepository, PropRepository>();
builder.Services.AddScoped<PropService>();

// 注册装备相关服务
builder.Services.AddScoped<IEquipmentRepository, EquipmentRepository>();
builder.Services.AddScoped<IEquipmentService, EquipmentService>();
builder.Services.AddScoped<IGemstoneService, GemstoneService>();
builder.Services.AddScoped<ISuitService, SuitService>();
builder.Services.AddScoped<IEquipmentAttributeService, EquipmentAttributeService>();

// 注册新增的装备功能服务
builder.Services.AddScoped<EquipmentEnhanceService>();
builder.Services.AddScoped<EquipmentGemstoneService>();
builder.Services.AddScoped<EquipmentResolveService>();

// 注册宠物进化服务
builder.Services.AddScoped<IPetEvolutionService, PetEvolutionService>();

// 注册宠物合成服务
builder.Services.AddScoped<IPetSynthesisService, PetSynthesisService>();
builder.Services.AddScoped<ISynthesisFormulaService, SynthesisFormulaService>();
builder.Services.AddScoped<ISynthesisGrowthCalculator, SynthesisGrowthCalculator>();
builder.Services.AddScoped<IGodPetSynthesisCalculator, GodPetSynthesisCalculator>();
builder.Services.AddScoped<EquipmentObtainService>();
builder.Services.AddScoped<SuitEffectService>();
builder.Services.AddScoped<ElementTransformService>();

// 注册基础玩家服务
builder.Services.AddScoped<PlayerService>();

// 注册增强的玩家服务（装饰器模式）
builder.Services.AddScoped<IPlayerService, EnhancedPlayerService>();

// 注册境界系统服务
builder.Services.AddScoped<RealmService>();

// 注册转生系统服务
builder.Services.AddScoped<INirvanaService, NirvanaService>();
builder.Services.AddScoped<INirvanaCalculationService, NirvanaCalculationService>();
builder.Services.AddScoped<INirvanaConfigService, NirvanaConfigService>();
builder.Services.AddScoped<INirvanaAntiCheatService, NirvanaAntiCheatService>();

// 注册实时通知服务
builder.Services.AddSingleton<IRealTimeService, RealTimeNotificationService>();

// 注册WebSocket服务
builder.Services.AddSingleton<WebSocketConnectionManager>();
builder.Services.AddSingleton<ChatMessageHandler>();

// 注册UniversalWebSocketHandler
builder.Services.AddSingleton<UniversalWebSocketHandler>();



// 注册道具脚本系统服务
builder.Services.AddScoped<SimplePropScriptEngine>();
builder.Services.AddScoped<MultiScriptService>();

// 注册战斗相关服务
builder.Services.AddScoped<WebAntiCheatService>();
// 注册其他战斗相关服务（暂时注释，等待实现）
// builder.Services.AddScoped<IMonsterAttributeService, MonsterAttributeService>();
// builder.Services.AddScoped<IAntiCheatService, AntiCheatService>();
// builder.Services.AddScoped<IDropService, DropService>();

// 注册 SqlSugar
builder.Services.AddScoped<ISqlSugarClient>(s =>
{
    var configuration = s.GetRequiredService<IConfiguration>();
    var connectionString = configuration.GetConnectionString("DefaultConnection");
    
    var sqlSugar = new SqlSugarClient(new ConnectionConfig()
    {
        ConnectionString = connectionString,
        DbType = DbType.MySql,
        IsAutoCloseConnection = true,
        InitKeyType = InitKeyType.Attribute,
        ConfigureExternalServices = new ConfigureExternalServices
        {
            EntityService = (property, column) =>
            {
                if (property.PropertyType.IsGenericType &&
                    property.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    column.IsNullable = true;
                }
            }
        }
    });

    // 设置SQL执行的日志
    sqlSugar.Aop.OnLogExecuting = (sql, parameters) =>
    {
        try
        {
            Console.WriteLine($"SQL: {sql}");
        }
        catch
        {
            // 忽略日志错误，防止影响主要功能
        }
    };

    return sqlSugar;
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo 
    { 
        Title = "WMS API", 
        Version = "v1",
        Description = "WMS系统API文档",
        Contact = new OpenApiContact
        {
            Name = "WMS Team",
            Email = "<EMAIL>"
        }
    });

    // 添加JWT认证配置
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // 启用XML注释
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);
});

// 添加CORS服务
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder =>
        {
            builder.AllowAnyOrigin()
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
});

var app = builder.Build();


// 启用详细的异常页面
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "WMS API v1");
        c.RoutePrefix = string.Empty; // 设置Swagger UI的根路径
        // 配置Swagger UI的一些选项
        c.DefaultModelsExpandDepth(-1); // 隐藏Models
        //c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None); // 折叠所有接口
        c.EnableFilter(); // 启用过滤
        c.EnableDeepLinking(); // 启用深度链接
    });

    // 添加重定向中间件
    app.Use(async (context, next) =>
    {
        if (context.Request.Path == "/swagger")
        {
            context.Response.Redirect("/");
            return;
        }
        await next();
    });
}

// 将默认文件配置添加到管道的最前面
app.UseDefaultFiles(new DefaultFilesOptions
{
    DefaultFileNames = new List<string> { "index.html" }
});

// 配置默认静态文件
app.UseStaticFiles();

// 配置游戏资源静态文件
var gameContentPath = Path.Combine(builder.Environment.ContentRootPath, "wwwroot", "game");
if (Directory.Exists(gameContentPath))
{
    app.UseStaticFiles(new StaticFileOptions
    {
        FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(gameContentPath),
        RequestPath = "/game"
    });
}

// 配置游戏页面路由 - 将游戏页面请求重定向到正确的HTML文件
app.Use(async (context, next) =>
{
    var path = context.Request.Path.Value?.ToLower();

    // 如果请求的是游戏页面，重定向到pages文件夹中的HTML文件
    if (path == "/game" || path == "/game/")
    {
        context.Response.Redirect("/game/pages/Index.html");
        return;
    }

    await next();
});

// 启用WebSocket
var webSocketOptions = new WebSocketOptions
{
    KeepAliveInterval = TimeSpan.FromMinutes(2)
};
app.UseWebSockets(webSocketOptions);

// 启用CORS
app.UseCors("AllowAll");

// WebSocket统一入口
app.Use(async (context, next) =>
{
    if (context.Request.Path == "/ws/universal")
    {
        if (context.WebSockets.IsWebSocketRequest)
        {
            var webSocket = await context.WebSockets.AcceptWebSocketAsync();
            var handler = context.RequestServices.GetRequiredService<UniversalWebSocketHandler>();
            var buffer = new byte[1024 * 4];
            while (webSocket.State == System.Net.WebSockets.WebSocketState.Open)
            {
                var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
                if (result.MessageType == System.Net.WebSockets.WebSocketMessageType.Text)
                {
                    var message = System.Text.Encoding.UTF8.GetString(buffer, 0, result.Count);
                    await handler.HandleAsync(webSocket, message);
                }
                else if (result.MessageType == System.Net.WebSockets.WebSocketMessageType.Close)
                {
                    await webSocket.CloseAsync(System.Net.WebSockets.WebSocketCloseStatus.NormalClosure, "Closed by client", CancellationToken.None);
                }
            }
        }
        else
        {
            context.Response.StatusCode = 400;
        }
    }
    else
    {
        await next();
    }
});

app.MapControllers();

// 添加简单的健康检查端点
app.MapGet("/health", () => "Healthy");

try
{
    Console.WriteLine("Starting the application...");
    app.Run();
}
catch (Exception ex)
{
    Console.WriteLine($"Application startup failed: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
    throw;
}

// 为测试提供公共访问
public partial class Program { }