import { _decorator, Component, Node, ProgressBar, find, Label, Sprite,SpriteFrame,resources } from 'cc';
const { ccclass, property } = _decorator;

import { MassiveResourceManager } from '../manager/MassiveResourceManager';

//宠物信息栏脚本
@ccclass('petInformation')
export class petInformation extends Component {
    
    public Nmae:Label;//宠物名称
    public grade: Label;//等级
    public petHead: Sprite | null = null; //头像

    HP_pet: ProgressBar | null = null; //生命
    MP_pet: ProgressBar | null = null;//魔法
    EXP_pet: ProgressBar | null = null;//经验

    start() {
        const HP_petNode: Node = find("/HP_pet", this.node);//读取宠物节点
        const MP_petNode: Node = find("/MP_pet", this.node);//读取怪物节点
        const EXP_petNode: Node = find("/EXP_pet", this.node);//读功能栏节点
        const NameCode:Node=find("/zd03/name",this.node);//读取名称节点
        const gradeCode:Node=find("/zd03/grade",this.node);//读取等级节点
        const petHeadCode:Node=find("/petHead",this.node);//读取头像节点

        if (!HP_petNode) {
            console.error("HP_petNode节点不存在");
            return;
        }
        if (!MP_petNode) {
            console.error("HP_petNode节点不存在");
            return;
        }
        if (!EXP_petNode) {
            console.error("HP_petNode节点不存在");
            return;
        }
        
        if(!NameCode){
                console.error("宠物名节点不存在");
            return;
        }
        if(!gradeCode){
                console.error("宠物等级节点不存在");
            return;
        }
        if(!petHeadCode){
                console.error("宠物头像节点不存在");
            return;
        }
        
        this.Nmae = NameCode.getComponent(Label);
        this.grade = gradeCode.getComponent(Label);
        this.petHead = petHeadCode.getComponent(Sprite);

        this.MP_pet = MP_petNode.getComponent(ProgressBar);
        this.HP_pet = HP_petNode.getComponent(ProgressBar);
        this.EXP_pet = EXP_petNode.getComponent(ProgressBar);

    }

    update(deltaTime: number) {

    }

    //更新进度条
    public UpdateProgressBar(Name: string, value: number) {
        if (Name == "HP") {
            this.HP_pet.progress = value;
        }
        if (Name == "MP") {
            this.MP_pet.progress = value;
        }
        if (Name == "EXP") {
            this.EXP_pet.progress = value;
        }
    }

    //更新宠物信息 - 使用智能批量头像加载
    public async UpdatePetInformation(Name: string, grade: number, PetName: string) {
        this.Nmae.string = Name;
        this.grade.string = grade.toString();
        
        try {
            const resourceManager = MassiveResourceManager.getInstance();
            const petId = parseInt(PetName) || 1;
            
            console.log(`🖼️ 开始加载宠物头像: ID=${petId}`);
            
            // 使用智能头像加载（支持批量图集）
            const spriteFrame = await resourceManager.loadPetHead(petId);
            this.petHead.spriteFrame = spriteFrame;
            
            console.log(`✅ 宠物头像加载成功: t${PetName}`);
            
        } catch (error) {
            console.error(`❌ 宠物头像加载失败: t${PetName}`, error);
            // 尝试加载默认头像
            this.loadDefaultHead();
        }
    }

    /**
     * 加载默认头像
     */
    private async loadDefaultHead(): void {
        try {
            const resourceManager = MassiveResourceManager.getInstance();
            const defaultHead = await resourceManager.loadSpriteFrame('head/t32/spriteFrame');
            this.petHead.spriteFrame = defaultHead;
            console.log('✅ 默认头像加载成功');
        } catch (error) {
            console.error('❌ 默认头像加载失败', error);
        }
    }
}


