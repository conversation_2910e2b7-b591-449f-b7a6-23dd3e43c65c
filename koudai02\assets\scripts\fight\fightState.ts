import { _decorator, Component, Node, Sprite, input, Input, EventTouch, tween, Color, Tween, Vec3, Button, SpriteAtlas, Label } from 'cc';
const { ccclass, property } = _decorator;

import { HttpRequest } from '../tool/HttpRequest';
import { Global } from '../Global';
import { MassiveResourceManager } from '../manager/MassiveResourceManager';


// 宠物数据接口定义
interface PetInfo {
    id: number;
    petNo: number;
    name: string;
    element: string;
    level: number;
    exp: number;
    hp: number;
    mp: number;
    growth: number;
    realm: string;
    isMainPet: boolean;
    status: string;
    isMain: boolean;
}
//宠物列表接口定义
interface PetListResponse {
    success: boolean;
    message: string;
    pets: PetInfo[];
    mainPetId: number;
    totalCount: number;
    ranchCount: number;
    carryCount: number;
}

// 🆕 地图数据接口定义
interface MapInfoDTO {
    mapId: number;
    mapName: string;
    mapDesc: string;
    mapType: number;
    atlastName: string;
    background: string;
    icon: string;
    isUnlocked: boolean;
    recommendLevel: number;
}
//地图详细信息接口定义
interface MapDetailConfigDTO {
    limitLevel: number;
    limitGrowth: number;
    requireKey: boolean;
    minGold: number;
    maxGold: number;
    minYuanbao: number;
    maxYuanbao: number;
}
//地图怪物接口定义
interface MapMonsterDTO {
    monsterId: number;
    monsterName: string;
    levelRange: string;
    element: string;
    expReward: number;
}
//地图掉落接口定义
interface MapDropDTO {
    itemId: string;
    itemName: string;
    dropType: string;
    dropRate: number;
    countRange: string;
}

//地图详细信息接口定义
interface MapDetailResponse {
    success: boolean;
    message: string;
    mapInfo?: MapInfoDTO;
    detailConfig?: MapDetailConfigDTO;
    monsters: MapMonsterDTO[];
    drops: MapDropDTO[];
}

@ccclass('fightState')
export class fightState extends Component {

    @property(Sprite)
    pet1!: Sprite;//宠物1
   
    @property(Sprite)
    pet2!: Sprite;//宠物2
   
    @property(Sprite)
    pet3!: Sprite;//宠物3

    //开始战斗按钮
    @property(Button)
    btn_fight: Button = null!;

    //地图描述
    @property(Label)
    private map_desc: Label = null!;

    @property(Node)
    private fightPanel: Node;//战斗面板

    private currentSelectedPet: Sprite | null = null;
    private carryPets: PetInfo[] = []; // 携带的宠物列表
    private petSprites: Sprite[] = []; // 宠物精灵数组
    private resourceManager: MassiveResourceManager;
    
    // 🆕 地图相关数据
    private currentMapDetail: MapDetailResponse | null = null;//地图详细信息
    private mapMonsters: MapMonsterDTO[] = [];//地图怪物
    private mapDrops: MapDropDTO[] = [];//地图掉落

    async start() {
        // 🔧 重置Global.currentPetId，确保用户必须手动选择宠物
        Global.currentPetId = 0;
        console.log("🔄 已重置Global.currentPetId，用户需要手动选择宠物");
        
        // 初始化资源管理器
        this.resourceManager = MassiveResourceManager.getInstance();
        
        // 初始化宠物精灵数组
        this.petSprites = [this.pet1, this.pet2, this.pet3];
        
        // 🆕 获取当前地图详细信息
        await this.loadMapDetail();
        
        // 获取携带的宠物列表
        await this.loadCarryPets();
        
        // 为每个pet添加点击事件监听
        this.addClickEvent(this.pet1, 0);
        this.addClickEvent(this.pet2, 1);
        this.addClickEvent(this.pet3, 2);

        this.btn_fight.node.on(Input.EventType.TOUCH_START, this.onFight, this);
        
        // 🔧 额外的安全措施：监听所有可能的触摸事件
        this.btn_fight.node.on(Input.EventType.TOUCH_END, this.onButtonTouchEnd, this);
        this.btn_fight.node.on(Input.EventType.TOUCH_CANCEL, this.onButtonTouchCancel, this);
    }

    /**
     * 🆕 节点激活时的处理（每次进入页面都会调用）
     */
    async onEnable() {
        console.log("🔄 fightState页面被激活，开始重新初始化...");
        
        // 🔧 重要：只有在start方法已经执行过后才进行重新初始化
        if (!this.resourceManager) {
            console.log("📝 首次激活，等待start方法完成初始化");
            return;
        }
        
        // 🔧 重新获取地图数据和宠物数据
        await this.refreshAllData();
        
        console.log("✅ fightState页面重新初始化完成");
    }

    /**
     * 🆕 获取当前地图详细信息
     */
    private async loadMapDetail(): Promise<void> {
        try {
            console.log(`🗺️ 开始获取地图详细信息 - 地图ID: ${Global.currentMapId}`);
            
            const requestData = {
                mapId: Global.currentMapId,
                userId: Global.userid
            };
            
            const response: MapDetailResponse = await HttpRequest.postConvertJson("Player/GetMapDetail", requestData);
            
            console.log("📥 地图详情接口响应:", response);
            
            if (response && response.success) {
                this.currentMapDetail = response;
                this.mapMonsters = response.monsters || [];
                this.mapDrops = response.drops || [];
                
                // 🆕 将地图资源信息赋值给Global
                if (response.mapInfo) {
                    Global.atlastName = response.mapInfo.atlastName || "";
                    Global.background = response.mapInfo.background || "";
                    
                    console.log(`📦 已更新Global地图资源:`);
                    console.log(`   - 图集名称: "${Global.atlastName}"`);
                    console.log(`   - 背景图片: "${Global.background}"`);
                    
                    // 🆕 将地图描述赋值给map_desc Label组件
                    if (this.map_desc) {
                        this.map_desc.string = response.mapInfo.mapDesc || "暂无地图描述";
                        console.log(`📝 已设置地图描述: "${this.map_desc.string}"`);
                    } else {
                        console.warn("⚠️ map_desc Label组件未找到");
                    }
                }
                
                console.log(`✅ 成功获取地图详情: ${response.mapInfo?.mapName}`);
                console.log(`📊 地图统计 - 怪物数量: ${this.mapMonsters.length}, 掉落配置: ${this.mapDrops.length}`);
                
                // 输出详细的地图信息
                this.logMapDetailInfo();
                
            } else {
                const errorMsg = response?.message || "获取地图详情失败";
                console.error("❌ 获取地图详情失败:", errorMsg);
                console.error("📥 完整响应:", response);
                
                // 使用默认地图数据作为备用
                this.useDefaultMapData();
            }
            
        } catch (error) {
            console.error("💥 获取地图详情时发生错误:", error);
            // 使用默认地图数据作为备用
            this.useDefaultMapData();
        }
    }

    /**
     * 🆕 输出详细的地图信息到控制台
     */
    private logMapDetailInfo(): void {
        if (!this.currentMapDetail) return;
        
        const { mapInfo, detailConfig, monsters, drops } = this.currentMapDetail;
        
        console.log("🗺️ ===== 地图详细信息 =====");
        
        if (mapInfo) {
            console.log(`📍 地图名称: ${mapInfo.mapName}`);
            console.log(`📝 地图描述: ${mapInfo.mapDesc}`);
            console.log(`🖼️ 图集名称: ${mapInfo.atlastName || '无'}`);
            console.log(`🎯 推荐等级: ${mapInfo.recommendLevel}`);
            console.log(`🔓 解锁状态: ${mapInfo.isUnlocked ? '已解锁' : '未解锁'}`);
        }
        
        if (detailConfig) {
            console.log("⚙️ 地图配置:");
            console.log(`   限制等级: ${detailConfig.limitLevel}`);
            console.log(`   限制成长: ${detailConfig.limitGrowth}`);
            console.log(`   需要钥匙: ${detailConfig.requireKey ? '是' : '否'}`);
            console.log(`   金币奖励: ${detailConfig.minGold} - ${detailConfig.maxGold}`);
            console.log(`   元宝奖励: ${detailConfig.minYuanbao} - ${detailConfig.maxYuanbao}`);
        }
        
        if (monsters && monsters.length > 0) {
            console.log("👹 地图怪物:");
            monsters.forEach((monster, index) => {
                console.log(`   ${index + 1}. ${monster.monsterName} (ID: ${monster.monsterId})`);
                console.log(`      等级范围: ${monster.levelRange}, 属性: ${monster.element}, 经验: ${monster.expReward}`);
            });
        }
        
        if (drops && drops.length > 0) {
            console.log("💎 掉落配置:");
            drops.forEach((drop, index) => {
                console.log(`   ${index + 1}. ${drop.itemName} (ID: ${drop.itemId})`);
                console.log(`      类型: ${drop.dropType}, 概率: ${(drop.dropRate * 100).toFixed(2)}%, 数量: ${drop.countRange}`);
            });
        }
        
        console.log("🗺️ ========================");
        
        // 🆕 显示Global中存储的地图资源信息
        console.log("📦 Global地图资源状态:");
        console.log(`   - Global.atlastName: "${Global.atlastName}"`);
        console.log(`   - Global.background: "${Global.background}"`);
    }

    /**
     * 🆕 使用默认地图数据（备用方案）
     */
    private useDefaultMapData(): void {
        console.log("🔄 使用默认地图数据作为备用方案");
        
        this.currentMapDetail = {
            success: true,
            message: "使用默认数据",
            mapInfo: {
                mapId: Global.currentMapId,
                mapName: "默认地图",
                mapDesc: "默认地图描述",
                mapType: 1,
                atlastName: "默认图集",
                background: "默认背景",
                icon: "",
                isUnlocked: true,
                recommendLevel: 1
            },
            detailConfig: {
                limitLevel: 1,
                limitGrowth: 1.0,
                requireKey: false,
                minGold: 100,
                maxGold: 500,
                minYuanbao: 1,
                maxYuanbao: 5
            },
            monsters: [
                {
                    monsterId: 1,
                    monsterName: "默认怪物",
                    levelRange: "1-5",
                    element: "无",
                    expReward: 100
                }
            ],
            drops: [
                {
                    itemId: "1",
                    itemName: "默认道具",
                    dropType: "地图掉落",
                    dropRate: 0.1,
                    countRange: "1-3"
                }
            ]
        };
        
        this.mapMonsters = this.currentMapDetail.monsters;
        this.mapDrops = this.currentMapDetail.drops;
        
        // 🆕 将默认地图资源信息赋值给Global
        if (this.currentMapDetail.mapInfo) {
            Global.atlastName = this.currentMapDetail.mapInfo.atlastName;
            Global.background = this.currentMapDetail.mapInfo.background;
            
            console.log(`📦 已更新Global默认地图资源:`);
            console.log(`   - 图集名称: "${Global.atlastName}"`);
            console.log(`   - 背景图片: "${Global.background}"`);
            
            // 🆕 将默认地图描述赋值给map_desc Label组件
            if (this.map_desc) {
                this.map_desc.string = this.currentMapDetail.mapInfo.mapDesc || "暂无地图描述";
                console.log(`📝 已设置默认地图描述: "${this.map_desc.string}"`);
            } else {
                console.warn("⚠️ map_desc Label组件未找到");
            }
        }
        
        console.log("💡 默认地图数据已加载");
    }

    /**
     * 🆕 获取当前地图的怪物列表
     */
    public getMapMonsters(): MapMonsterDTO[] {
        return this.mapMonsters;
    }

    /**
     * 🆕 获取当前地图的掉落配置
     */
    public getMapDrops(): MapDropDTO[] {
        return this.mapDrops;
    }

    /**
     * 🆕 获取当前地图详细信息
     */
    public getCurrentMapDetail(): MapDetailResponse | null {
        return this.currentMapDetail;
    }

    /**
     * 🆕 检查是否满足地图进入条件
     */
    public checkMapRequirements(): { canEnter: boolean; errorMessage: string } {
        if (!this.currentMapDetail?.detailConfig) {
            return { canEnter: true, errorMessage: "" }; // 没有配置信息时默认允许进入
        }
        
        const config = this.currentMapDetail.detailConfig;
        
        // 这里可以添加更多的检查逻辑，比如检查用户等级、成长等
        // 暂时简化为总是允许进入
        if (config.limitLevel > 1) {
            console.log(`⚠️ 地图限制等级: ${config.limitLevel}`);
        }
        
        if (config.requireKey) {
            console.log("🔑 该地图需要钥匙");
            // 这里可以添加检查用户是否有钥匙的逻辑
        }
        
        return { canEnter: true, errorMessage: "" };
    }

    /**
     * 🆕 随机选择一个地图怪物（用于战斗）
     */
    public getRandomMapMonster(): MapMonsterDTO | null {
        if (this.mapMonsters.length === 0) {
            console.warn("⚠️ 当前地图没有怪物配置");
            return null;
        }
        
        const randomIndex = Math.floor(Math.random() * this.mapMonsters.length);
        const selectedMonster = this.mapMonsters[randomIndex];
        
        console.log(`🎲 随机选择怪物: ${selectedMonster.monsterName} (ID: ${selectedMonster.monsterId})`);
        return selectedMonster;
    }

    /**
     * 获取携带的宠物列表
     */
    private async loadCarryPets(): Promise<void> {
        try {
            console.log("🐾 开始获取携带的宠物列表...");
            
            // 🔧 按照接口文档要求，使用POST方式请求 /api/Player/pets
            const requestData = {
                userId: Global.userid,
                status: "携带",           // 过滤携带状态的宠物
                onlyMainPet: false      // 获取所有携带的宠物，不只是主战宠物
            };
            
            const response: PetListResponse = await HttpRequest.postConvertJson("Player/GetUserPets", requestData);
            
            console.log("📥 接口响应:", response);
  
            if (response && response.success) {
                this.carryPets = response.pets || [];
                console.log(`✅ 成功获取${this.carryPets.length}只携带宠物`);
                console.log(`📊 统计信息 - 总数:${response.totalCount}, 牧场:${response.ranchCount}, 携带:${response.carryCount}`);
                console.log("🐾 宠物列表:", this.carryPets);
                
                // 🆕 设置主战宠物名称到Global.petName
                const mainPet = this.carryPets.find(pet => pet.isMain === true);
                if (mainPet) {
                    Global.petName = mainPet.name;
                    Global.currentPetId = mainPet.petNo;
                    console.log(`🎯 设置主战宠物: "${Global.petName}" (ID: ${Global.currentPetId})`);
                } else if (this.carryPets.length > 0) {
                    // 如果没找到主战宠物，使用第一只宠物
                    Global.petName = this.carryPets[0].name;
                    Global.currentPetId = this.carryPets[0].petNo;
                    console.log(`🔄 使用第一只宠物: "${Global.petName}" (ID: ${Global.currentPetId})`);
                }
                
                // 设置宠物显示
                await this.setupPetDisplay();
                
                // 🔧 设置主战宠物为默认选中宠物 - 使用找到的主战宠物ID
                const selectedPetId = mainPet ? mainPet.petNo : (this.carryPets.length > 0 ? this.carryPets[0].petNo : 0);
                console.log(`🎯 设置默认选中宠物ID: ${selectedPetId}`);
                
                // 设置主战宠物的视觉选中效果和currentSelectedPet
                this.setDefaultSelectedPet(selectedPetId);
                
                // 🔧 启用开始战斗按钮（因为已有默认选中的宠物）
                this.updateFightButtonState(true);
                
            } else {
                const errorMsg = response?.message || "接口返回失败状态";
                console.error("❌ 获取宠物列表失败:", errorMsg);
                console.error("📥 完整响应:", response);
                
                // 使用默认宠物数据作为备用
                this.useDefaultPets();
            }
            
        } catch (error) {
            console.error("💥 获取宠物列表时发生错误:", error);
            // 使用默认宠物数据作为备用
            this.useDefaultPets();
        }
    }
    
    /**
     * 使用默认宠物数据（备用方案）
     */
    private useDefaultPets(): void {
        console.log("🔄 使用默认宠物数据作为备用方案");
        
        // 🔧 使用固定的默认宠物ID，而不是当前已重置为0的Global.currentPetId
        const defaultPetId = 1; // 使用宠物ID 1作为默认宠物
        
        this.carryPets = [
            {
                id: 1,
                petNo: defaultPetId,
                name: "默认宠物",
                element: "火",
                level: 1,
                exp: 0,
                hp: 100,
                mp: 50,
                growth: 1.0,
                realm: "筑基期",
                isMainPet: true,
                status: "携带",
                isMain: true
            }
        ];
        
        // 🆕 设置默认宠物名称到Global.petName
        Global.petName = this.carryPets[0].name;
        console.log(`🎯 设置默认宠物名称: "${Global.petName}"`);
        
        // 设置显示
        this.setupPetDisplay();
        
        // 🔧 设置默认宠物为选中状态
        Global.currentPetId = defaultPetId;
        console.log(`🎯 设置默认宠物ID: ${Global.currentPetId}`);
        
        // 设置第一只宠物为选中状态
        if (this.petSprites[0]) {
            this.currentSelectedPet = this.petSprites[0];
            this.applyClickEffect(this.petSprites[0]);
        }
        
        // 启用开始战斗按钮
        this.updateFightButtonState(true);
        
        console.log("💡 默认宠物数据已加载并设置为选中状态");
    }

    /**
     * 设置宠物显示
     */
    private async setupPetDisplay(): Promise<void> {
        console.log("🎨 开始设置宠物显示...");
        
        // 最多显示3只宠物
        const maxPets = Math.min(this.carryPets.length, 3);
        
        for (let i = 0; i < 3; i++) {
            const petSprite = this.petSprites[i];
            
            if (i < maxPets && petSprite) {
                const petInfo = this.carryPets[i];
                
                try {
                    // 获取宠物对应的图集信息
                    const { atlas, enabled } = this.resourceManager.getPetGroup(petInfo.petNo);
                    
                    if (!enabled) {
                        console.warn(`⚠️ 宠物${petInfo.petNo}的图集${atlas}未启用，使用默认图集`);
                    }
                    
                    // 加载图集
                    const spriteAtlas: SpriteAtlas = await this.resourceManager.loadAtlas(atlas);
                    petSprite.spriteAtlas = spriteAtlas;
                    
                    // 加载宠物头像（使用t类型头像）
                    const spriteFrame = await this.resourceManager.loadPetHeadType(petInfo.petNo, 't');
                    petSprite.spriteFrame = spriteFrame;
                    
                    // 显示宠物节点
                    petSprite.node.active = true;
                    
                    console.log(`✅ 宠物${i + 1}设置完成: ${petInfo.name} (ID: ${petInfo.petNo})`);
                    
                } catch (error) {
                    console.error(`❌ 设置宠物${i + 1}显示失败:`, error);
                    
                    // 隐藏失败的宠物节点
                    petSprite.node.active = false;
                }
                
            } else if (petSprite) {
                // 隐藏多余的宠物节点
                petSprite.node.active = false;
                console.log(`🔒 隐藏宠物${i + 1}节点（无数据）`);
            }
        }
        
        console.log("🎉 宠物显示设置完成");
    }

    /**
     * 设置默认选中的宠物（主战宠物）- 🔧 设置真正的选中状态
     */
    private setDefaultSelectedPet(mainPetId: number): void {
        // 查找主战宠物在携带列表中的索引
        const mainPetIndex = this.carryPets.findIndex(pet => pet.petNo === mainPetId);
        
        if (mainPetIndex !== -1 && mainPetIndex < 3) {
            const mainPetSprite = this.petSprites[mainPetIndex];
            if (mainPetSprite && mainPetSprite.node.active) {
                console.log(`🌟 设置主战宠物为默认选中: ${this.carryPets[mainPetIndex].name} (ID: ${mainPetId})`);
                
                // 🔧 设置为真正的选中状态
                this.currentSelectedPet = mainPetSprite;
                
                // 应用选中的视觉效果
                this.applyClickEffect(mainPetSprite);
                
                // 🆕 更新Global.petName为选中宠物的名称
                Global.petName = this.carryPets[mainPetIndex].name;
                console.log(`🎯 更新选中宠物名称为: "${Global.petName}"`);
                
                return;
            }
        }
        
        // 如果找不到主战宠物，选择第一只宠物
        if (this.carryPets.length > 0 && this.petSprites[0] && this.petSprites[0].node.active) {
            console.log("🔄 未找到主战宠物，默认选择第一只宠物");
            
            // 更新Global.currentPetId为第一只宠物的ID
            Global.currentPetId = this.carryPets[0].petNo;
            console.log(`🎯 更新默认选中宠物ID为: ${Global.currentPetId}`);
            
            // 🆕 更新Global.petName为第一只宠物的名称
            Global.petName = this.carryPets[0].name;
            console.log(`🎯 更新默认选中宠物名称为: "${Global.petName}"`);
            
            // 设置为真正的选中状态
            this.currentSelectedPet = this.petSprites[0];
            
            // 应用选中的视觉效果
            this.applyClickEffect(this.petSprites[0]);
        }
    }

    /**
     * 🔧 更新开始战斗按钮的状态
     */
    private updateFightButtonState(enabled: boolean): void {
        if (!this.btn_fight) return;
        
        if (enabled) {
            // 启用状态：正常颜色
            this.btn_fight.node.getComponent(Sprite)!.color = new Color(255, 255, 255, 255);
            console.log("✅ 开始战斗按钮已启用");
        } else {
            // 禁用状态：灰色提示
            this.btn_fight.node.getComponent(Sprite)!.color = new Color(150, 150, 150, 255);
            console.log("🔒 开始战斗按钮已禁用（需要先选择宠物）");
        }
    }

    onFight() {
        console.log("🔘 用户点击开始战斗按钮");
        console.log(`📊 当前状态 - Global.currentPetId: ${Global.currentPetId}, currentSelectedPet: ${this.currentSelectedPet ? '已选择' : '未选择'}`);
        console.log(`🗺️ 当前地图ID: ${Global.currentMapId}`);
        
        // 🔧 在跳转前验证是否已选择宠物
        if (!this.validatePetSelection()) {
            console.log("❌ 宠物选择验证失败，阻止跳转到战斗页面");
            return;
        }
        
        // 🆕 验证地图进入条件
        const mapRequirementCheck = this.checkMapRequirements();
        if (!mapRequirementCheck.canEnter) {
            console.warn("❌ 地图进入条件验证失败:", mapRequirementCheck.errorMessage);
            alert(`无法进入地图: ${mapRequirementCheck.errorMessage}`);
            return;
        }
        
        // 🆕 输出当前战斗配置信息
        console.log("⚔️ ===== 战斗配置信息 =====");
        console.log(`🎯 选中宠物ID: ${Global.currentPetId}`);
        console.log(`🗺️ 当前地图: ${this.currentMapDetail?.mapInfo?.mapName || '未知地图'} (ID: ${Global.currentMapId})`);
        console.log(`🖼️ 地图图集: ${Global.atlastName || '未设置'}`);
        console.log(`🌄 地图背景: ${Global.background || '未设置'}`);
        console.log(`👹 可战斗怪物数量: ${this.mapMonsters.length}`);
        console.log(`💎 掉落配置数量: ${this.mapDrops.length}`);
        
        // 🆕 将地图数据存储到Global中，供fightMian使用
        Global.currentMapMonsters = [...this.mapMonsters];
        Global.currentMapDrops = [...this.mapDrops];
        Global.currentMapInfo = this.currentMapDetail?.mapInfo || null;
        
        // 随机选择一个怪物用于即将开始的战斗
        const selectedMonster = this.getRandomMapMonster();
        if (selectedMonster) {
            console.log(`🎲 本次战斗怪物: ${selectedMonster.monsterName} (ID: ${selectedMonster.monsterId})`);
            // 🆕 将选中的怪物ID存储到Global中，供战斗系统使用
            Global.selectedMonsterId = selectedMonster.monsterId;
            console.log(`✅ 已将选中怪物ID存储到Global.selectedMonsterId: ${Global.selectedMonsterId}`);
        } else {
            console.warn("⚠️ 未选择到怪物，使用默认怪物ID");
            Global.selectedMonsterId = 1; // 默认怪物ID
        }
        
        console.log("📦 已将地图数据存储到Global中:");
        console.log(`   - 怪物数量: ${Global.currentMapMonsters.length}`);
        console.log(`   - 掉落配置数量: ${Global.currentMapDrops.length}`);
        console.log(`   - 地图信息: ${Global.currentMapInfo?.mapName || '无'}`);
        console.log("⚔️ ========================");
        
        console.log(`🎯 所有验证通过，开始战斗 - 宠物ID: ${Global.currentPetId}, 地图ID: ${Global.currentMapId}`);
        this.fightPanel.active = true;
        this.node.active = false;
        console.log("✅ 已跳转到战斗页面");
    }

    /**
     * 🔧 验证宠物选择状态
     */
    private validatePetSelection(): boolean {
        // 检查是否有选中的宠物
        if (!Global.currentPetId || Global.currentPetId <= 0) {
            console.warn("⚠️ Global.currentPetId无效，无法开始战斗");
            alert("宠物选择状态异常，请重新进入页面！");
            return false;
        }

        // 检查是否有当前选中的精灵
        if (!this.currentSelectedPet) {
            console.warn("⚠️ 没有当前选中的宠物精灵");
            alert("宠物选择状态异常，请重新进入页面！");
            return false;
        }

        // 检查选中的宠物是否在携带列表中
        const selectedPetInfo = this.getCurrentSelectedPetInfo();
        if (!selectedPetInfo) {
            console.warn("⚠️ 选中的宠物信息无效");
            alert("选中的宠物信息无效，请重新进入页面！");
            return false;
        }

        console.log(`✅ 宠物选择验证通过: ${selectedPetInfo.name} (ID: ${selectedPetInfo.petNo})`);
        return true;
    }

    /**
     * 🔧 按钮触摸结束事件（防护措施）
     */
    private onButtonTouchEnd(event: EventTouch): void {
        console.log("🔘 按钮触摸结束事件");
        // 这里不执行跳转逻辑，只在TOUCH_START中处理
    }

    /**
     * 🔧 按钮触摸取消事件（防护措施）
     */
    private onButtonTouchCancel(event: EventTouch): void {
        console.log("🔘 按钮触摸取消事件");
        // 这里不执行跳转逻辑，只在TOUCH_START中处理
    }

    update(deltaTime: number) {
        
    }

    /**
     * 为精灵添加点击事件
     * @param pet 要添加点击事件的精灵
     * @param index 宠物在数组中的索引
     */
    private addClickEvent(pet: Sprite, index: number) {
        if (!pet || !pet.node) return;
        
        pet.node.on(Input.EventType.TOUCH_START, (event: EventTouch) => {
            // 只有当宠物节点可见时才响应点击
            if (pet.node.active) {
                this.onPetClicked(pet, index);
            }
        }, this);
    }

    /**
     * 精灵点击事件处理
     * @param clickedPet 被点击的精灵
     * @param petIndex 宠物索引
     */
    private onPetClicked(clickedPet: Sprite, petIndex: number) {
        // 如果点击的是已选中的精灵，则不做处理
        if (this.currentSelectedPet === clickedPet) {
            return;
        }

        // 检查宠物数据是否存在
        if (petIndex >= this.carryPets.length) {
            console.warn("⚠️ 点击的宠物索引超出范围");
            return;
        }

        const selectedPet = this.carryPets[petIndex];
        
        console.log(`🎯 选择宠物: ${selectedPet.name} (ID: ${selectedPet.petNo})`);

        // 重置所有精灵到正常状态
        this.resetAllPets();

        // 为当前点击的精灵应用炫酷效果
        this.applyClickEffect(clickedPet);

        // 设置当前选中的精灵
        this.currentSelectedPet = clickedPet;
        
        // 🔧 关键：更新Global.currentPetId为选中宠物的petNo
        Global.currentPetId = selectedPet.petNo;
        console.log(`✅ 已更新Global.currentPetId = ${Global.currentPetId}`);
        
        // 🆕 更新Global.petName为选中宠物的名称
        Global.petName = selectedPet.name;
        console.log(`✅ 已更新Global.petName = "${Global.petName}"`);
    }

    /**
     * 应用炫酷的点击效果
     * @param pet 要应用效果的精灵
     */
    private applyClickEffect(pet: Sprite) {
        if (!pet || !pet.node) return;

        // 停止之前的动画
        Tween.stopAllByTarget(pet.node);

        // 设置发光效果（改变颜色为亮黄色）
        pet.color = new Color(255, 255, 100, 255);

        // 缩放动画效果
        const originalScale = pet.node.scale.clone();
        
        tween(pet.node)
            .to(0.1, { scale: originalScale.clone().multiplyScalar(1.2) }) // 放大到1.2倍
            .to(0.1, { scale: originalScale.clone().multiplyScalar(1.1) }) // 缩小到1.1倍
            .repeatForever() // 持续缩放效果
            .start();
    }

    /**
     * 重置所有精灵到正常状态
     */
    private resetAllPets() {
        this.petSprites.forEach(pet => {
            if (pet && pet.node && pet.node.active) {
                // 停止所有动画
                Tween.stopAllByTarget(pet.node);
                
                // 重置颜色为正常白色
                pet.color = new Color(255, 255, 255, 255);
                
                // 重置缩放为原始大小 (1, 1, 1)
                tween(pet.node)
                    .to(0.2, { scale: new Vec3(1, 1, 1) })
                    .start();
            }
        });
    }

    /**
     * 获取当前选中的精灵
     */
    public getCurrentSelectedPet(): Sprite | null {
        return this.currentSelectedPet;
    }
    
    /**
     * 获取当前选中的宠物信息
     */
    public getCurrentSelectedPetInfo(): PetInfo | null {
        if (!this.currentSelectedPet) return null;
        
        const selectedIndex = this.petSprites.indexOf(this.currentSelectedPet);
        if (selectedIndex !== -1 && selectedIndex < this.carryPets.length) {
            return this.carryPets[selectedIndex];
        }
        
        return null;
    }

    /**
     * 手动选择精灵（供外部调用）
     * @param petIndex 精灵索引 (0, 1, 2)
     */
    public selectPet(petIndex: number) {
        if (petIndex < 0 || petIndex >= this.petSprites.length) {
            console.warn('无效的精灵索引:', petIndex);
            return;
        }
        
        const petSprite = this.petSprites[petIndex];
        if (petSprite && petSprite.node.active) {
            this.onPetClicked(petSprite, petIndex);
        } else {
            console.warn('指定索引的宠物不可用:', petIndex);
        }
    }
    
    /**
     * 刷新宠物列表（供外部调用）
     */
    public async refreshPets(): Promise<void> {
        console.log("🔄 刷新宠物列表...");
        await this.loadCarryPets();
    }
    
    /**
     * 🆕 刷新地图数据（供外部调用）
     */
    public async refreshMapData(): Promise<void> {
        console.log("🔄 刷新地图数据...");
        await this.loadMapDetail();
    }
    
    /**
     * 🆕 刷新所有数据（宠物+地图）
     */
    public async refreshAllData(): Promise<void> {
        console.log("🔄 刷新所有数据（宠物+地图）...");
        
        // 🔧 重置当前选中状态
        this.currentSelectedPet = null;
        Global.currentPetId = 0;
        console.log("🔄 已重置宠物选中状态");
        
        // 🆕 重置宠物名称
        Global.petName = "";
        console.log("🔄 已重置Global.petName");
        
        // 🔧 重置所有宠物的视觉效果
        this.resetAllPets();
        
        // 🔧 禁用战斗按钮，等待用户重新选择宠物
        this.updateFightButtonState(false);
        
        await Promise.all([
            this.loadMapDetail(),
            this.loadCarryPets()
        ]);
        console.log("✅ 所有数据刷新完成");
    }

    /**
     * 🆕 组件销毁时清理事件监听器
     */
    onDestroy() {
        console.log("🗑️ fightState组件销毁，开始清理...");
        
        // 清理宠物点击事件监听器
        this.petSprites.forEach((pet, index) => {
            if (pet && pet.node) {
                pet.node.off(Input.EventType.TOUCH_START);
            }
        });
        
        // 清理战斗按钮事件监听器
        if (this.btn_fight && this.btn_fight.node) {
            this.btn_fight.node.off(Input.EventType.TOUCH_START, this.onFight, this);
            this.btn_fight.node.off(Input.EventType.TOUCH_END, this.onButtonTouchEnd, this);
            this.btn_fight.node.off(Input.EventType.TOUCH_CANCEL, this.onButtonTouchCancel, this);
        }
        
        // 清理状态
        this.currentSelectedPet = null;
        this.carryPets = [];
        this.currentMapDetail = null;
        this.mapMonsters = [];
        this.mapDrops = [];
        
        console.log("✅ fightState组件清理完成");
    }
}