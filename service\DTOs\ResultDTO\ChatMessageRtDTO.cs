﻿namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 定义一个聊天消息类
    /// </summary>
    public class ChatMessageRtDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 玩家Id
        /// </summary>
        public int playerId { get; set; }

        /// <summary>
        /// 账号
        /// </summary>
        public string number { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string password { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; }
    }
}
