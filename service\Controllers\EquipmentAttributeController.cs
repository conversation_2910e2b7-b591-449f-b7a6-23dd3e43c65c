using Microsoft.AspNetCore.Mvc;
using WebApplication_HM.Interface;
using WebApplication_HM.DTOs;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Services;

namespace WebApplication_HM.Controllers
{
    /// <summary>
    /// 装备属性计算控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class EquipmentAttributeController : ControllerBase
    {
        private readonly IEquipmentAttributeService _equipmentAttributeService;
        private readonly EnhancedPlayerService _enhancedPlayerService;
        private readonly ILogger<EquipmentAttributeController> _logger;

        public EquipmentAttributeController(
            IEquipmentAttributeService equipmentAttributeService,
            EnhancedPlayerService enhancedPlayerService,
            ILogger<EquipmentAttributeController> logger)
        {
            _equipmentAttributeService = equipmentAttributeService;
            _enhancedPlayerService = enhancedPlayerService;
            _logger = logger;
        }

        /// <summary>
        /// 获取宠物的装备属性加�?        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>装备属性加�?/returns>
        [HttpGet("pet/{petId}/equipment-attributes")]
        public async Task<ActionResult<ApiResult<Dictionary<string, double>>>> GetPetEquipmentAttributes(int petId)
        {
            try
            {
                var attributes = await _equipmentAttributeService.CalculateEquipmentAttributesAsync(petId);
                return Ok(ApiResult<Dictionary<string, double>>.CreateSuccess(attributes));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取宠物装备属性失败，宠物ID: {PetId}", petId);
                return StatusCode(500, ApiResult<Dictionary<string, double>>.CreateError("获取装备属性失败"));
            }
        }

        /// <summary>
        /// 获取单件装备的属性加�?        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns>装备属性加�?/returns>
        [HttpGet("equipment/{userEquipmentId}/attributes")]
        public async Task<ActionResult<ApiResult<Dictionary<string, double>>>> GetEquipmentAttributes(int userEquipmentId)
        {
            try
            {
                var attributes = await _equipmentAttributeService.CalculateEquipmentAttributeAsync(userEquipmentId);
                return Ok(ApiResult<Dictionary<string, double>>.CreateSuccess(attributes));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取装备属性失败，装备ID: {EquipmentId}", userEquipmentId);
                return StatusCode(500, ApiResult<Dictionary<string, double>>.CreateError("获取装备属性失败"));
            }
        }

        /// <summary>
        /// 获取详细的属性计算结�?        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <returns>详细属性计算结�?/returns>
        [HttpGet("pet/{petId}/detailed-attributes")]
        public async Task<ActionResult<ApiResult<AttributeCalculationResult>>> GetDetailedAttributes(int petId)
        {
            try
            {
                var result = await _equipmentAttributeService.GetDetailedAttributeCalculationAsync(petId);
                return Ok(ApiResult<AttributeCalculationResult>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取详细属性计算失败，宠物ID: {PetId}", petId);
                return StatusCode(500, ApiResult<AttributeCalculationResult>.CreateError("获取详细属性失败"));
            }
        }

        /// <summary>
        /// 获取增强的宠物属性（包含装备加成�?        /// </summary>
        /// <param name="request">属性请�?/param>
        /// <returns>增强的属性结�?/returns>
        [HttpPost("enhanced-attributes")]
        public async Task<ActionResult<ApiResult<AttributeResultDTO>>> GetEnhancedAttributes(
            [FromBody] AttributeRequestDTO request)
        {
            try
            {
                var attributes = await _enhancedPlayerService.GetPetAttributesEnhancedAsync(request);
                return Ok(ApiResult<AttributeResultDTO>.CreateSuccess(attributes));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取增强属性失败");
                return StatusCode(500, ApiResult<AttributeResultDTO>.CreateError("获取增强属性失败"));
            }
        }

        /// <summary>
        /// 增强的战斗计算（包含装备属性）
        /// </summary>
        /// <param name="request">战斗请求</param>
        /// <returns>战斗结果</returns>
        [HttpPost("enhanced-battle")]
        public async Task<ActionResult<ApiResult<BattleResultDTO>>> EnhancedBattleCalculate(
            [FromBody] BattleRequestDTO request)
        {
            try
            {
                var result = await _enhancedPlayerService.BattleCalculateEnhancedAsync(request);
                return Ok(ApiResult<BattleResultDTO>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "增强战斗计算失败");
                return StatusCode(500, ApiResult<BattleResultDTO>.CreateError("增强战斗计算失败"));
            }
        }

        /// <summary>
        /// 计算装备属性值预�?        /// </summary>
        /// <param name="request">计算请求</param>
        /// <returns>属性�?/returns>
        [HttpPost("calculate-value")]
        public ActionResult<ApiResult<double>> CalculateEquipmentValue(
            [FromBody] EquipmentValueCalculateRequest request)
        {
            try
            {
                var value = _equipmentAttributeService.CalculateEquipmentValue(
                    request.BaseValue, request.StrengthenLevel, 
                    request.ElementBonus, request.IsMainAttribute);
                
                return Ok(ApiResult<double>.CreateSuccess(value));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算装备属性值失败");
                return StatusCode(500, ApiResult<double>.CreateError("计算失败"));
            }
        }

        /// <summary>
        /// 获取属性对比（装备前后对比�?        /// </summary>
        /// <param name="petId">宠物ID</param>
        /// <param name="userEquipmentId">要装备的装备ID</param>
        /// <returns>属性对比结�?/returns>
        [HttpGet("pet/{petId}/compare/{userEquipmentId}")]
        public async Task<ActionResult<ApiResult<AttributeCompareResult>>> CompareAttributes(
            int petId, int userEquipmentId)
        {
            try
            {
                // 1. 获取当前属性
                var currentAttributes = await _equipmentAttributeService.CalculateEquipmentAttributesAsync(petId);

                // 2. 获取要装备的装备属性
                var equipmentAttributes = await _equipmentAttributeService.CalculateEquipmentAttributeAsync(userEquipmentId);

                // 3. 计算差值
                var comparison = new AttributeCompareResult
                {
                    CurrentAttributes = currentAttributes,
                    NewEquipmentAttributes = equipmentAttributes,
                    AttributeDifferences = new Dictionary<string, double>()
                };

                foreach (var attr in currentAttributes.Keys)
                {
                    var currentValue = currentAttributes.GetValueOrDefault(attr, 0);
                    var newValue = equipmentAttributes.GetValueOrDefault(attr, 0);
                    comparison.AttributeDifferences[attr] = newValue - currentValue;
                }

                return Ok(ApiResult<AttributeCompareResult>.CreateSuccess(comparison));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "属性对比失败");
                return StatusCode(500, ApiResult<AttributeCompareResult>.CreateError("属性对比失败"));
            }
        }
    }

    /// <summary>
    /// 装备属性值计算请�?    /// </summary>
    public class EquipmentValueCalculateRequest
    {
        public string BaseValue { get; set; } = "";
        public int StrengthenLevel { get; set; }
        public int ElementBonus { get; set; }
        public bool IsMainAttribute { get; set; }
    }

    /// <summary>
    /// 属性对比结�?    /// </summary>
    public class AttributeCompareResult
    {
        public Dictionary<string, double> CurrentAttributes { get; set; } = new();
        public Dictionary<string, double> NewEquipmentAttributes { get; set; } = new();
        public Dictionary<string, double> AttributeDifferences { get; set; } = new();
    }
}
