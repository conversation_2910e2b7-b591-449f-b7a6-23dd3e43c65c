namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 玩家状态变更DTO
    /// </summary>
    public class PlayerStatusChangeDTO
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        public string Type { get; set; } = "player_status_change";

        /// <summary>
        /// 玩家ID
        /// </summary>
        public int PlayerId { get; set; }

        /// <summary>
        /// 玩家昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 变更类型 (login/logout/status_change)
        /// </summary>
        public string ChangeType { get; set; }

        /// <summary>
        /// 旧状态
        /// </summary>
        public string OldStatus { get; set; }

        /// <summary>
        /// 新状态
        /// </summary>
        public string NewStatus { get; set; }

        /// <summary>
        /// 变更描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangeTime { get; set; } = DateTime.Now;
    }
} 