### Cocos Creator 3.8 场景相关规则文档

#### 一、场景概述
在 Cocos Creator 3.8 中，游戏场景（Scene）是呈现所有游戏内容的载体，而场景文件本身也作为游戏资源存在，并保存了游戏的大部分信息。它是游戏开发时组织游戏内容的中心，也是向玩家展示游戏的基本形态。

#### 二、场景操作
##### （一）创建场景
有三种方式可以创建场景：
1. **资源管理器右键创建**：在资源管理器中，右键点击需要放置场景文件的文件夹，然后选择“创建” -> “Scene”。这种方式有助于使项目具备良好的文件夹目录结构，强烈推荐使用。
2. **资源管理器创建按钮**：在资源管理器中点击左上角的“+”创建按钮，然后选择“Scene”。
3. **菜单栏新建场景**：在顶部菜单栏中选择“文件” -> “新建场景”，即可在场景编辑器中直接创建一个新场景。但此时资源管理器中不会出现新场景文件，需要在保存场景时弹出的“保存场景”窗口中手动保存场景文件，保存完成后才会在资源管理器的根目录下出现场景文件。

##### （二）保存场景
1. **快捷键保存**：使用快捷键 Ctrl + S（Windows）或 Command + S（MacOS）来快速保存场景。
2. **菜单栏保存**：在顶部菜单栏中选择“文件” -> “保存场景”。

##### （三）切换场景
1. **资源管理器双击切换**：在资源管理器中，通过双击需要切换的场景文件，即可打开该场景。
2. **代码动态切换**：在游戏中动态切换场景，可通过 `director.loadScene()` 等 API 来实现。例如：
```typescript
import { director } from 'cc';
director.loadScene("MyScene");
```
另外，从 v2.4 开始 Asset Bundle 还增加了一种新的加载方式：
```typescript
bundle.loadScene('MyScene', function (err, scene) {     director.runScene(scene); });
```
Asset Bundle 提供的 `loadScene` 只会加载指定 bundle 中的场景，并不会自动运行场景，还需要使用 `director.runScene` 来运行场景。

##### （四）场景资源自动释放
从当前场景切换到下一个场景时，该场景中直接或间接引用到的所有资源，默认都不会主动释放。如果项目中的场景很多，随着新场景的切换，内存占用就会不断上升。除了使用 `assetManager.releaseAsset` 等 API 来精确释放不使用的资源，还可以使用场景的自动释放功能。
设置方法：双击打开场景文件后，在层级管理器选中 `scene` 节点，即可在属性检查器中设置场景是否自动释放。
注意：启用了场景的资源自动释放后，如果在脚本中保存了对该场景的资源的特殊引用（以全局变量、单例、闭包、“动态资源”等形式进行的引用），则当场景切换后，由于资源已经被释放，这些引用可能会变成非法的，有可能引起渲染异常等问题。为了解决这个问题，可以使用 `Asset.addRef` 增加引用计数来锁住这些资源。

#### 三、场景编辑器操作
##### （一）视图操作
场景编辑器包括 3D 和 2D 两种视图，可通过编辑器左上角工具栏的 3D/2D 按钮进行切换。
1. **3D 视图操作**：
    - 鼠标左键 + Alt：以视图中心点为中心旋转。
    - 鼠标中键：平移视图。
    - 空格键 + 鼠标/触摸板拖拽：平移视图。
    - 鼠标滚轮：以视图中心点为中心缩放视图。
    - 鼠标右键 + WASD：摄像机漫游。
    - F 快捷键：摄像机聚焦到当前选中节点。
2. **2D 视图操作**：
    - 鼠标中键：平移视图。
    - 鼠标滚轮：以当前鼠标悬停位置为中心缩放视图。
    - 鼠标右键：平移视图。
    - F 快捷键：摄像机聚焦到当前选中节点。

##### （二）变换工具
场景编辑器通过主窗口工具栏左上角的一系列变换工具来布置场景中的节点，将鼠标悬浮到变换工具上方时会显示相关的提示信息。
1. **移动变换工具**：是打开编辑器时默认处于激活状态的变换工具，也可以通过点击位于主窗口左上角工具栏第一个按钮来激活，或者在使用场景编辑器时按下快捷键 W 激活。选中节点后，节点中心（或锚点所在位置）会出现由红绿两个箭头和蓝色方块组成的移动控制手柄。按住红色箭头拖拽鼠标，将在 x 轴方向上移动节点；按住绿色箭头拖拽鼠标，将在 y 轴方向移动节点；按住蓝色方块拖拽鼠标，可以同时在两个轴向自由移动节点。
2. **旋转变换工具**：点击主窗口左上角工具栏第二个按钮，或在使用场景编辑器时按下 E 快捷键，即可激活。旋转变换工具的手柄主要是一个箭头和一个圆环组成，箭头所指的方向表示当前节点旋转属性的角度。拖拽箭头或圆环内任意一点就可以旋转节点，放开鼠标之前，可以在控制手柄上看到当前旋转属性的角度值。
3. **缩放变换工具**：点击主窗口左上角工具栏第三个按钮，或在使用场景编辑器时按下 R 快捷键，即可激活。按住红色方块拖拽鼠标，在 x 轴方向上缩放节点图像；按住绿色方块拖拽鼠标，在 y 轴方向上缩放节点图像；按住中间的黄色方块，保持宽高比的前提下整体缩放节点图像。缩放节点时，会同比缩放所有的子节点。
4. **矩形变换工具**：点击主窗口左上角工具栏第四个按钮，或在使用场景编辑器时按下 T 快捷键，即可激活。拖拽控制手柄的任一顶点，可以在保持对角顶点位置不变的情况下，同时修改节点尺寸中的 `width` 和 `height` 属性；拖拽控制手柄的任一边，可以在保持对边位置不变的情况下，修改节点尺寸中的 `width` 或 `height` 属性。在 UI 元素的排版中，经常会需要使用矩形变换工具直接精确控制节点四条边的位置和长度。而对于必须保持原始图片宽高比的图像元素，通常不会使用矩形变换工具来调整尺寸。

#### 四、场景相关类与接口
##### （一）Node 类
`Node` 类是 Cocos Creator 场景中的所有节点类，具有层级关系，持有各类组件，维护 3D 空间左边变换（坐标、旋转、缩放）信息。以下是一些常用的属性和方法：
1. **属性**：
    - `position`：本地坐标系下的坐标。
    - `rotation`：本地坐标系下的旋转，用四元数表示。
    - `scale`：本地坐标系下的缩放。
    - `active`：当前节点的自身激活状态。值得注意的是，一个节点的父节点如果不被激活，那么即使它自身设为激活，它仍然无法激活。可以使用 `activeInHierarchy` 检查节点在场景中实际的激活状态。
    - `parent`：父节点。
    - `children`：节点的所有子节点。
2. **方法**：
    - `walk`：遍历该节点的子树里的所有节点并按规则执行回调函数。
    - `addComponent`：为节点添加组件。
    - `getComponent`：获取节点上的组件。

##### （二）SceneBuilder 接口
`SceneBuilder` 接口用于构建场景，需要绘制的场景内容。以下是一些常用的方法：
1. **setBuiltinCameraConstants**：设置内置相机常量，例如 `cc_matView`。具体常量见 `cc-global.chunk` 中的 `CCCamera`。
2. **setBuiltinDirectionalLightConstants**：设置内置方向光与阴影常量。具体常量见 `cc-shadow.chunk` 中的 `CCShadow` 与 `cc-global.chunk` 中的 `CCCamera`。
3. **setColor**：设置颜色值，常量（uniform）有 4 个 float (16 bytes)。

#### 五、场景渲染相关
Cocos Creator 3.8 在渲染方面有诸多增强，例如在 3.8.4 版本中：
1. **可定制渲染管线 CRP**：基于 `RenderGraph` 的可定制渲染管线 CRP 正式上线。新项目默认管线为新管线；旧项目如果未使用自定义管线，则使用原渲染管线；旧项目如果使用了自定义管线，则使用新渲染管线。基于 CRP 管线，开发者能够在不修改引擎源码的情况下编写全平台兼容的渲染流程，还能根据项目的需求定制渲染流程，删除不必要渲染过程，节省开销提升性能。
2. **渲染性能提升**：特别是小游戏平台，不管是新管线还是旧管线，相比 3.8.0 版本，性能都有大幅度提升。
3. **支持 WebGPU**：WebGPU 作为全新一代的 API，不仅能够显著提升渲染效率，更能在网页环境中充分发挥 GPU 的通用计算能力。
4. **Tone Mapping 模式切换**：在 Scene 面板上，增加了 `PostSettings` 组件，`Tone Mapping Type` 可以选择 `DEFAULT` 和 `LINEAR` 两种选项。`DEFAULT` 采用 ACES 算法做 Tone Map，会将线性空间转换到 gamma 空间后再输出，整个画面的明暗对比度会更强，适用于追求真实的 PBR 效果的渲染需求；`LINEAR` 会将 `LINEAR` 空间中的像素直接输出，画面明暗对比度比 `DEFAULT` 弱，会保留图片原色，适用于想要 1:1 呈现美术资产颜色的情况，如无光照卡通，电商展品等。
5. **新增材质**：新增了钢琴烤漆、车漆和玻璃材质。

#### 六、场景中的常驻节点
引擎同时只会运行一个场景，当切换场景时，默认会将场景内所有节点和其他实例销毁。如果需要用一个组件控制所有场景的加载，或在场景之间传递参数数据，就需要将该组件所在节点标记为「常驻节点」，使它在场景切换时不被自动销毁，常驻内存。使用以下接口：
```typescript
import { director } from 'cc';
director.addPersistRootNode(myNode);
```
取消一个节点的常驻属性：
```typescript
director.removePersistRootNode(myNode);
```
需要注意的是，目标节点必须为位于层级的根节点，否则设置无效。并且上述 API 并不会立即销毁指定节点，只是将节点还原为可在场景切换时销毁的节点。

#### 七、场景加载回调
加载场景时，可以附加一个参数用来指定场景加载后的回调函数：
```typescript
import { director } from 'cc';

function onSceneLaunched() {
    // 场景加载后的初始化或数据传递操作
}

director.loadScene("MyScene", onSceneLaunched);
```
由于回调函数只能写在本脚本中，所以场景加载回调通常用来配合常驻节点，在常驻节点上挂载的脚本中使用。

#### 八、预加载场景
`director.loadScene` 会在加载场景之后自动切换运行新场景，有些时候需要在后台静默加载新场景，并在加载完成后手动进行切换。可以预先使用 `preloadScene` 接口对场景进行预加载：
```typescript
import { director } from 'cc';

director.preloadScene("table", function () {    console.log('Next scene preloaded');});
```
之后在合适的时间调用 `loadScene`，就可以真正切换场景：
```typescript
director.loadScene("table");
```
就算预加载还没完成，也可以直接调用 `director.loadScene`，预加载完成后场景就会启动。