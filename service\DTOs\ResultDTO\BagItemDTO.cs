namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 背包物品信息DTO
    /// </summary>
    public class BagItemDTO
    {
        /// <summary>
        /// 道具ID
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// 道具编号
        /// </summary>
        public int ItemNo { get; set; }

        /// <summary>
        /// 道具名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 道具类型
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 道具描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 道具品质
        /// </summary>
        public string Quality { get; set; }

        /// <summary>
        /// 道具图标
        /// </summary>
        public string Icon { get; set; }

        /// <summary>
        /// 道具价格
        /// </summary>
        public int Price { get; set; }

        /// <summary>
        /// 拥有数量
        /// </summary>
        public long ItemCount { get; set; }

        /// <summary>
        /// 道具位置
        /// </summary>
        public int? ItemPos { get; set; }

        /// <summary>
        /// 道具序号
        /// </summary>
        public int ItemSeq { get; set; }

        /// <summary>
        /// 是否可使用
        /// </summary>
        public bool CanUse { get; set; }

        /// <summary>
        /// 是否可出售
        /// </summary>
        public bool CanSell { get; set; }
    }
} 