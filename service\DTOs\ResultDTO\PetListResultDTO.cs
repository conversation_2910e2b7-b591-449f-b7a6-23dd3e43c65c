using System.Collections.Generic;

namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 宠物列表结果DTO
    /// </summary>
    public class PetListResultDTO
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 宠物列表
        /// </summary>
        public List<PetInfoDTO> Pets { get; set; } = new List<PetInfoDTO>();

        /// <summary>
        /// 主战宠物ID
        /// </summary>
        public int? MainPetId { get; set; }

        /// <summary>
        /// 查询结果总数量
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 牧场宠物数量
        /// </summary>
        public int RanchCount { get; set; }

        /// <summary>
        /// 携带宠物数量
        /// </summary>
        public int CarryCount { get; set; }
    }

    /// <summary>
    /// 宠物信息DTO
    /// </summary>
    public class PetInfoDTO
    {
        /// <summary>
        /// 宠物ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 宠物序号
        /// </summary>
        public int PetNo { get; set; }

        /// <summary>
        /// 宠物名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 属性/元素
        /// </summary>
        public string Element { get; set; } = "";

        /// <summary>
        /// 等级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        public long Exp { get; set; }

        /// <summary>
        /// 生命值
        /// </summary>
        public long Hp { get; set; }

        /// <summary>
        /// 魔法值
        /// </summary>
        public long Mp { get; set; }

        /// <summary>
        /// 成长值
        /// </summary>
        public decimal Growth { get; set; }

        /// <summary>
        /// 境界
        /// </summary>
        public string Realm { get; set; } = "";

        /// <summary>
        /// 是否为主战宠物（通过用户表main_pet_id判断）
        /// </summary>
        public bool IsMainPet { get; set; }

        /// <summary>
        /// 宠物状态（牧场、携带、丢弃）
        /// </summary>
        public string Status { get; set; } = "";

        /// <summary>
        /// 是否主战宠物标识（宠物表自身字段）
        /// </summary>
        public bool IsMain { get; set; }
    }
} 