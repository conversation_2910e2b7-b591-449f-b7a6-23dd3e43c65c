using System.Text.Json;
using System.Net.WebSockets;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using WebApplication_HM.Utils;


namespace WebApplication_HM.Services.SocketInfo;

//示例消息处理器
public class ChatMessageHandler : WebSocketHandler
{
    private readonly ILogger<ChatMessageHandler> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;


    // 构造函数，传入WebSocketConnectionManager
    public ChatMessageHandler(
        IServiceScopeFactory serviceScopeFactory,
        WebSocketConnectionManager webSocketConnectionManager,
        ILogger<ChatMessageHandler> logger) : base(webSocketConnectionManager)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;
    }

    // 当有新的WebSocket连接时调用
    public override async Task OnConnected(WebSocket socket)
    {
        await base.OnConnected(socket);
        _logger.LogInformation($"WebSocket连接已建立");
    }

    // 添加新方法：根据WebSocket实例获取connectionId
    public string GetConnectionId(System.Net.WebSockets.WebSocket socket)
    {
        return WebSocketConnectionManager
            .GetAll()
            .FirstOrDefault(x => x.Value.Socket == socket)
            .Key;
    }

    // 处理接收到的消息
    // 异步处理传入消息
    public async Task HandleIncomingMessage(string connectionId, string message)
    {
        try
        {
            // 将消息反序列化为ChatMessageDTO对象
            var messageObj = JsonSerializer.Deserialize<ChatMessageDTO>(message);
         
            // 根据消息类型进行不同的处理
            switch (messageObj.Type)
            {  
                case "chat":

                    // 群发消息
                    await SendMessageToAllAsync($"{messageObj.number}: {messageObj.Content}");

                    break;

                case "private":

                    //私有消息： 给指定用户发消息，TargetUserId（对方id） number(本人账号)  Content（发送内容）
                    if (!string.IsNullOrEmpty(messageObj.TargetUserId))
                    {
                        // 获取目标用户的连接
                        var targetConnection = WebSocketConnectionManager.GetAll()
                            .FirstOrDefault(x => x.Value.ConnectionId == messageObj.TargetUserId);

                        // 如果目标用户存在连接
                        if (!string.IsNullOrEmpty(targetConnection.Key))
                        {
                            // 将消息发送给目标用户
                            await SendMessageAsync(targetConnection.Key,
                                $"Private message from {messageObj.number}: {messageObj.Content}");
                        }
                    }

                    break;

                case "Login":

                    //// 登录功能
                    //using (var scope = _serviceScopeFactory.CreateScope())
                    //{
                    //    // 获取玩家服务
                    //    var playerService = scope.ServiceProvider.GetRequiredService<IPlayerService>();

                    //    // 验证必要参数
                    //    if (string.IsNullOrEmpty(messageObj.number) || string.IsNullOrEmpty(messageObj.password))
                    //    {
                    //        // 如果账号或密码为空，发送错误消息
                    //        await SendMessageAsync(connectionId, JsonSerializer.Serialize(new ChatMessageRtDTO
                    //        {
                    //            Type = "Login",
                    //            Content = "账号或密码不能为空"
                    //        }));
                    //        return;
                    //    }

                        
                    //    // 调用登录服务
                    //    var loginResult = await playerService.LoginAsyncs(messageObj.number,DataProcessHelper.MD5Hash(messageObj.password));

                    //    // 登录失败处理
                    //    if (!loginResult.Success || loginResult.PlayerInfo == null)
                    //    {
                    //        // 如果登录失败，发送错误消息
                    //        await SendMessageAsync(connectionId, JsonSerializer.Serialize(new ChatMessageRtDTO
                    //        {
                    //            Type = "Login",
                    //            Content = loginResult.Message
                    //        }));
                    //        return;
                    //    }

                    //    // 登录成功，检查是否已经有该玩家的其他连接
                    //    var existingConnections = WebSocketConnectionManager.GetConnectionsByPlayerId(loginResult.PlayerInfo.uid);

                    //    if (existingConnections != null && existingConnections.Any())
                    //    {
                    //        // 如果有其他连接，强制断开
                    //        foreach (var existingConn in existingConnections)
                    //        {
                    //            if (existingConn.IsConnected)
                    //            {
                    //                await SendMessageAsync(existingConn.ConnectionId, JsonSerializer.Serialize(new ChatMessageRtDTO
                    //                {
                    //                    Type = "Login",
                    //                    Content = "disconnect"
                    //                }));

                    //                existingConn.IsConnected = false;
                    //                WebSocketConnectionManager.RemoveSocket(existingConn.ConnectionId);
                    //                _logger.LogInformation($"玩家已在其他设备登录，强制断开旧连接，PlayerId: {loginResult.PlayerInfo.uid}, OldConnectionId: {existingConn.ConnectionId}");
                    //            }
                    //        }
                    //    }

                    //    // 绑定玩家信息
                    //    await BindPlayerInfo(connectionId, loginResult.PlayerInfo.uid, loginResult.PlayerInfo.name);

                    //    // 发送登录成功消息
                    //    await SendMessageAsync(connectionId, JsonSerializer.Serialize(new ChatMessageRtDTO
                    //    {
                    //        Type = "Login",
                    //        Content = "success",
                    //        playerId = loginResult.PlayerInfo.uid,
                    //        Name = loginResult.PlayerInfo.name
                    //    }));

                    //    _logger.LogInformation($"玩家登录成功，PlayerId: {loginResult.PlayerInfo.uid}, ConnectionId: {connectionId}");
                    //}

                    break;

                default:

                    // 如果消息类型不支持，发送错误消息
                    await SendMessageAsync(connectionId, "该消息类型不支持");

                    break;
            }
        }
        catch (JsonException)
        {
            // 如果消息格式无效，发送错误消息
            await SendMessageAsync(connectionId, "Invalid message format");
        }
        catch (Exception ex)
        {
            // 如果处理消息时发生错误，发送错误消息
            await SendMessageAsync(connectionId, $"Error processing message: {ex.Message}");
        }
    }

    // 添加新方法用于绑定玩家信息到连接
    public async Task BindPlayerInfo(string connectionId, int playerId, string playerName)
    {
        await WebSocketConnectionManager.BindPlayerToConnection(connectionId, playerId, playerName);
        _logger.LogInformation($"玩家信息已绑定到连接，ConnectionId: {connectionId}, PlayerId: {playerId}, PlayerName: {playerName}");
    }

}

