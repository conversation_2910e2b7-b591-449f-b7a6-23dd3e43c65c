namespace WebApplication_HM.DTOs.ResultDTO
{
    /// <summary>
    /// 注册结果DTO
    /// </summary>
    public class RegisterResultDTO
    {
        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 返回消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 新创建的用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户账号
        /// </summary>
        public string Account { get; set; } = string.Empty;

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string Nickname { get; set; } = string.Empty;
    }
} 