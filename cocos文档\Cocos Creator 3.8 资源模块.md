# Cocos Creator 3.8 资源模块相关接口规则文档

## 一、命名空间
### 1. AssetManager
该命名空间涵盖了与资源管理核心功能相关的内容，如资源的加载、释放、缓存管理等操作，为开发者提供了一套完整的资源管理解决方案。

### 2. EffectAsset
此命名空间主要与特效资源相关，包含了特效资源的定义、管理以及与特效资源交互的方法和工具。

## 二、类
### 1. Asset
#### 说明
`Asset` 是 Creator 中的资源基类，其他具体的资源类都继承自它。开发者可能需要根据具体需求重写一些方法，如 `createNode` 用于创建与资源关联的节点，`_nativeAsset` 的 `getset` 方法用于管理原生资源，`Object._serialize` 和 `Object._deserialize` 用于资源的序列化和反序列化操作。
#### 使用方式
```typescript
import { Asset } from 'cc';

class CustomAsset extends Asset {
    // 重写 createNode 方法
    createNode(callback: (error: Error | null, node: any) => void) {
        // 实现创建节点的逻辑
        const node = new cc.Node();
        callback(null, node);
    }
}
```

### 2. AssetManager
#### 说明
这是一个单例类，负责管理资源的行为和信息，包括资源的加载、释放等操作。开发者可以通过 `assetManager` 全局单例来访问它。
#### 使用方式
```typescript
import { assetManager } from 'cc';

// 加载资源
assetManager.loadRemote('https://example.com/image.png', (err, asset) => {
    if (err) {
        console.error('资源加载失败:', err);
    } else {
        console.log('资源加载成功:', asset);
    }
});
```

### 3. BufferAsset
#### 说明
`BufferAsset` 是一类资产，其内部数据是一段内存缓冲。开发者可以通过 `buffer` 函数获取其内部数据。
#### 使用方式
```typescript
import { BufferAsset } from 'cc';

// 假设从某个地方获取到了 buffer 数据
const buffer = new ArrayBuffer(1024);
const bufferAsset = new BufferAsset();
bufferAsset.buffer = buffer;

const data = bufferAsset.buffer;
console.log('Buffer 数据:', data);
```

### 4. BuiltinResMgr
暂未获取到详细说明，推测该类用于管理内置资源，可能涉及内置资源的加载、缓存和释放等操作。

### 5. Bundle
#### 说明
一个包含一定数量资源（包括场景）的包，开发者可以对包内的资源进行加载、预加载和释放等操作。
#### 使用方式
```typescript
import { assetManager } from 'cc';

// 加载 bundle
assetManager.loadBundle('myBundle', (err, bundle) => {
    if (err) {
        console.error('Bundle 加载失败:', err);
    } else {
        // 加载 bundle 内的资源
        bundle.load('image', (err, asset) => {
            if (err) {
                console.error('资源加载失败:', err);
            } else {
                console.log('资源加载成功:', asset);
            }
        });
    }
});
```

### 6. Cache
#### 说明
用于缓存某些内容的数据结构，可提高资源的访问效率，避免重复加载。
#### 使用方式
```typescript
import { Cache } from 'cc';

const cache = new Cache();
// 存储数据到缓存
cache.put('key', 'value');
// 从缓存中获取数据
const data = cache.get('key');
console.log('缓存数据:', data);
```

### 7. CacheManager
#### 说明
缓存管理器，在非 WEB 平台上，用于管理所有从服务器上下载下来的缓存。开发者可以通过 `cacheManager` 全局单例来访问它。
#### 使用方式
```typescript
import { cacheManager } from 'cc';

// 清理缓存
cacheManager.clear();
```

### 8. CCLoader
#### 说明
资源加载管理器，引擎会自动创建一个单例对象 `loader`。开发者可以使用该单例进行资源的加载操作。
#### 使用方式
```typescript
import { loader } from 'cc';

// 加载资源
loader.loadRes('image', (err, asset) => {
    if (err) {
        console.error('资源加载失败:', err);
    } else {
        console.log('资源加载成功:', asset);
    }
});
```

### 9. DependUtil
#### 说明
管理资源的依赖列表，是一个单例，开发者可以通过 `dependUtil` 访问它。该类可帮助开发者处理资源之间的依赖关系，确保资源的正确加载和释放。
#### 使用方式
```typescript
import { dependUtil } from 'cc';

// 获取资源的依赖列表
const dependencies = dependUtil.getDeps('resourcePath');
console.log('资源依赖列表:', dependencies);
```

### 10. Downloader
#### 说明
管理所有下载过程，是一个单例，开发者可以通过 `downloader` 访问它。它能下载各种类型的文件。
#### 使用方式
```typescript
import { downloader } from 'cc';

downloader.download('https://example.com/file.txt', (err, data) => {
    if (err) {
        console.error('文件下载失败:', err);
    } else {
        console.log('文件下载成功:', data);
    }
});
```

### 11. EffectAsset
#### 说明
`Effect` 资源，作为材质实例初始化的模板，每个 `effect` 资源都应是全局唯一的。所有 `Effect` 资源都由此类的一个静态对象管理。
#### 使用方式
```typescript
import { EffectAsset } from 'cc';

// 假设已经有一个 effect 资源的 url
const effectUrl = 'effects/myEffect.effect';
EffectAsset.load(effectUrl, (err, effect) => {
    if (err) {
        console.error('Effect 资源加载失败:', err);
    } else {
        console.log('Effect 资源加载成功:', effect);
    }
});
```

### 12. ImageAsset
#### 说明
图像资源，存储了图像的原始数据。开发者可以使用此资源来创建任意 `TextureBase` 资源。
#### 使用方式
```typescript
import { ImageAsset, Texture2D } from 'cc';

// 假设已经有一个 ImageAsset 实例
const imageAsset = new ImageAsset();
const texture = new Texture2D();
texture.image = imageAsset;
```

### 13. JavaScript
#### 说明
`JavaScript` 脚本资源，用于管理和加载 `JavaScript` 脚本文件。
#### 使用方式
```typescript
import { loader } from 'cc';

// 加载 JavaScript 脚本资源
loader.loadRes('scripts/myScript.js', (err, script) => {
    if (err) {
        console.error('脚本资源加载失败:', err);
    } else {
        console.log('脚本资源加载成功:', script);
    }
});
```

### 14. JsonAsset
#### 说明
`Json` 资源，加载后将直接解析为对象。如果开发者希望获得 `JSON` 的原始文本，需要使用文本资源（使用文件名后缀 `.txt`）。
#### 使用方式
```typescript
import { loader } from 'cc';

// 加载 Json 资源
loader.loadRes('data/myData.json', (err, jsonAsset) => {
    if (err) {
        console.error('Json 资源加载失败:', err);
    } else {
        const jsonData = jsonAsset.json;
        console.log('Json 数据:', jsonData);
    }
});
```

### 15. Material
#### 说明
材质资源类，包含模型绘制方式的全部细节描述。开发者可以通过创建和配置 `Material` 实例来控制模型的渲染效果。
#### 使用方式
```typescript
import { Material } from 'cc';

// 创建一个新的材质实例
const material = new Material();
// 设置材质的属性
material.setProperty('color', cc.Color.WHITE);
```

### 16. Parser
#### 说明
解析已下载的文件，是一个单例，开发者可以通过 `parser` 访问它。该类可将下载的文件解析为可用的资源对象。
#### 使用方式
```typescript
import { parser } from 'cc';

// 假设已经下载了一个文件
const fileData = new ArrayBuffer(1024);
const parsedData = parser.parse(fileData, 'fileType');
console.log('解析后的数据:', parsedData);
```

### 17. Pipeline
#### 说明
加载管线能通过执行一系列阶段来完成加载任务。`AssetManager` 使用其来加载所有资源。开发者可以通过配置加载管线来定制资源的加载流程。
#### 使用方式
```typescript
import { Pipeline } from 'cc';

// 创建一个加载管线实例
const pipeline = new Pipeline();
// 添加阶段到管线
pipeline.insertStage('stageName', (task, done) => {
    // 执行阶段逻辑
    done();
});
```

### 18. RenderingSubMesh
#### 说明
包含所有顶点数据的渲染子网格，可以用来创建 `InputAssembler`。该类在处理模型的渲染时非常有用。
#### 使用方式
```typescript
import { RenderingSubMesh, InputAssembler } from 'cc';

// 创建一个 RenderingSubMesh 实例
const subMesh = new RenderingSubMesh();
// 使用 RenderingSubMesh 创建 InputAssembler
const inputAssembler = new InputAssembler();
inputAssembler.setSubMesh(subMesh);
```

### 19. RenderTexture
#### 说明
渲染贴图是 `Camera` 或 `Canvas` 组件的渲染目标对象，渲染管线会使用它的 `RenderWindow` 作为渲染的目标窗口。开发者可以使用 `RenderTexture` 实现一些特殊的渲染效果，如实时反射、阴影等。
#### 使用方式
```typescript
import { RenderTexture, Camera } from 'cc';

// 创建一个 RenderTexture 实例
const renderTexture = new RenderTexture();
// 设置 Camera 的渲染目标为 RenderTexture
const camera = this.node.getComponent(Camera);
camera.targetTexture = renderTexture;
```

### 20. RequestItem
#### 说明
请求的相关信息集合，包含了资源请求的各种参数和配置。
#### 使用方式
```typescript
import { RequestItem } from 'cc';

// 创建一个 RequestItem 实例
const requestItem = new RequestItem();
requestItem.url = 'https://example.com/resource.png';
requestItem.type = 'image';
```

### 21. SceneAsset
#### 说明
场景资源类，用于管理和加载场景资源。开发者可以通过加载 `SceneAsset` 来切换游戏场景。
#### 使用方式
```typescript
import { assetManager } from 'cc';

// 加载场景资源
assetManager.loadScene('myScene', (err, scene) => {
    if (err) {
        console.error('场景资源加载失败:', err);
    } else {
        console.log('场景资源加载成功:', scene);
    }
});
```

### 22. Script
#### 说明
脚本资源基类，其他具体的脚本资源类（如 `JavaScript`、`TypeScript`）继承自它。
#### 使用方式
```typescript
import { Script } from 'cc';

class CustomScript extends Script {
    // 实现脚本逻辑
    start() {
        console.log('脚本开始执行');
    }
}
```

### 23. Task
#### 说明
任务是在管线中运行的最小数据单位，开发者可以创建一个任务并传入输入信息，在经过管线执行后获取该任务的输出来使用。
#### 使用方式
```typescript
import { Task, Pipeline } from 'cc';

// 创建一个任务
const task = new Task({
    input: 'inputData',
    output: null
});

// 创建一个管线
const pipeline = new Pipeline();
pipeline.insertStage('stage', (task, done) => {
    task.output = task.input + ' processed';
    done();
});

// 执行任务
pipeline.async(task, (err, result) => {
    if (err) {
        console.error('任务执行失败:', err);
    } else {
        console.log('任务执行结果:', result);
    }
});
```

### 24. TextAsset
#### 说明
文本资源，用于存储和加载文本文件。
#### 使用方式
```typescript
import { loader } from 'cc';

// 加载文本资源
loader.loadRes('texts/myText.txt', (err, textAsset) => {
    if (err) {
        console.error('文本资源加载失败:', err);
    } else {
        const text = textAsset.text;
        console.log('文本内容:', text);
    }
});
```

### 25. Texture2D
#### 说明
二维贴图资源，每个 `Mipmap` 层级都为一张 `ImageAsset`。开发者可以使用 `Texture2D` 来创建纹理，用于模型的材质。
#### 使用方式
```typescript
import { Texture2D, ImageAsset } from 'cc';

// 创建一个 ImageAsset 实例
const imageAsset = new ImageAsset();
// 创建一个 Texture2D 实例
const texture = new Texture2D();
texture.image = imageAsset;
```

### 26. TextureCube
#### 说明
立方体贴图资源，每个 `Mipmap` 层级都为 6 张 `ImageAsset`，分别代表了立方体贴图的 6 个面。常用于实现天空盒、反射等效果。
#### 使用方式
```typescript
import { TextureCube, ImageAsset } from 'cc';

// 创建 6 个 ImageAsset 实例
const imageAssets = [
    new ImageAsset(),
    new ImageAsset(),
    new ImageAsset(),
    new ImageAsset(),
    new ImageAsset(),
    new ImageAsset()
];

// 创建一个 TextureCube 实例
const textureCube = new TextureCube();
textureCube.images = imageAssets;
```

### 27. TypeScript
#### 说明
`TypeScript` 脚本资源，用于管理和加载 `TypeScript` 脚本文件。
#### 使用方式
```typescript
import { loader } from 'cc';

// 加载 TypeScript 脚本资源
loader.loadRes('scripts/myScript.ts', (err, script) => {
    if (err) {
        console.error('脚本资源加载失败:', err);
    } else {
        console.log('脚本资源加载成功:', script);
    }
});
```

## 三、接口
### 1. IAttributeInfo
暂未获取到详细说明，推测该接口用于定义属性信息，可能在资源的属性配置和管理中使用。

### 2. IBlockInfo
暂未获取到详细说明，推测该接口用于定义块信息，可能与资源的分块管理或渲染块的配置有关。

### 3. IBufferInfo
暂未获取到详细说明，推测该接口用于定义缓冲区信息，可能在处理资源的内存缓冲时使用。

### 4. IBuiltin
暂未获取到详细说明，推测该接口用于定义内置资源的相关信息，可能与内置资源的加载和使用有关。

### 5. IBuiltinInfo
暂未获取到详细说明，推测该接口用于详细描述内置资源的信息，可能包含内置资源的属性、配置等。

### 6. IDefineInfo
暂未获取到详细说明，推测该接口用于定义资源的定义信息，可能在资源的编译和解析过程中使用。

### 7. IDescriptorInfo
暂未获取到详细说明，推测该接口用于定义描述符信息，可能与资源的描述和元数据有关。

### 8. IImageInfo
暂未获取到详细说明，推测该接口用于定义图像信息，可能包含图像的尺寸、格式、颜色模式等。

### 9. IInputAttachmentInfo
暂未获取到详细说明，推测该接口用于定义输入附件信息，可能在渲染管线的输入阶段使用。

### 10. IMaterialInfo
#### 说明
用来初始化材质的基本信息，包含了材质的各种属性和配置。
#### 使用方式
```typescript
import { IMaterialInfo, Material } from 'cc';

const materialInfo: IMaterialInfo = {
    name: 'myMaterial',
    properties: {
        color: cc.Color.WHITE
    }
};

const material = new Material(materialInfo);
```

### 11. IPassInfo
暂未获取到详细说明，推测该接口用于定义渲染通道信息，可能在渲染管线的通道配置中使用。

### 12. IPassStates
暂未获取到详细说明，推测该接口用于定义渲染通道的状态信息，可能包含通道的启用、禁用、混合模式等。

### 13. IPreCompileInfo
暂未获取到详细说明，推测该接口用于定义预编译信息，可能在资源的预编译阶段使用。

### 14. IPropertyInfo
暂未获取到详细说明，推测该接口用于定义属性信息，可能在资源的属性配置和管理中使用。

### 15. ISamplerInfo
暂未获取到详细说明，推测该接口用于定义采样器信息，可能在纹理采样的配置中使用。

### 16. ISamplerTextureInfo
暂未获取到详细说明，推测该接口用于定义采样器和纹理的关联信息，可能在纹理采样的配置中使用。

### 17. IShaderInfo
暂未获取到详细说明，推测该接口用于定义着色器信息，可能包含着色器的代码、参数等。

### 18. ITechniqueInfo
暂未获取到详细说明，推测该接口用于定义技术信息，可能在渲染技术的配置和管理中使用。

### 19. ITextureInfo
暂未获取到详细说明，推测该接口用于定义纹理信息，可能包含纹理的尺寸、格式、过滤模式等。

## 四、枚举
### BuiltinBundleName
#### 说明
内置 `bundle` 的枚举，定义了引擎内置的 `bundle` 名称，方便开发者使用内置资源。
#### 使用方式
```typescript
import { BuiltinBundleName, assetManager } from 'cc';

// 加载内置 bundle 中的资源
assetManager.loadBundle(BuiltinBundleName.INTERNAL, (err, bundle) => {
    if (err) {
        console.error('内置 bundle 加载失败:', err);
    } else {
        bundle.load('resource', (err, asset) => {
            if (err) {
                console.error('资源加载失败:', err);
            } else {
                console.log('资源加载成功:', asset);
            }
        });
    }
});
```

## 五、变量
### 1. AssetLibrary
#### 说明
管理项目中加载/卸载资源的资源库，开发者可以通过该变量来管理项目中的资源。
#### 使用方式
```typescript
import { AssetLibrary } from 'cc';

// 获取资源库中的资源
const asset = AssetLibrary.getAsset('resourcePath');
console.log('获取到的资源:', asset);
```

### 2. assetManager
#### 说明
`AssetManager` 的全局单例，引擎使用 `assetManager` 来完成所有资源和资源包的管理工作，包括加载、释放等。
#### 使用方式
```typescript
import { assetManager } from 'cc';

// 释放资源
assetManager.releaseAsset('resourcePath');
```

### 3. builtinResMgr
暂未获取到详细说明，推测该变量用于管理内置资源的管理器，可能提供了一些操作内置资源的方法。

### 4. loader
#### 说明
资源加载管理器的单例对象，开发者可以使用该变量进行资源的加载操作。
#### 使用方式
```typescript
import { loader } from 'cc';

// 加载资源
loader.loadResDir('resources', (err, assets) => {
    if (err) {
        console.error('资源加载失败:', err);
    } else {
        console.log('加载的资源数量:', assets.length);
    }
});
```

### 5. resources
#### 说明
一个 `Bundle` 实例，用于管理所有在 `assets/resources` 下的资源。开发者可以通过该变量来加载和管理该目录下的资源。
#### 使用方式
```typescript
import { resources } from 'cc';

// 加载 resources 目录下的资源
resources.load('image', (err, asset) => {
    if (err) {
        console.error('资源加载失败:', err);
    } else {
        console.log('资源加载成功:', asset);
    }
});
```

### 6. url
暂未获取到详细说明，推测该变量用于处理资源的 URL 相关操作，如 URL 的解析、拼接等。

综上所述，Cocos Creator 3.8 的资源模块提供了丰富的接口和工具，通过合理使用这些接口，开发者可以高效地管理和使用游戏中的各种资源。