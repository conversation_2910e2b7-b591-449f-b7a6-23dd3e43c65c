import { _decorator, Component, Node, RichText } from 'cc';
const { ccclass, property } = _decorator;

//战斗输出面板显示脚本
@ccclass('combatInfoPanel')
export class combatInfoPanel extends Component {

    @property(RichText)
    richText: RichText | null = null;

    start() {

        this.richText = this.node.getComponent(RichText);

    }

    update(deltaTime: number) {

    }

    //attack：普通攻击  damage：加深伤害  health：吸取生命
    //isMonsterAttack：是否为怪物攻击（用于给怪物普通攻击加标记）
    public showNotice(attack: number, damage: number, health: number, isMonsterAttack: boolean = false) {
        this.node.active = true;

        if (this.richText) {
            // 清除默认的文本内容  
            this.richText.string = '';

            if (attack > 0) {
                // 🔧 优化：根据攻击者类型显示不同的标识
                const attackPrefix = isMonsterAttack ? '<color=#ff6b00>[怪] </color>' : '<color=#00aaff>[宠] </color>';
                this.richText.string +=
                    attackPrefix +
                    '<color=#00ff00>普通攻击：</color>' + // 设置"普通攻击："为绿色  
                    '<color=#e81e00>' + attack + '</color>\n'; // 设置伤害为红色  
            }

            if (damage > 0) {
                // 🔧 优化：暴击伤害也添加攻击者标识
                const damagePrefix = isMonsterAttack ? '<color=#ff6b00>[怪] </color>' : '<color=#00aaff>[宠] </color>';
                this.richText.string +=
                    damagePrefix +
                    '<color=#fb3bff>暴击伤害：' + damage + '</color>\n'; // 设置"暴击伤害"为紫色
            }
            
            if (health > 0) {
                this.richText.string +=
                    '<color=#63fc01>吸取生命：' + health + '</color>'; // 设置"吸取生命"为绿色
            }
        }
    }

    
    //attack：普通攻击  damage：加深伤害  health：吸取生命
    //isMonsterAttack：是否为怪物攻击（用于给怪物普通攻击加标记）
    public showNotice_1(attack: number, damage: number, health: number, isMonsterAttack: boolean = false) {
        this.node.active = true;

        if (this.richText) {
            // 清除默认的文本内容  
            this.richText.string = '';

            // if (attack > 0) {
            //     // 如果是怪物攻击，在普通攻击前加上标记
            //     // const attackPrefix = isMonsterAttack ? '<color=#ff6b00>[怪] </color>' : '';
            //     const attackPrefix ='';
            //     this.richText.string +=
            //         attackPrefix +
            //         '<color=#00ff00>普通攻击：</color>' + // 设置"普通攻击："为绿色  
            //         '<color=#e81e00>-' + attack + '</color>\n'; // 设置伤害为红色  
            // }



            if (damage > 0) {
                // this.richText.string +=
                //     '<color=#fb3bff>普通攻击：' + damage + '</color>\n'; // 设置"加深伤害"为紫色
           
                this.richText.string +=
                    '<color=#00ff00>普通攻击：</color>' + // 设置"普通攻击："为绿色  
                    '<color=#e81e00>-' + damage + '</color>\n'; // 设置伤害为红色  
            }

            // if (health > 0) {
            //     this.richText.string +=
            //         '<color=#63fc01>吸取生命：' + health + '</color>'; // 设置"吸取生命"为绿色
            // }


        }

    }

    /**
     * 🆕 更新血量和魔法值显示
     */
    public updateHpMp(currentHp: number, maxHp: number, currentMp: number, maxMp: number) {
        if (this.richText) {
            const hpPercent = Math.round((currentHp / maxHp) * 100);
            const mpPercent = Math.round((currentMp / maxMp) * 100);
            
            this.richText.string = 
                `<color=#ff0000>HP: ${currentHp}/${maxHp} (${hpPercent}%)</color>\n` +
                `<color=#0066ff>MP: ${currentMp}/${maxMp} (${mpPercent}%)</color>`;
            
            this.node.active = true;
        }
    }

    /**
     * 🆕 更新血量显示
     */
    public updateHp(currentHp: number, maxHp: number) {
        if (this.richText) {
            const hpPercent = Math.round((currentHp / maxHp) * 100);
            
            this.richText.string = 
                `<color=#ff0000>HP: ${currentHp}/${maxHp} (${hpPercent}%)</color>`;
            
            this.node.active = true;
        }
    }

    /**
     * 🆕 隐藏信息面板
     */
    public hideNotice() {
        this.node.active = false;
    }


}


