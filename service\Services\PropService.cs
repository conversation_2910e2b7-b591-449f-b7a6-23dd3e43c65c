using WebApplication_HM.Interface;
using WebApplication_HM.Models;
using WebApplication_HM.Services.PropScript;
using SqlSugar;
using Newtonsoft.Json;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 道具管理服务（兼容原WindowsFormsApplication7的DataProcess道具相关方法）
    /// </summary>
    public class PropService
    {
        private readonly IPropRepository _propRepository;
        private readonly SimplePropScriptEngine _scriptEngine;
        private readonly MultiScriptService _multiScriptService;
        private readonly ISqlSugarClient _db;
        private readonly ILogger<PropService> _logger;
        
        // 缓存相关
        private List<PropInfo> PP_List = new List<PropInfo>();
        private System.Diagnostics.Stopwatch _propWatch;
        
        public PropService(
            IPropRepository propRepository,
            SimplePropScriptEngine scriptEngine,
            MultiScriptService multiScriptService,
            ISqlSugarClient db,
            ILogger<PropService> logger)
        {
            _propRepository = propRepository;
            _scriptEngine = scriptEngine;
            _multiScriptService = multiScriptService;
            _db = db;
            _logger = logger;
            _propWatch = new System.Diagnostics.Stopwatch();
        }

        #region 道具管理核心方法（兼容原系统）

        /// <summary>
        /// 获取玩家所有道具（对应原GetPAP方法）
        /// </summary>
        public async Task<List<PropInfo>> GetPAPAsync(int userId)
        {
            try
            {
                // 缓存机制：如果350ms内重复调用，返回缓存
                if (_propWatch != null && _propWatch.ElapsedMilliseconds < 350)
                {
                    return PP_List;
                }

                var items = await _propRepository.GetUserItemsAsync(userId);
                PP_List = items;
                
                // 重置计时器
                _propWatch = System.Diagnostics.Stopwatch.StartNew();
                
                return items;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取玩家所有道具失败，用户ID: {UserId}", userId);
                return new List<PropInfo>();
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public List<PropInfo> GetPAP(int userId) => GetPAPAsync(userId).Result;

        /// <summary>
        /// 按道具ID获取用户道具（对应原GetAP_ID方法）
        /// </summary>
        public async Task<PropInfo> GetAP_IDAsync(string itemId, int userId)
        {
            try
            {
                return await _propRepository.GetUserItemByIdAsync(userId, itemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按ID获取用户道具失败，道具ID: {ItemId}, 用户ID: {UserId}", itemId, userId);
                return null;
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public PropInfo GetAP_ID(string itemId, int userId) => GetAP_IDAsync(itemId, userId).Result;

        /// <summary>
        /// 按序号获取道具（对应原GetAP_XH方法）
        /// </summary>
        public async Task<PropInfo> GetAP_XHAsync(string itemSeq, bool useCache = false)
        {
            try
            {
                if (!int.TryParse(itemSeq, out int seq))
                {
                    _logger.LogWarning("无效的道具序号: {ItemSeq}", itemSeq);
                    return null;
                }

                return await _propRepository.GetItemBySeqAsync(seq);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按序号获取道具失败，序号: {ItemSeq}", itemSeq);
                return null;
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public PropInfo GetAP_XH(string itemSeq, bool useCache = false) => GetAP_XHAsync(itemSeq, useCache).Result;

        /// <summary>
        /// 获取用户指定位置的道具（对应原GetALPP方法）
        /// </summary>
        public async Task<List<PropInfo>> GetALPPAsync(int userId, PropLoaction location)
        {
            try
            {
                return await _propRepository.GetUserItemsByPositionAsync(userId, (int)location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户指定位置道具失败，用户ID: {UserId}, 位置: {Location}", userId, location);
                return new List<PropInfo>();
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public List<PropInfo> GetALPP(PropLoaction location, int userId) => GetALPPAsync(userId, location).Result;

        /// <summary>
        /// 添加道具到用户背包（对应原AddPlayerProp方法）
        /// </summary>
        public async Task<bool> AddPlayerPropAsync(PropInfo prop, int userId)
        {
            try
            {
                if (string.IsNullOrEmpty(prop.ItemId))
                {
                    _logger.LogWarning("尝试添加空的道具ID");
                    return false;
                }

                // 清理道具ID中的空格
                prop.ItemId = prop.ItemId.Replace(" ", "");

                // 确保道具位置不为空，默认为1（背包）
                int position = prop.ItemPos.HasValue && prop.ItemPos.Value > 0 ? prop.ItemPos.Value : 1;

                var success = await _propRepository.AddUserItemAsync(
                    userId,
                    prop.ItemId,
                    prop.ItemCount,
                    position);

                if (success)
                {
                    // 清除缓存，强制下次重新加载
                    _propWatch = null;
                    PP_List.Clear();
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加道具失败，用户ID: {UserId}, 道具: {@Prop}", userId, prop);
                return false;
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public bool AddPlayerProp(PropInfo prop, int userId) => AddPlayerPropAsync(prop, userId).Result;

        /// <summary>
        /// 修改玩家道具数量（对应原RevisePP方法）
        /// </summary>
        public async Task<bool> RevisePPAsync(PropInfo prop)
        {
            try
            {
                // 跳过称号类道具
                if (prop.ItemName?.Contains("称号") == true)
                {
                    return true;
                }

                var success = await _propRepository.UpdateItemCountAsync(prop.ItemSeq, prop.ItemCount);
                
                if (success)
                {
                    // 更新缓存
                    var existingItem = PP_List.FirstOrDefault(x => x.ItemSeq == prop.ItemSeq);
                    if (existingItem != null)
                    {
                        var index = PP_List.IndexOf(existingItem);
                        PP_List[index] = prop;
                    }
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改道具数量失败，道具: {@Prop}", prop);
                return false;
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public void RevisePP(PropInfo prop) => RevisePPAsync(prop).Wait();

        /// <summary>
        /// 删除玩家道具（对应原DeletePP方法）
        /// </summary>
        public async Task<bool> DeletePPAsync(PropInfo prop)
        {
            try
            {
                var success = await _propRepository.DeleteItemAsync(prop.ItemSeq);
                
                if (success)
                {
                    // 更新缓存
                    PP_List.RemoveAll(x => x.ItemSeq == prop.ItemSeq);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除道具失败，道具: {@Prop}", prop);
                return false;
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public void DeletePP(PropInfo prop) => DeletePPAsync(prop).Wait();

        /// <summary>
        /// 修改或删除道具（对应原ReviseOrDeletePP方法）
        /// </summary>
        public async Task<bool> ReviseOrDeletePPAsync(int itemSeq, long newCount)
        {
            try
            {
                var success = await _propRepository.UpdateOrDeleteItemAsync(itemSeq, newCount);
                
                if (success)
                {
                    // 清除缓存，强制下次重新加载
                    _propWatch = null;
                    PP_List.Clear();
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改或删除道具失败，序号: {ItemSeq}, 新数量: {NewCount}", itemSeq, newCount);
                return false;
            }
        }

        /// <summary>
        /// 指定道具放入背包（对应原PAPTP方法）
        /// </summary>
        public async Task<bool> PAPTPAsync(string itemSeq, int userId)
        {
            try
            {
                if (!int.TryParse(itemSeq, out int seq))
                {
                    return false;
                }

                // 检查背包容量（这里需要根据实际需求实现容量检查）
                var backpackItems = await GetALPPAsync(userId, PropLoaction.背包);
                var warehouseItems = await GetALPPAsync(userId, PropLoaction.仓库);
                
                // 假设道具容量为100（需要从用户配置中读取）
                if (backpackItems.Count + warehouseItems.Count >= 100)
                {
                    return false;
                }

                return await _propRepository.UpdateItemPositionAsync(seq, (int)PropLoaction.背包);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "道具放入背包失败，序号: {ItemSeq}, 用户ID: {UserId}", itemSeq, userId);
                return false;
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public bool PAPTP(string itemSeq, int userId) => PAPTPAsync(itemSeq, userId).Result;

        /// <summary>
        /// 检查用户是否拥有指定道具（对应原OwnOrNot_PropID方法）
        /// </summary>
        public async Task<bool> OwnOrNot_PropIDAsync(string propId, int userId)
        {
            try
            {
                return await _propRepository.HasItemAsync(userId, propId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查道具拥有状态失败，道具ID: {PropId}, 用户ID: {UserId}", propId, userId);
                return false;
            }
        }

        /// <summary>
        /// 兼容原系统的同步方法
        /// </summary>
        public bool OwnOrNot_PropID(string propId, int userId) => OwnOrNot_PropIDAsync(propId, userId).Result;

        #endregion

        #region 道具配置相关方法

        /// <summary>
        /// 获取道具配置信息
        /// </summary>
        public async Task<PropConfig> GetPropConfigAsync(string itemId)
        {
            try
            {
                return await _propRepository.GetItemConfigAsync(itemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具配置失败，道具ID: {ItemId}", itemId);
                return null;
            }
        }

        /// <summary>
        /// 获取道具脚本信息
        /// </summary>
        public async Task<PropScriptInfo> GetPropScriptAsync(string itemId)
        {
            try
            {
                return await _propRepository.GetItemScriptAsync(itemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具脚本失败，道具ID: {ItemId}", itemId);
                return null;
            }
        }

        /// <summary>
        /// 获取道具名称（兼容原系统）
        /// </summary>
        public async Task<string> GetPropNameAsync(string itemId)
        {
            try
            {
                var config = await GetPropConfigAsync(itemId);
                return config?.Name ?? "未知道具";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具名称失败，道具ID: {ItemId}", itemId);
                return "未知道具";
            }
        }

        /// <summary>
        /// 获取道具图标（兼容原系统）
        /// </summary>
        public async Task<string> GetPropICOAsync(string itemId)
        {
            try
            {
                var config = await GetPropConfigAsync(itemId);
                return config?.Icon ?? "";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具图标失败，道具ID: {ItemId}", itemId);
                return "";
            }
        }

        /// <summary>
        /// 获取道具价格（兼容原系统）
        /// </summary>
        public async Task<string> GetPropPriceAsync(string itemId)
        {
            try
            {
                var config = await GetPropConfigAsync(itemId);
                return config?.Price.ToString() ?? "0";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具价格失败，道具ID: {ItemId}", itemId);
                return "0";
            }
        }

        #endregion

        #region 高级道具脚本系统

        /// <summary>
        /// 使用道具（支持脚本执行）
        /// </summary>
        /// <param name="request">脚本执行请求</param>
        /// <returns>执行结果</returns>
        public PropScriptResult UseItemWithScript(PropScriptExecuteRequest request)
        {
            try
            {
                _logger.LogInformation($"执行道具脚本: UserId={request.UserId}, ItemId={request.ItemId}");

                // 获取道具信息
                var prop = GetPropInfo(request.ItemId);
                if (prop == null)
                {
                    return PropScriptResult.CreateFailure("道具不存在");
                }

                // 检查用户是否拥有该道具
                var userItem = GetUserItem(request.UserId, request.ItemId);
                if (userItem == null || userItem.item_count < request.UseCount)
                {
                    return PropScriptResult.CreateFailure("道具数量不足");
                }

                // 获取脚本内容
                string script = string.IsNullOrEmpty(request.SpecificScript) ? prop.Script : request.SpecificScript;

                if (string.IsNullOrEmpty(script))
                {
                    return PropScriptResult.CreateFailure("该道具无法直接使用!");
                }

                // 检查是否为多脚本选择
                if (script.Contains("多脚本选择") && string.IsNullOrEmpty(request.SpecificScript))
                {
                    var multiOptions = _multiScriptService.ParseMultiScript(request.UserId, request.ItemId, script);
                    if (multiOptions != null)
                    {
                        return PropScriptResult.MultiSelect(multiOptions.Options, multiOptions.ScriptMapping);
                    }
                }

                // 执行脚本
                var result = _scriptEngine.ExecuteScript(script, prop, request.UserId);

                // 如果执行成功且不是特殊道具，扣除道具数量
                if (result.Success && !IsSpecialProp(prop))
                {
                    DeductUserItem(request.UserId, request.ItemId, request.UseCount);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"使用道具脚本失败: {JsonConvert.SerializeObject(request)}");
                return PropScriptResult.CreateFailure("使用道具失败");
            }
        }

        /// <summary>
        /// 执行多脚本选择
        /// </summary>
        /// <param name="request">多脚本选择请求</param>
        /// <returns>执行结果</returns>
        public PropScriptResult ExecuteMultiScriptSelection(MultiScriptSelectRequest request)
        {
            try
            {
                _logger.LogInformation($"执行多脚本选择: UserId={request.UserId}, ItemId={request.ItemId}, Option={request.SelectedOption}");

                var result = _multiScriptService.ExecuteSelectedScript(request);

                // 如果执行成功，扣除道具数量
                if (result.Success)
                {
                    var prop = GetPropInfo(request.ItemId);
                    if (prop != null && !IsSpecialProp(prop))
                    {
                        DeductUserItem(request.UserId, request.ItemId, 1);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"执行多脚本选择失败: {JsonConvert.SerializeObject(request)}");
                return PropScriptResult.CreateFailure("执行多脚本选择失败");
            }
        }

        /// <summary>
        /// 获取用户的多脚本选择状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>多脚本状态</returns>
        public MultiScriptState GetUserMultiScriptState(int userId)
        {
            return _multiScriptService.GetUserScriptState(userId);
        }

        /// <summary>
        /// 清除用户的多脚本选择状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        public void ClearUserMultiScriptState(int userId)
        {
            _multiScriptService.ClearUserScriptState(userId);
        }

        /// <summary>
        /// 验证道具脚本格式
        /// </summary>
        /// <param name="script">脚本内容</param>
        /// <returns>验证结果</returns>
        public ScriptValidationResult ValidateScript(string script)
        {
            try
            {
                if (string.IsNullOrEmpty(script))
                {
                    return ScriptValidationResult.Invalid("脚本内容为空");
                }

                // 检查多脚本选择格式
                if (script.Contains("多脚本选择"))
                {
                    if (_multiScriptService.ValidateMultiScriptFormat(script))
                    {
                        return ScriptValidationResult.Valid(ScriptType.MultiScriptSelect, script.Split('|'));
                    }
                    else
                    {
                        return ScriptValidationResult.Invalid("多脚本选择格式错误");
                    }
                }

                // 检查指令式脚本
                if (script.Contains('|'))
                {
                    string[] directive = script.Split('|');
                    if (directive.Length <= 1)
                    {
                        return ScriptValidationResult.Invalid("脚本格式错误");
                    }

                    var scriptType = GetScriptType(directive[0]);
                    if (scriptType == ScriptType.Unknown)
                    {
                        return ScriptValidationResult.Invalid($"未知的脚本指令: {directive[0]}");
                    }

                    return ScriptValidationResult.Valid(scriptType, directive);
                }

                // 简单脚本
                return ScriptValidationResult.Valid(ScriptType.SimpleExperience, new[] { script });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"验证脚本失败: {script}");
                return ScriptValidationResult.Invalid("脚本验证异常");
            }
        }

        /// <summary>
        /// 获取脚本类型
        /// </summary>
        private ScriptType GetScriptType(string command)
        {
            return command switch
            {
                "扩展道具格子" => ScriptType.ExpandPropCapacity,
                "扩展牧场格子" => ScriptType.ExpandPastureCapacity,
                "学习技能" => ScriptType.LearnSkill,
                "宠物当前经验" => ScriptType.PetExperience,
                "巫族宠物经验" => ScriptType.WuClanPetExperience,
                "龙珠经验值" => ScriptType.DragonBallExp,
                "龙珠突破" => ScriptType.DragonBallBreakthrough,
                "合成道具" => ScriptType.ComposeItem,
                "扣除并获得道具" => ScriptType.DeductAndGainItem,
                "召唤宠物" => ScriptType.SummonPet,
                "镶嵌宝石" => ScriptType.GemstoneInlay,
                "获得称号" => ScriptType.GainTitle,
                "魂宠召唤" => ScriptType.SoulPetSummon,
                _ => ScriptType.Unknown
            };
        }

        /// <summary>
        /// 判断是否为特殊道具（不扣除数量）
        /// </summary>
        private bool IsSpecialProp(PropInfo prop)
        {
            return prop.ItemName.Contains("称号") || prop.ItemName.Contains("魂宠");
        }

        /// <summary>
        /// 获取道具信息
        /// </summary>
        private PropInfo GetPropInfo(string itemId)
        {
            try
            {
                // 尝试将itemId转换为int
                if (int.TryParse(itemId, out int itemNo))
                {
                    var itemConfig = _propRepository.GetItemConfigByNoAsync(itemNo).Result;
                    if (itemConfig == null) return null;

                    return new PropInfo
                    {
                        ItemId = itemId,
                        ItemName = itemConfig.Name ?? "",
                        ItemType = itemConfig.Type ?? "",
                        Script = itemConfig.Script ?? ""
                    };
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取用户道具
        /// </summary>
        private user_item GetUserItem(int userId, string itemId)
        {
            try
            {
                // 直接查询数据库获取用户道具
                return _db.Queryable<user_item>()
                    .Where(i => i.user_id == userId && i.item_id == itemId)
                    .First();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 丢弃道具（删除道具并记录到丢弃表）
        /// </summary>
        public async Task<(bool Success, string Message, object Data)> DiscardItemAsync(int userId, int itemSeq, string reason)
        {
            try
            {
                // 获取道具信息（直接从数据库获取以验证所有者）
                var userItem = await _db.Queryable<user_item>()
                    .Where(x => x.item_seq == itemSeq)
                    .FirstAsync();

                if (userItem == null)
                {
                    return (false, "道具不存在", null);
                }

                // 验证道具所有者
                if (userItem.user_id != userId)
                {
                    return (false, "无权限操作此道具", null);
                }

                // 获取道具配置信息（用于记录道具名称）
                string itemName = "未知道具";
                try
                {
                    if (int.TryParse(userItem.item_id, out int itemNo))
                    {
                        var config = await _db.Queryable<item_config>()
                            .Where(x => x.item_no == itemNo)
                            .FirstAsync();
                        if (config != null)
                        {
                            itemName = config.name ?? "未知道具";
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取道具配置失败，道具ID: {ItemId}", userItem.item_id);
                }

                // 开始事务
                _db.Ado.BeginTran();
                try
                {
                    // 1. 记录到丢弃表
                    var discardLog = new item_discard_log
                    {
                        user_id = userId,
                        original_item_seq = itemSeq,
                        item_id = userItem.item_id,
                        item_name = itemName,
                        discard_count = userItem.item_count,
                        discard_reason = reason ?? "用户主动丢弃",
                        discard_time = DateTime.Now,
                        is_recovered = 0,
                        remark = $"从位置{userItem.item_pos}丢弃"
                    };

                    await _db.Insertable(discardLog).ExecuteCommandAsync();

                    // 2. 删除用户道具
                    var deleteResult = await _db.Deleteable<user_item>()
                        .Where(x => x.item_seq == itemSeq)
                        .ExecuteCommandAsync();

                    if (deleteResult <= 0)
                    {
                        throw new Exception("删除道具失败");
                    }

                    _db.Ado.CommitTran();

                    _logger.LogInformation("道具丢弃成功，用户ID: {UserId}, 道具序号: {ItemSeq}, 道具名称: {ItemName}",
                        userId, itemSeq, itemName);

                    return (true, $"成功丢弃道具：{itemName}", new { itemName, discardCount = userItem.item_count });
                }
                catch (Exception transactionEx)
                {
                    _db.Ado.RollbackTran();
                    _logger.LogError(transactionEx, "道具丢弃事务失败");
                    return (false, "丢弃道具失败", null);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "丢弃道具失败，用户ID: {UserId}, 道具序号: {ItemSeq}", userId, itemSeq);
                return (false, "丢弃道具失败", null);
            }
        }

        /// <summary>
        /// 获取用户丢弃记录
        /// </summary>
        public async Task<object> GetDiscardLogsAsync(int userId, int page = 1, int pageSize = 20)
        {
            try
            {
                var totalCount = await _db.Queryable<item_discard_log>()
                    .Where(x => x.user_id == userId)
                    .CountAsync();

                var logs = await _db.Queryable<item_discard_log>()
                    .Where(x => x.user_id == userId)
                    .OrderBy(x => x.discard_time, OrderByType.Desc)
                    .ToPageListAsync(page, pageSize);

                return new
                {
                    success = true,
                    data = logs,
                    pagination = new
                    {
                        page,
                        pageSize,
                        totalCount,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取丢弃记录失败，用户ID: {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// 找回丢弃的道具
        /// </summary>
        public async Task<(bool Success, string Message, object Data)> RecoverDiscardedItemAsync(int logId, int userId)
        {
            try
            {
                // 获取丢弃记录
                var discardLog = await _db.Queryable<item_discard_log>()
                    .Where(x => x.log_id == logId && x.user_id == userId)
                    .FirstAsync();

                if (discardLog == null)
                {
                    return (false, "丢弃记录不存在", null);
                }

                if (discardLog.is_recovered == 1)
                {
                    return (false, "该道具已经找回过了", null);
                }

                // 开始事务
                _db.Ado.BeginTran();
                try
                {
                    // 1. 重新创建道具到背包
                    var newUserItem = new user_item
                    {
                        user_id = userId,
                        item_id = discardLog.item_id,
                        item_count = discardLog.discard_count,
                        item_pos = 1, // 找回到背包
                        create_time = DateTime.Now
                    };

                    await _db.Insertable(newUserItem).ExecuteCommandAsync();

                    // 2. 更新丢弃记录状态
                    await _db.Updateable<item_discard_log>()
                        .SetColumns(x => new item_discard_log
                        {
                            is_recovered = 1,
                            recover_time = DateTime.Now,
                            remark = discardLog.remark + " [已找回]"
                        })
                        .Where(x => x.log_id == logId)
                        .ExecuteCommandAsync();

                    _db.Ado.CommitTran();

                    _logger.LogInformation("道具找回成功，用户ID: {UserId}, 记录ID: {LogId}, 道具名称: {ItemName}",
                        userId, logId, discardLog.item_name);

                    return (true, $"成功找回道具：{discardLog.item_name}", new {
                        itemName = discardLog.item_name,
                        recoverCount = discardLog.discard_count
                    });
                }
                catch (Exception transactionEx)
                {
                    _db.Ado.RollbackTran();
                    _logger.LogError(transactionEx, "道具找回事务失败");
                    return (false, "找回道具失败", null);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "找回道具失败，记录ID: {LogId}, 用户ID: {UserId}", logId, userId);
                return (false, "找回道具失败", null);
            }
        }

        /// <summary>
        /// 移动道具位置
        /// </summary>
        public async Task<bool> MoveItemPositionAsync(int userId, int itemSeq, int newPosition)
        {
            try
            {
                // 验证新位置是否有效
                if (newPosition < 1 || newPosition > 3)
                {
                    _logger.LogWarning("无效的道具位置: {NewPosition}", newPosition);
                    return false;
                }

                // 获取道具信息（直接从数据库获取以验证所有者）
                var userItem = await _db.Queryable<user_item>()
                    .Where(x => x.item_seq == itemSeq)
                    .FirstAsync();

                if (userItem == null)
                {
                    _logger.LogWarning("道具不存在，序号: {ItemSeq}", itemSeq);
                    return false;
                }

                // 验证道具所有者
                if (userItem.user_id != userId)
                {
                    _logger.LogWarning("道具所有者不匹配，用户ID: {UserId}, 道具序号: {ItemSeq}", userId, itemSeq);
                    return false;
                }

                // 更新道具位置
                var success = await _propRepository.UpdateItemPositionAsync(itemSeq, newPosition);

                if (success)
                {
                    string positionName = newPosition switch
                    {
                        1 => "背包",
                        2 => "仓库",
                        3 => "丢弃",
                        _ => "未知位置"
                    };

                    _logger.LogInformation("道具位置更新成功，序号: {ItemSeq}, 新位置: {NewPosition}({PositionName})",
                        itemSeq, newPosition, positionName);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动道具位置失败，用户ID: {UserId}, 道具序号: {ItemSeq}, 新位置: {NewPosition}",
                    userId, itemSeq, newPosition);
                return false;
            }
        }

        /// <summary>
        /// 扣除用户道具
        /// </summary>
        public async Task<bool> DeductUserItemAsync(int userId, string itemId, int count)
        {
            try
            {
                var userItem = GetUserItem(userId, itemId);
                if (userItem == null || userItem.item_count < count)
                {
                    return false;
                }

                long newCount = userItem.item_count - count;
                if (newCount <= 0)
                {
                    return await _propRepository.DeleteItemAsync(userItem.item_seq);
                }
                else
                {
                    return await _propRepository.UpdateItemCountAsync(userItem.item_seq, newCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扣除用户道具失败");
                return false;
            }
        }

        /// <summary>
        /// 扣除用户道具（同步版本）
        /// </summary>
        private bool DeductUserItem(int userId, string itemId, int count)
        {
            return DeductUserItemAsync(userId, itemId, count).Result;
        }

        #endregion
    }
}
