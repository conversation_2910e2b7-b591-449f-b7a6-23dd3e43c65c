# Cocos Creator 3.8 性能监控模块相关接口规则文档

## 一、Profiler 模块概述
Profiler 是 Cocos Creator 3D 引擎内置的性能分析工具，主要用于实时监控游戏运行时的核心指标。其源码在不同平台有不同路径，Web 路径为 `engine/cocos/profiler/profiler.ts`，原生路径为 `engine/native/cocos/profiler/Profiler.cpp`。该模块可监控的指标包括：
- **核心状态**：如 FPS、帧耗时、GPU 类型、多线程状态等。
- **对象统计**：渲染节点数量、对象实例数量及其峰值。
- **内存统计**：内存分配总量、对象计数及峰值。
- **性能统计**：各代码块（如渲染、逻辑）的耗时分布及调用频率。

## 二、核心接口及使用方式
### 1. profiler.stats
#### 说明
`profiler.stats` 是存储引擎运行时核心性能指标的对象，类型为 `IProfilerState`。通过该对象可以获取各种性能指标信息，方便在项目界面上显示如 Draw Call、FPS 等信息。
#### 使用方式
以下是 `stats` 对象中部分核心字段的详细说明及使用示例：
```typescript
import { profiler } from 'cc';

// 获取 FPS 信息
const fps = profiler.stats.fps.value;
console.log(`当前帧率: ${fps}`);

// 获取 Draw Call 信息
const draws = profiler.stats.draws.value;
console.log(`当前 Draw Call 数量: ${draws}`);
```
#### `stats` 字段全解析
- **fps（帧率）**
    - **作用**：实时显示每秒渲染的帧数（Frames Per Second）。
    - **理想值**：≥ 60（流畅），30 ≤ FPS < 60（可接受），< 30（卡顿）。
    - **监控点**：游戏整体性能的核心指标，反映设备负载和代码效率。
    - **数据来源**：通过 `PerfCounter` 每帧计算时间差统计。
- **draws（绘制调用次数）**
    - **作用**：统计每帧的 Draw Call 数量，Draw Call 是指 CPU 向 GPU 发送的一次绘制命令，过多的 Draw Call 会影响性能。
    - **监控点**：渲染性能的重要指标，优化 Draw Call 可以提高游戏的渲染效率。
    - **数据来源**：引擎在渲染过程中统计。
- **frameTime（帧耗时）**
    - **作用**：记录每帧的渲染耗时，单位为毫秒。
    - **监控点**：反映游戏的实时性能，帧耗时过长会导致游戏卡顿。
    - **数据来源**：通过记录每帧开始和结束的时间计算得出。
- **gpuType（GPU 类型）**
    - **作用**：显示当前设备使用的 GPU 类型。
    - **监控点**：不同的 GPU 对游戏的支持和性能表现可能不同，可用于针对性优化。
    - **数据来源**：通过设备信息获取。
- **multiThreadState（多线程状态）**
    - **作用**：显示游戏是否启用了多线程，以及多线程的运行状态。
    - **监控点**：多线程可以提高游戏的性能，但需要合理使用，避免出现线程安全问题。
    - **数据来源**：引擎内部记录。
- **renderNodeCount（渲染节点数量）**
    - **作用**：统计当前场景中参与渲染的节点数量。
    - **监控点**：过多的渲染节点会增加渲染压力，影响性能。
    - **数据来源**：引擎在渲染过程中统计。
- **objectInstanceCount（对象实例数量）**
    - **作用**：统计游戏中创建的对象实例数量。
    - **监控点**：对象实例数量过多会占用大量内存，影响游戏性能。
    - **数据来源**：引擎在对象创建和销毁时记录。
- **memoryAllocation（内存分配总量）**
    - **作用**：显示游戏运行时的内存分配总量，单位为字节。
    - **监控点**：内存使用过高可能导致游戏崩溃或性能下降。
    - **数据来源**：引擎内部记录内存分配情况。
- **objectCount（对象计数）**
    - **作用**：统计游戏中各种类型对象的数量。
    - **监控点**：可以帮助分析游戏中对象的使用情况，优化对象的创建和销毁。
    - **数据来源**：引擎在对象创建和销毁时记录。

## 三、使用建议
- **实时监控**：在游戏开发过程中，可以在界面上实时显示 `profiler.stats` 中的关键指标，如 FPS 和 Draw Call，以便及时发现性能问题。
- **性能优化**：根据监控到的指标，针对性地进行性能优化。例如，如果 Draw Call 过高，可以考虑进行合批优化；如果内存使用过高，可以优化对象的创建和销毁逻辑。
- **版本对比**：在不同版本的游戏中进行性能监控对比，评估优化效果，确保游戏性能不断提升。