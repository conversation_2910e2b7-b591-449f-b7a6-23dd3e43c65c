import { _decorator, Component, Node,find,Label } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('notice_test')
export class notice_test extends Component {

    label1: Label = null;

    start() {
      const LabelNode:Node=find("/Label",this.node);

      this.label1=LabelNode.getComponent(Label);
     
    }

    update(deltaTime: number) {
        
    }

    
    public onClick(){
        this.label1.string="Hello 111112222";
    }
}


