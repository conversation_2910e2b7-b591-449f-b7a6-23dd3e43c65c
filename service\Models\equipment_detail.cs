﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///装备明细表
    ///</summary>
    [SugarTable("equipment_detail")]
    public partial class equipment_detail
    {
           public equipment_detail(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:装备ID（关联equipment.equip_id）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string equip_id {get;set;}

           /// <summary>
           /// Desc:攻击加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? atk {get;set;}

           /// <summary>
           /// Desc:命中加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? hit {get;set;}

           /// <summary>
           /// Desc:防御加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? def {get;set;}

           /// <summary>
           /// Desc:速度加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? spd {get;set;}

           /// <summary>
           /// Desc:闪避加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? dodge {get;set;}

           /// <summary>
           /// Desc:生命加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? hp {get;set;}

           /// <summary>
           /// Desc:魔法加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? mp {get;set;}

           /// <summary>
           /// Desc:加深加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? deepen {get;set;}

           /// <summary>
           /// Desc:抵消加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? offset {get;set;}

           /// <summary>
           /// Desc:吸血加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vamp {get;set;}

           /// <summary>
           /// Desc:吸魔加成
           /// Default:0.00
           /// Nullable:True
           /// </summary>           
           public decimal? vamp_mp {get;set;}

           /// <summary>
           /// Desc:装备类型ID（关联equipment_type）
           /// Default:
           /// Nullable:False
           /// </summary>
           public string equip_type_id {get;set;}

           /// <summary>
           /// Desc:装备说明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? description {get;set;}

           /// <summary>
           /// Desc:主属性
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? main_attr {get;set;}

           /// <summary>
           /// Desc:五行限制
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? element_limit {get;set;}

           /// <summary>
           /// Desc:装备名称
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? equip_name {get;set;}

           /// <summary>
           /// Desc:主属性值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? main_attr_value {get;set;}

           /// <summary>
           /// Desc:副属性
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sub_attr {get;set;}

           /// <summary>
           /// Desc:副属性值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sub_attr_value {get;set;}

    }
}
