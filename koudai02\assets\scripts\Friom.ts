import { _decorator, Component, Node, find } from 'cc';
import { Global } from './Global';
import { HttpRequest } from './tool/HttpRequest';
import { MassiveResourceManager } from './manager/MassiveResourceManager';
import { pack } from './wrap/pack';

const { ccclass, property } = _decorator;

//主脚本
@ccclass('Friom')
export class Friom extends Component {

    beibao: Node = null;//背包按钮节点
    zhuagnbei: Node = null;//装备按钮节点

    pack: Node = null;//背包页面节点
    BackpackClosed: Node = null;//背包关闭按钮
    packState: boolean = false;//背包页面状态
    panel_field: Node = null;//野外探险页面
    panel_town: Node = null;//中心城镇页面
    m_field: Node;//野外探险按钮
    m_town: Node;//中心城镇按钮
    m_Pet: Node;//宠物资料按钮

    async start() {
        console.log("🎮 欢迎进入首页");
        
        // 智能预加载资源
        try {
            const resourceManager = MassiveResourceManager.getInstance();
            // 假设用户等级10，当前在主界面
            const userLevel = Global.currentUser?.level || 10;
            await resourceManager.smartPreload(userLevel, 'Friom');
        } catch (error) {
            console.warn("⚠️ 智能预加载失败，但不影响游戏运行:", error);
        }
        
        // 初始化UI
        this.initializeUI();
        
        // 预加载下一个可能用到的资源
        this.preloadNextSceneAssets();
    }

    /**
     * 初始化UI界面
     */
    private initializeUI(): void {
        //背包
        this.beibao = find("/headMenu/beibao_01", this.node);
        
        //装备
        this.zhuagnbei = find("/headMenu/zhuangbei_02", this.node);

        this.pack = find("/pack", this.node);
        this.BackpackClosed = find("/pack/BackpackClosed", this.node);
        this.m_field = find("/side/menu/m_field", this.node);
        this.m_town = find("/side/menu/m_town", this.node);
        this.m_Pet = find("/side/menu/m_Pet", this.node);
        this.panel_field = find("/panel_field", this.node);
        this.panel_town = find("/panel_town", this.node);
        
        // 验证节点是否存在
        if (!this.panel_field) {
            console.error("❌ 野外探险页面未找到");
            return;
        }

        if (!this.panel_town) {
            console.error("❌ 中心城镇页面未找到");
            return;
        }

        if (!this.m_field) {
            console.error("❌ 野外探险按钮未找到");
            return;
        }
        if (!this.m_town) {
            console.error("❌ 中心城镇按钮未找到");
            return;
        }
        if (!this.m_Pet) {
            console.error("❌ 宠物资料按钮未找到");
            return;
        }

        // 绑定事件
        this.BackpackClosed.on(Node.EventType.MOUSE_DOWN, this.packShow, this);
        this.beibao.on(Node.EventType.MOUSE_DOWN, () => this.showPackInterface('backpack'), this);
        this.zhuagnbei.on(Node.EventType.MOUSE_DOWN, () => this.showPackInterface('equipment'), this);
        this.m_field.on(Node.EventType.MOUSE_DOWN, this.fieldShow, this);
        this.m_town.on(Node.EventType.MOUSE_DOWN, this.townShow, this);
    }

    /**
     * 预加载下一个场景可能用到的资源
     */
    private async preloadNextSceneAssets(): void {
        try {
            console.log("🔄 开始预加载战斗场景资源...");
            // 这里可以在后台静默预加载，不阻塞UI
            // 预加载会在用户进入战斗前提前准备好资源
        } catch (error) {
            console.warn("⚠️ 预加载战斗资源失败:", error);
        }
    }

    update(deltaTime: number) {

    }

    //隐藏显示野外探险
    public fieldShow() {
        this.panel_field.active = true;
        this.panel_town.active = false;
    }

    //隐藏显示中心城镇
    public townShow() {
        this.panel_field.active = false;
        this.panel_town.active = true;
    }

    //背包和装备界面隐藏或显示
    private packShow() {
        if (this.packState) {
            this.pack.active = false;
            this.packState = false;
        } else {
            this.pack.active = true;
            this.packState = true;
        }
    }

    //显示指定类型的背包界面
    private showPackInterface(interfaceType: 'backpack' | 'equipment') {
        if (this.packState) {
            this.pack.active = false;
            this.packState = false;
        } else {
            this.pack.active = true;
            this.packState = true;

            // 获取pack组件并设置界面类型
            const packComponent = this.pack.getComponent(pack);
            if (packComponent) {
                packComponent.setInterfaceType(interfaceType);
            } else {
                console.warn("pack组件未找到");
            }
        }
    }


}


