using Microsoft.Extensions.Logging;
using SqlSugar;
using WebApplication_HM.Sugar;
using WebApplication_HM.Models;
using WebApplication_HM.DTOs.Common;
using WebApplication_HM.Interface;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 五行点化服务
    /// 基于老项目EquipmentProcess.cs中的TransformWX方法完整迁移
    /// </summary>
    public class ElementTransformService
    {
        private readonly ILogger<ElementTransformService> _logger;
        private readonly DbContext _dbContext;
        private readonly IEquipmentRepository _equipmentRepository;

        // 五行相生关系 (基于老项目EquimentAttribute字典)
        private static readonly Dictionary<string, string> ELEMENT_GENERATION = new()
        {
            {"生命", "金"}, {"魔法", "木"}, {"攻击", "火"}, {"防御", "土"}, 
            {"命中", "雷"}, {"闪避", "水"}, {"速度", "风"}
        };

        // 五行相克关系 (基于老项目Restraint字典)
        private static readonly Dictionary<string, string> ELEMENT_RESTRAINT = new()
        {
            {"生命", "火"}, {"魔法", "金"}, {"攻击", "水"}, {"防御", "木"}, 
            {"命中", "风"}, {"闪避", "土"}, {"速度", "雷"}
        };

        // 五行列表 (基于老项目WXList)
        private static readonly List<string> ELEMENT_LIST = new() 
        { 
            "金", "木", "火", "土", "雷", "水", "风" 
        };

        // 五行点化石道具ID (基于老项目)
        private const string ELEMENT_TRANSFORM_STONE_ID = "2017092001";

        public ElementTransformService(
            ILogger<ElementTransformService> logger,
            DbContext dbContext,
            IEquipmentRepository equipmentRepository)
        {
            _logger = logger;
            _dbContext = dbContext;
            _equipmentRepository = equipmentRepository;
        }

        /// <summary>
        /// 五行点化主方法
        /// 基于老项目EquipmentProcess.cs中的TransformWX方法完整迁移
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        public async Task<ApiResult<ElementTransformResult>> TransformElementAsync(int userEquipmentId)
        {
            try
            {
                var result = await _dbContext.Db.Ado.UseTranAsync(async () =>
                {
                    // 1. 获取装备信息
                    var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                    if (equipment == null)
                        return ApiResult<ElementTransformResult>.CreateError("装备不存在");

                    var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                    if (equipDetail == null)
                        return ApiResult<ElementTransformResult>.CreateError("装备配置不存在");

                    var equipType = await _equipmentRepository.GetEquipmentTypeAsync(equipDetail.equip_type_id);
                    if (equipType == null)
                        return ApiResult<ElementTransformResult>.CreateError("装备类型不存在");

                    // 2. 检查装备类型限制 (基于老项目逻辑)
                    var restrictedTypes = new[] { "灵饰", "卡牌", "法宝", "背饰" };
                    if (restrictedTypes.Contains(equipType.type_name))
                    {
                        return ApiResult<ElementTransformResult>.CreateError("该装备不可点化五行!");
                    }

                    // 3. 检查五行点化石数量
                    var transformStone = await GetUserItemAsync(equipment.user_id, ELEMENT_TRANSFORM_STONE_ID);
                    if (transformStone == null || transformStone.item_count < 1)
                    {
                        return ApiResult<ElementTransformResult>.CreateError("必须拥有足够的五行点化石才能点化哦!");
                    }

                    // 4. 获取装备主属性
                    string mainAttr = equipDetail.main_attr ?? "攻击";
                    string bestElement = ELEMENT_GENERATION.GetValueOrDefault(mainAttr, "金");
                    string worstElement = ELEMENT_RESTRAINT.GetValueOrDefault(mainAttr, "火");

                    string newElement;
                    string oldElement = equipment.element ?? "无";

                    // 5. 如果装备没有五行或五行为"无" (基于老项目逻辑)
                    if (string.IsNullOrEmpty(equipment.element) || equipment.element == "无")
                    {
                        // 随机生成五行，但排除最佳五行 (基于老项目逻辑)
                        var availableElements = new List<string>(ELEMENT_LIST);
                        availableElements.Remove(bestElement);
                        newElement = availableElements[new Random().Next(availableElements.Count)];
                    }
                    // 6. 如果已经是最佳五行 (基于老项目逻辑)
                    else if (equipment.element == bestElement)
                    {
                        return ApiResult<ElementTransformResult>.CreateError("您的装备五行已经是最佳的了，无需再点化了！");
                    }
                    // 7. 否则按概率点化 (基于老项目复杂概率逻辑)
                    else
                    {
                        newElement = GenerateRandomElementWithProbability(bestElement, worstElement);
                    }

                    // 8. 消耗五行点化石
                    await ConsumeItemAsync(equipment.user_id, ELEMENT_TRANSFORM_STONE_ID, 1);

                    // 9. 更新装备五行
                    equipment.element = newElement;
                    equipment.update_time = DateTime.Now;
                    await _equipmentRepository.UpdateEquipmentAsync(equipment);

                    // 10. 记录操作日志
                    await LogElementTransformOperationAsync(equipment.user_id, userEquipmentId, 
                        oldElement, newElement, "SUCCESS", "五行点化成功！");

                    var transformResult = new ElementTransformResult
                    {
                        Success = true,
                        OldElement = oldElement,
                        NewElement = newElement,
                        BestElement = bestElement,
                        WorstElement = worstElement,
                        MainAttribute = mainAttr,
                        Message = "五行点化成功！"
                    };

                    return ApiResult<ElementTransformResult>.CreateSuccess(transformResult, "五行点化成功！");
                });

                return result.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "五行点化失败，装备ID: {EquipmentId}", userEquipmentId);
                return ApiResult<ElementTransformResult>.CreateError("五行点化失败");
            }
        }

        /// <summary>
        /// 获取装备五行信息
        /// </summary>
        /// <param name="userEquipmentId">用户装备ID</param>
        /// <returns></returns>
        public async Task<ApiResult<ElementInfoResult>> GetElementInfoAsync(int userEquipmentId)
        {
            try
            {
                var equipment = await _equipmentRepository.GetEquipmentByIdAsync(userEquipmentId);
                if (equipment == null)
                    return ApiResult<ElementInfoResult>.CreateError("装备不存在");

                var equipDetail = await _equipmentRepository.GetEquipmentDetailAsync(equipment.equip_id);
                if (equipDetail == null)
                    return ApiResult<ElementInfoResult>.CreateError("装备配置不存在");

                var equipType = await _equipmentRepository.GetEquipmentTypeAsync(equipDetail.equip_type_id);
                if (equipType == null)
                    return ApiResult<ElementInfoResult>.CreateError("装备类型不存在");

                string mainAttr = equipDetail.main_attr ?? "攻击";
                string bestElement = ELEMENT_GENERATION.GetValueOrDefault(mainAttr, "金");
                string worstElement = ELEMENT_RESTRAINT.GetValueOrDefault(mainAttr, "火");
                string currentElement = equipment.element ?? "无";

                var elementInfo = new ElementInfoResult
                {
                    CurrentElement = currentElement,
                    BestElement = bestElement,
                    WorstElement = worstElement,
                    MainAttribute = mainAttr,
                    CanTransform = !new[] { "灵饰", "卡牌", "法宝", "背饰" }.Contains(equipType.type_name),
                    IsOptimal = currentElement == bestElement,
                    ElementRelationship = GetElementRelationship(currentElement, bestElement, worstElement)
                };

                return ApiResult<ElementInfoResult>.CreateSuccess(elementInfo, "获取五行信息成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取五行信息失败，装备ID: {EquipmentId}", userEquipmentId);
                return ApiResult<ElementInfoResult>.CreateError("获取五行信息失败");
            }
        }

        /// <summary>
        /// 根据概率生成随机五行
        /// 基于老项目的复杂概率逻辑完整迁移
        /// </summary>
        private string GenerateRandomElementWithProbability(string bestElement, string worstElement)
        {
            // 基于老项目的概率逻辑：
            // 1. 创建候选列表，排除相克和最佳五行
            var candidates = new List<string>(ELEMENT_LIST);
            candidates.Remove(worstElement);  // 移除相克五行
            candidates.Remove(bestElement);   // 移除最佳五行

            // 2. 构建概率池 (基于老项目逻辑)
            var probabilityPool = new List<string>();
            
            // 添加普通五行3次 (基础概率)
            for (int i = 0; i < 3; i++)
            {
                probabilityPool.AddRange(candidates);
            }

            // 添加相克五行4次 (较低概率)
            for (int i = 0; i < 4; i++)
            {
                probabilityPool.Add(worstElement);
            }

            // 添加最佳五行1次 (最低概率)
            probabilityPool.Add(bestElement);

            // 3. 随机选择
            var random = new Random();
            return probabilityPool[random.Next(probabilityPool.Count)];
        }

        /// <summary>
        /// 获取五行关系描述
        /// </summary>
        private string GetElementRelationship(string currentElement, string bestElement, string worstElement)
        {
            if (currentElement == "无" || string.IsNullOrEmpty(currentElement))
                return "无五行";
            
            if (currentElement == bestElement)
                return "最佳五行";
            
            if (currentElement == worstElement)
                return "相克五行";
            
            return "普通五行";
        }

        #region 辅助方法

        /// <summary>
        /// 获取用户道具
        /// </summary>
        private async Task<user_item?> GetUserItemAsync(int userId, string itemId)
        {
            return await _dbContext.Db.Queryable<user_item>()
                .Where(x => x.user_id == userId && x.item_id == itemId && x.item_count > 0)
                .FirstAsync();
        }

        /// <summary>
        /// 消耗道具
        /// </summary>
        private async Task ConsumeItemAsync(int userId, string itemId, int quantity)
        {
            var item = await _dbContext.Db.Queryable<user_item>()
                .Where(x => x.user_id == userId && x.item_id == itemId)
                .FirstAsync();

            if (item != null)
            {
                item.item_count -= quantity;
                if (item.item_count <= 0)
                {
                    await _dbContext.Db.Deleteable<user_item>()
                        .Where(x => x.id == item.id)
                        .ExecuteCommandAsync();
                }
                else
                {
                    await _dbContext.Db.Updateable(item).ExecuteCommandAsync();
                }
            }
        }

        /// <summary>
        /// 记录五行点化操作日志
        /// </summary>
        private async Task LogElementTransformOperationAsync(int userId, int userEquipmentId, 
            string oldElement, string newElement, string result, string message)
        {
            try
            {
                var log = new equipment_operation_log
                {
                    user_id = userId,
                    user_equipment_id = userEquipmentId,
                    operation_type = "TRANSFORM_ELEMENT",
                    operation_data = $"{oldElement} -> {newElement}",
                    result = result,
                    result_message = message,
                    create_time = DateTime.Now
                };

                await _dbContext.Db.Insertable(log).ExecuteCommandAsync();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "记录五行点化操作日志失败");
            }
        }

        #endregion
    }

    /// <summary>
    /// 五行点化结果
    /// </summary>
    public class ElementTransformResult
    {
        public bool Success { get; set; }
        public string OldElement { get; set; } = string.Empty;
        public string NewElement { get; set; } = string.Empty;
        public string BestElement { get; set; } = string.Empty;
        public string WorstElement { get; set; } = string.Empty;
        public string MainAttribute { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    /// <summary>
    /// 五行信息结果
    /// </summary>
    public class ElementInfoResult
    {
        public string CurrentElement { get; set; } = string.Empty;
        public string BestElement { get; set; } = string.Empty;
        public string WorstElement { get; set; } = string.Empty;
        public string MainAttribute { get; set; } = string.Empty;
        public bool CanTransform { get; set; }
        public bool IsOptimal { get; set; }
        public string ElementRelationship { get; set; } = string.Empty;
    }
}
