# Test new discard system
$baseUrl = "http://localhost:5000/api"

Write-Host "=== 测试新的道具丢弃系统 ==="

# 1. 检查当前背包内容
Write-Host "`n1. 当前背包内容:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/user/1/position/1" -Method GET
    $items = $response.Content | ConvertFrom-Json
    Write-Host "背包道具数量: $($items.Count)"
    if ($items.Count -gt 0) {
        $testItem = $items[0]
        Write-Host "测试道具: $($testItem.itemName) (序号: $($testItem.itemSeq), 数量: $($testItem.itemCount))"
        $testItemSeq = $testItem.itemSeq
        $testItemName = $testItem.itemName
    } else {
        Write-Host "背包为空，无法进行测试"
        exit
    }
}
catch {
    Write-Host "获取背包内容失败: $($_.Exception.Message)"
    exit
}

# 2. 测试丢弃道具
Write-Host "`n2. 测试丢弃道具:"
$discardBody = @{
    UserId = 1
    ItemSeq = $testItemSeq
    Reason = "测试丢弃功能"
} | ConvertTo-Json

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/discard" -Method POST -Body $discardBody -ContentType "application/json"
    Write-Host "✅ 丢弃道具成功: $($response.StatusCode)"
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   响应: $($result.message)"
}
catch {
    Write-Host "❌ 丢弃道具失败: $($_.Exception.Message)"
    exit
}

# 3. 检查背包内容（应该减少了）
Write-Host "`n3. 丢弃后背包内容:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/user/1/position/1" -Method GET
    $itemsAfterDiscard = $response.Content | ConvertFrom-Json
    Write-Host "背包道具数量: $($itemsAfterDiscard.Count)"
    Write-Host "道具数量变化: $($items.Count) -> $($itemsAfterDiscard.Count)"
}
catch {
    Write-Host "获取背包内容失败: $($_.Exception.Message)"
}

# 4. 查看丢弃记录
Write-Host "`n4. 查看丢弃记录:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/discard-logs/1" -Method GET
    $discardLogs = $response.Content | ConvertFrom-Json
    Write-Host "丢弃记录数量: $($discardLogs.data.Count)"
    
    if ($discardLogs.data.Count -gt 0) {
        $latestLog = $discardLogs.data[0]
        Write-Host "最新丢弃记录:"
        Write-Host "   - 记录ID: $($latestLog.log_id)"
        Write-Host "   - 道具名称: $($latestLog.item_name)"
        Write-Host "   - 丢弃数量: $($latestLog.discard_count)"
        Write-Host "   - 丢弃原因: $($latestLog.discard_reason)"
        Write-Host "   - 丢弃时间: $($latestLog.discard_time)"
        Write-Host "   - 是否已找回: $($latestLog.is_recovered)"
        
        $logIdToRecover = $latestLog.log_id
    }
}
catch {
    Write-Host "获取丢弃记录失败: $($_.Exception.Message)"
    exit
}

# 5. 测试找回道具
Write-Host "`n5. 测试找回道具:"
$recoverBody = 1 | ConvertTo-Json  # UserId

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/recover/$logIdToRecover" -Method POST -Body $recoverBody -ContentType "application/json"
    Write-Host "✅ 找回道具成功: $($response.StatusCode)"
    $result = $response.Content | ConvertFrom-Json
    Write-Host "   响应: $($result.message)"
}
catch {
    Write-Host "❌ 找回道具失败: $($_.Exception.Message)"
}

# 6. 检查背包内容（应该恢复了）
Write-Host "`n6. 找回后背包内容:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/user/1/position/1" -Method GET
    $itemsAfterRecover = $response.Content | ConvertFrom-Json
    Write-Host "背包道具数量: $($itemsAfterRecover.Count)"
    Write-Host "道具数量变化: $($items.Count) -> $($itemsAfterDiscard.Count) -> $($itemsAfterRecover.Count)"
}
catch {
    Write-Host "获取背包内容失败: $($_.Exception.Message)"
}

# 7. 再次查看丢弃记录（状态应该更新了）
Write-Host "`n7. 找回后的丢弃记录:"
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/prop/discard-logs/1" -Method GET
    $discardLogsAfter = $response.Content | ConvertFrom-Json
    
    if ($discardLogsAfter.data.Count -gt 0) {
        $updatedLog = $discardLogsAfter.data[0]
        Write-Host "更新后的记录:"
        Write-Host "   - 记录ID: $($updatedLog.log_id)"
        Write-Host "   - 道具名称: $($updatedLog.item_name)"
        Write-Host "   - 是否已找回: $($updatedLog.is_recovered)"
        Write-Host "   - 找回时间: $($updatedLog.recover_time)"
        Write-Host "   - 备注: $($updatedLog.remark)"
    }
}
catch {
    Write-Host "获取丢弃记录失败: $($_.Exception.Message)"
}

Write-Host "`n=== 测试完成 ==="
Write-Host "新丢弃系统功能验证:"
Write-Host "1. 道具真正从背包中删除 ✅"
Write-Host "2. 丢弃记录正确保存到数据库 ✅"
Write-Host "3. 可以查询丢弃记录 ✅"
Write-Host "4. 可以找回丢弃的道具 ✅"
Write-Host "5. 找回后记录状态正确更新 ✅"
