using WebApplication_HM.DTOs;
using WebApplication_HM.Models;

namespace WebApplication_HM.IServices
{
    /// <summary>
    /// DbFirst服务
    /// </summary>
    public interface IDbFirstService
    {
        /// <summary>
        /// 生成数据库实体
        /// </summary>
        /// <returns>执行结果和消息</returns>
        Task<(bool success, string message)> GenerateEntity();

        /// <summary>
        /// 根据实体生成数据库表或字段
        /// </summary>
        /// <returns>执行结果和消息</returns>
        Task<(bool success, string message)> GenerateDatabase();

        /// <summary>
        /// 根据指定实体类型生成数据库表或字段
        /// </summary>
        /// <param name="entityType">实体类型</param>
        /// <returns>执行结果和消息</returns>
        Task<(bool success, string message)> GenerateDatabaseByType(Type entityType);

        /// <summary>
        /// 同步指定实体的数据库结构
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <returns>执行结果和消息</returns>
        Task<(bool success, string message)> SyncEntityStructure<T>() where T : class, new();
    }
}