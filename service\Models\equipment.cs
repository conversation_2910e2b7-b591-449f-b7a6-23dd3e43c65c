﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace WebApplication_HM.Models
{
    ///<summary>
    ///装备主表
    ///</summary>
    [SugarTable("equipment")]
    public partial class equipment
    {
           public equipment(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:装备唯一ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string equip_id {get;set;}
 
          /// <summary>
           /// Desc:装备名称
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;}

           /// <summary>
           /// Desc:装备图标
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? icon {get;set;}

           /// <summary>
           /// Desc:装备类型ID（关联equipment_type）
           /// Default:
           /// Nullable:False
           /// </summary>
           public string equip_type_id {get;set;}

           /// <summary>
           /// Desc:五行属性
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? element {get;set;}

           /// <summary>
           /// Desc:扩展槽位
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? slot {get;set;}

           /// <summary>
           /// Desc:强化等级
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? strengthen_level {get;set;}

           /// <summary>
           /// Desc:套装ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? suit_id {get;set;}

    }
}
