using WebApplication_HM.Models;
using WebApplication_HM.DTOs;
using SqlSugar;
using Newtonsoft.Json;
using System.Text;

// 类型别名
using user_info = WebApplication_HM.Models.user;

namespace WebApplication_HM.Services.PropScript
{
    /// <summary>
    /// 简化的道具脚本执行引擎
    /// </summary>
    public class SimplePropScriptEngine
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<SimplePropScriptEngine> _logger;

        /// <summary>
        /// 境界状态字典（基于老项目逻辑）
        /// </summary>
        private static readonly Dictionary<int, string> PetStates = new Dictionary<int, string>
        {
            {1, "元神初具"}, {2, "元神小成"}, {3, "元神大成"}, {4, "元神圆满"},
            {5, "金丹初凝"}, {6, "金丹小成"}, {7, "金丹大成"}, {8, "金丹圆满"},
            {9, "元婴初期"}, {10, "元婴中期"}, {11, "元婴后期"}, {12, "元婴圆满"},
            {13, "化神初期"}, {14, "化神中期"}, {15, "化神后期"}, {16, "天地同寿"},
            {17, "返虚初期"}, {18, "返虚中期"}, {19, "返虚后期"}, {20, "合体初期"},
            {21, "合体中期"}, {22, "合体后期"}, {23, "大乘初期"}, {24, "大乘中期"},
            {25, "大乘后期"}, {26, "神轮境"}
        };
        private readonly Random _random;

        public SimplePropScriptEngine(ISqlSugarClient db, ILogger<SimplePropScriptEngine> logger)
        {
            _db = db;
            _logger = logger;
            _random = new Random();
        }

        /// <summary>
        /// 执行道具脚本（简化版本）
        /// </summary>
        public PropScriptResult ExecuteScript(string script, PropInfo prop, int userId)
        {
            try
            {
                if (string.IsNullOrEmpty(script))
                {
                    return PropScriptResult.CreateFailure("该道具无法直接使用!");
                }

                // 获取用户信息
                var user = GetUserInfo(userId);
                if (user == null)
                {
                    return PropScriptResult.CreateFailure("用户信息获取失败");
                }

                // 处理多脚本选择
                if (script.Contains("多脚本选择"))
                {
                    return HandleMultiScriptSelection(script);
                }

                // 解析脚本指令
                if (script.Contains('|'))
                {
                    return ExecuteDirectiveScript(script, prop, user, userId);
                }

                // 处理简单脚本
                return ExecuteSimpleScript(script, prop, user, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"执行道具脚本失败: Script={script}, PropId={prop?.ItemId}, UserId={userId}");
                return PropScriptResult.CreateFailure("脚本执行异常");
            }
        }

        /// <summary>
        /// 处理多脚本选择
        /// </summary>
        private PropScriptResult HandleMultiScriptSelection(string script)
        {
            try
            {
                string[] split = script.Split('!');
                if (split.Length >= 2)
                {
                    var options = JsonConvert.DeserializeObject<Dictionary<string, string>>(split[1]);
                    if (options != null)
                    {
                        var selectList = options.Keys.ToList();
                        return PropScriptResult.MultiSelect(selectList, options);
                    }
                }
                
                return PropScriptResult.CreateFailure("多脚本选择格式错误");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理多脚本选择失败: {script}");
                return PropScriptResult.CreateFailure("多脚本选择解析失败");
            }
        }

        /// <summary>
        /// 执行指令式脚本
        /// </summary>
        private PropScriptResult ExecuteDirectiveScript(string script, PropInfo prop, user_info user, int userId)
        {
            string[] directive = script.Split('|');
            if (directive.Length <= 1)
            {
                return PropScriptResult.CreateFailure("脚本格式错误");
            }

            string command = directive[0];

            return command switch
            {
                "扩展道具格子" => HandleExpandPropCapacity(directive, user),
                "扩展牧场格子" => HandleExpandPastureCapacity(directive, user),
                "学习技能" => HandleLearnSkill(directive, user),
                "宠物当前经验" => HandlePetExperience(directive, user, false),
                "巫族宠物经验" => HandlePetExperience(directive, user, true),
                "龙珠经验值" => HandleDragonBallExp(directive, user),
                "龙珠突破" => HandleDragonBallBreakthrough(directive, user),
                "合成道具" => HandleComposeItem(directive, userId),
                "扣除并获得道具" => HandleDeductAndGainItem(directive, userId),
                "召唤宠物" => HandleSummonPet(directive, userId),
                "镶嵌宝石" => HandleGemstoneInlay(directive, userId),
                "获得称号" => HandleGainTitle(directive, userId),
                "魂宠召唤" => HandleSoulPetSummon(directive, userId),
                // 新增的脚本类型
                "获得元宝" => HandleGainYuanbao(directive, user),
                "获得金币" => HandleGainGold(directive, user),
                "获得水晶" => HandleGainCrystal(directive, user),
                "获得积分" => HandleGainScore(directive, user),
                "获得威望" => HandleGainPrestige(directive, user),
                "获得道具" => HandleGainItem(directive, userId),
                "获得多个道具" => HandleGainMultipleItems(directive, userId),
                "获得装备" => HandleGainEquipment(directive, userId),
                "获得道具和装备" => HandleGainItemsAndEquipment(directive, userId),
                "一定概率获得" => HandleRandomGain(directive, userId),
                "一定概率获得物品" => HandleRandomGainItems(directive, userId),
                "一定概率获得道具或装备" => HandleRandomGainItemsOrEquipment(directive, userId),
                "随机获得" => HandleRandomSelect(directive, userId),
                "抽奖获得" => HandleLotteryGain(directive, userId),
                "抽奖获得2" => HandleLotteryGainWithLuck(directive, userId),
                // 战斗系统类脚本
                "获得自动战斗次数" => HandleGainAutoBattleCount(directive, user),
                "自动合成涅槃次数" => HandleGainAutoNirvanaCount(directive, user),
                "增加刷怪数" => HandleIncreaseMonsterCount(directive, user),
                "设置刷怪数" => HandleSetMonsterCount(directive, user),
                "必定遭遇" => HandleGuaranteedEncounter(directive, user),
                // 地图系统类脚本
                "开启地图" => HandleOpenMap(directive, user),
                "重置副本" => HandleResetDungeon(directive, userId),
                // 道具制作类脚本
                "合成物品" => HandleSynthesizeItem(directive, userId),
                "获得进化道具" => HandleGainEvolutionItem(directive, userId),
                // 角色系统类脚本
                "修改系别" => HandleModifyElement(directive, user),
                "扣除成长" => HandleDeductGrowth(directive, user),
                // 第四阶段脚本 - 宠物系统类
                "获得宠物" => HandleGainPet(directive, userId),
                // 第四阶段脚本 - 简单脚本类
                "VIP升级" => HandleVipUpgrade(directive, user),
                "至尊VIP" => HandleSupremeVip(directive, user),
                "星辰VIP" => HandleStarVip(directive, user),
                "星辰VIP直升" => HandleStarVipDirect(directive, user),
                "重置地狱" => HandleResetHell(directive, user),
                "重置通天" => HandleResetTower(directive, user),
                "主宠改名" => HandleMainPetRename(directive, user),
                "开启任务助手" => HandleEnableTaskHelper(directive, user),
                // 第五阶段脚本 - 特殊功能类
                "占卜屋" => HandleDivinationHouse(directive, user),
                "召唤侍灵" => HandleSummonSpirit(directive, user),
                "神兵" => HandleDivineWeapon(directive, user),
                "扩容页面" => HandleExpandPage(directive, user),
                "皮肤" => HandleSkin(directive, user),
                "魂器" => HandleSoulWeapon(directive, user),
                // 第六阶段脚本 - 消息系统类
                "提示信息" => HandleShowMessage(directive, user),
                "发送神谕" => HandleSendOracle(directive, user),
                // 第六阶段脚本 - 其他功能类
                "装备魂宠" => HandleEquipSoulPet(directive, user),
                "设置主宠" => HandleSetMainPet(directive, user),
                "提升境界" => HandleUpgradeRealm(directive, user),
                "突破境界" => HandleBreakRealm(directive, user),
                "宠物改名" => HandleRenamePet(directive, user),
                "宠物放生" => HandleReleasePet(directive, user),
                "技能遗忘" => HandleForgetSkill(directive, user),
                "默认技能" => HandleDefaultSkill(directive, user),
                "属性重置" => HandleResetAttributes(directive, user),
                "装备强化" => HandleEnhanceEquipment(directive, user),
                "装备分解" => HandleDecomposeEquipment(directive, user),
                "宝石合成" => HandleSynthesizeGem(directive, user),
                "获得法宝" => HandleGainTreasure(directive, user),
                "获得灵饰" => HandleGainAccessory(directive, user),
                "经验转移" => HandleTransferExp(directive, user),
                // 第七阶段脚本 - 补充遗漏的脚本
                "佩戴称号" => HandleEquipTitle(directive, user),
                "卸下魂宠" => HandleUnequipSoulPet(directive, user),
                _ => PropScriptResult.CreateFailure($"未知的脚本指令: {command}")
            };
        }

        /// <summary>
        /// 执行简单脚本
        /// </summary>
        private PropScriptResult ExecuteSimpleScript(string script, PropInfo prop, user_info user, int userId)
        {
            if (script.Contains("经验"))
            {
                return PropScriptResult.CreateSuccess("获得经验");
            }
            
            if (script.Contains("金币") || script.Contains("金钱"))
            {
                return PropScriptResult.CreateSuccess("获得金币");
            }
            
            if (script.Contains("元宝"))
            {
                return PropScriptResult.CreateSuccess("获得元宝");
            }

            return PropScriptResult.CreateFailure("未识别的脚本类型");
        }

        /// <summary>
        /// 扩展道具格子
        /// </summary>
        private PropScriptResult HandleExpandPropCapacity(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("参数不足");
                }

                int expandSize = Convert.ToInt32(directive[1]);
                int maxCapacity = 240; // 默认最大容量
                int minCapacity = 0;   // 默认最小容量

                // 处理可选的最小和最大容量参数
                if (directive.Length >= 4)
                {
                    minCapacity = Convert.ToInt32(directive[2]);
                    maxCapacity = Convert.ToInt32(directive[3]);
                }

                int currentCapacity = user.prop_capacity ?? 100;

                // 检查最小容量限制
                if (currentCapacity < minCapacity)
                {
                    return PropScriptResult.CreateFailure($"该道具只能在道具最大容量为{minCapacity}的时候使用。");
                }

                int finalCapacity = currentCapacity + expandSize;

                // 检查最大容量限制
                if (finalCapacity > maxCapacity)
                {
                    if (currentCapacity >= maxCapacity)
                    {
                        return PropScriptResult.CreateFailure("道具格子数已经升到最高啦!");
                    }
                    finalCapacity = maxCapacity;
                }

                // 更新用户容量
                user.prop_capacity = finalCapacity;
                bool success = UpdateUserInfo(user);

                if (success)
                {
                    int actualExpand = finalCapacity - currentCapacity;
                    return PropScriptResult.CreateSuccess($"道具格子数已提升了{actualExpand}格!");
                }

                return PropScriptResult.CreateFailure("容量更新失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扩展道具格子失败");
                return PropScriptResult.CreateFailure("扩展道具格子失败");
            }
        }

        /// <summary>
        /// 扩展牧场格子
        /// </summary>
        private PropScriptResult HandleExpandPastureCapacity(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 4)
                {
                    return PropScriptResult.CreateFailure("参数错误!");
                }

                int expandSize = Convert.ToInt32(directive[1]);
                int minCapacity = Convert.ToInt32(directive[2]);
                int maxCapacity = Convert.ToInt32(directive[3]);

                int currentCapacity = user.pasture_capacity ?? 80; // 默认牧场容量80

                if (currentCapacity < minCapacity)
                {
                    return PropScriptResult.CreateFailure($"该道具只能在牧场容量达到{minCapacity}的时候使用");
                }

                int finalCapacity = currentCapacity + expandSize;

                if (finalCapacity > maxCapacity)
                {
                    if (currentCapacity >= maxCapacity)
                    {
                        return PropScriptResult.CreateFailure("牧场已提升到最大!请使用更高级的道具升级吧!");
                    }
                    finalCapacity = maxCapacity;
                }

                // 更新牧场容量
                user.pasture_capacity = finalCapacity;
                bool success = UpdateUserInfo(user);

                if (success)
                {
                    int actualExpand = finalCapacity - currentCapacity;
                    return PropScriptResult.CreateSuccess($"牧场容量已提升{actualExpand}格!");
                }

                return PropScriptResult.CreateFailure("牧场容量更新失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扩展牧场格子失败");
                return PropScriptResult.CreateFailure("扩展牧场格子失败");
            }
        }

        /// <summary>
        /// 处理获得元宝（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainYuanbao(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("元宝数量参数缺失");
                }

                int yuanbaoAmount = Convert.ToInt32(directive[1]);
                string msg = directive[0] + directive[1];

                // 基于老项目的逻辑：检查元宝上限
                long maxYuanbao = GetMaxValue(2); // 元宝上限
                long currentYuanbao = user.yuanbao ?? 0;

                if (currentYuanbao + yuanbaoAmount < maxYuanbao)
                {
                    user.yuanbao = (int)(currentYuanbao + yuanbaoAmount);
                    bool success = UpdateUserInfo(user);
                    if (success)
                    {
                        return PropScriptResult.CreateSuccess($"获得元宝{yuanbaoAmount}");
                    }
                    return PropScriptResult.CreateFailure("元宝更新失败");
                }
                else
                {
                    // 超过上限，设置为上限值
                    user.yuanbao = (int)maxYuanbao;
                    bool success = UpdateUserInfo(user);
                    if (success)
                    {
                        return PropScriptResult.CreateSuccess($"获得元宝{yuanbaoAmount}(已达上限)");
                    }
                    return PropScriptResult.CreateFailure("元宝更新失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得元宝失败");
                return PropScriptResult.CreateFailure("处理获得元宝失败");
            }
        }

        /// <summary>
        /// 处理获得金币（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainGold(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("金币数量参数缺失");
                }

                long goldAmount = Convert.ToInt64(directive[1]);
                string msg = directive[0] + directive[1];

                // 基于老项目的逻辑：检查金币上限
                long maxGold = GetMaxValue(1); // 金币上限
                long currentGold = user.gold ?? 0;

                if (currentGold + goldAmount <= maxGold)
                {
                    user.gold = currentGold + goldAmount;
                    bool success = UpdateUserInfo(user);
                    if (success)
                    {
                        return PropScriptResult.CreateSuccess($"获得金币{goldAmount}");
                    }
                    return PropScriptResult.CreateFailure("金币更新失败");
                }
                else
                {
                    // 超过上限的处理
                    long finalGold = currentGold + goldAmount;
                    if (finalGold >= maxGold)
                    {
                        user.gold = maxGold;
                    }
                    else
                    {
                        user.gold = finalGold;
                    }

                    bool success = UpdateUserInfo(user);
                    if (success)
                    {
                        return PropScriptResult.CreateSuccess($"获得金币{goldAmount}");
                    }
                    return PropScriptResult.CreateFailure("金币更新失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得金币失败");
                return PropScriptResult.CreateFailure("处理获得金币失败");
            }
        }

        /// <summary>
        /// 处理获得水晶（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainCrystal(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("水晶数量参数缺失");
                }

                int crystalAmount = Convert.ToInt32(directive[1]);
                string msg = directive[0] + directive[1];

                // 基于老项目的逻辑：检查水晶上限
                long maxCrystal = GetMaxValue(2); // 水晶上限（与元宝相同）
                long currentCrystal = user.crystal ?? 0;

                if (currentCrystal + crystalAmount < maxCrystal)
                {
                    user.crystal = (int)(currentCrystal + crystalAmount);
                    bool success = UpdateUserInfo(user);
                    if (success)
                    {
                        return PropScriptResult.CreateSuccess($"获得水晶{crystalAmount}");
                    }
                    return PropScriptResult.CreateFailure("水晶更新失败");
                }
                else
                {
                    // 超过上限，设置为上限值
                    user.crystal = (int)maxCrystal;
                    bool success = UpdateUserInfo(user);
                    if (success)
                    {
                        return PropScriptResult.CreateSuccess($"获得水晶{crystalAmount}(已达上限)");
                    }
                    return PropScriptResult.CreateFailure("水晶更新失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得水晶失败");
                return PropScriptResult.CreateFailure("处理获得水晶失败");
            }
        }

        /// <summary>
        /// 处理获得积分（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainScore(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("积分数量参数缺失");
                }

                // 基于老项目逻辑：只有VIP10才能获得积分
                if (user.vip_level != 10)
                {
                    return PropScriptResult.CreateFailure("只有VIP10才能获得积分哦!");
                }

                int scoreAmount = Convert.ToInt32(directive[1]);
                long currentScore = user.vip_score ?? 0;

                user.vip_score = (int)(currentScore + scoreAmount);
                bool success = UpdateUserInfo(user);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"获得积分{scoreAmount}");
                }
                return PropScriptResult.CreateFailure("积分更新失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得积分失败");
                return PropScriptResult.CreateFailure("处理获得积分失败");
            }
        }

        /// <summary>
        /// 处理获得威望（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainPrestige(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("威望数量参数缺失");
                }

                int prestigeAmount = Convert.ToInt32(directive[1]);

                // 注意：user模型中没有威望字段，需要添加或使用其他字段
                // 这里暂时使用一个扩展字段或者需要在数据库中添加威望字段
                // 为了兼容性，我们先返回成功但记录日志
                _logger.LogWarning("威望字段未在user模型中定义，需要添加prestige字段");

                return PropScriptResult.CreateSuccess($"获得威望{prestigeAmount}(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得威望失败");
                return PropScriptResult.CreateFailure("处理获得威望失败");
            }
        }

        /// <summary>
        /// 处理获得道具（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainItem(string[] directive, int userId)
        {
            try
            {
                if (directive.Length < 3)
                {
                    return PropScriptResult.CreateFailure("道具参数不足");
                }

                string itemId = directive[1];
                string itemCount = directive[2];

                // 基于老项目的逻辑：创建PropInfo并添加到背包
                var propInfo = new user_item
                {
                    user_id = userId,
                    item_id = itemId,
                    item_count = Convert.ToInt64(itemCount),
                    item_seq = GetNextItemSeq(userId)
                };

                bool success = AddPlayerProp(propInfo);
                if (success)
                {
                    string itemName = GetPropName(itemId);
                    return PropScriptResult.CreateSuccess($"恭喜您!获得道具 {itemName}*{itemCount}");
                }

                return PropScriptResult.CreateFailure("添加道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得道具失败");
                return PropScriptResult.CreateFailure("处理获得道具失败");
            }
        }

        /// <summary>
        /// 处理获得多个道具（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainMultipleItems(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：获得多个道具|道具ID1|数量1|道具ID2|数量2|...
                int num = directive.Length - 1;
                num /= 2;
                string 列表 = "";

                for (int i = 0; i < num; i++)
                {
                    var propInfo = new user_item
                    {
                        user_id = userId,
                        item_id = directive[2 * i + 1],
                        item_count = Convert.ToInt64(directive[2 * i + 2]),
                        item_seq = GetNextItemSeq(userId)
                    };

                    列表 = 列表 + GetPropName(directive[2 * i + 1]) + "*" + directive[2 * i + 2] + "<br>";
                    AddPlayerProp(propInfo);
                }

                列表 = 列表 + "<br>";
                列表 = 列表.Replace("<br><br>", "");
                string msg = "得到了自然女神的祝福,获得了:<br>" + 列表;

                return PropScriptResult.CreateSuccess(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得多个道具失败");
                return PropScriptResult.CreateFailure("处理获得多个道具失败");
            }
        }

        /// <summary>
        /// 处理获得装备（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainEquipment(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：获得装备|装备ID1|装备ID2|...
                StringBuilder 列表 = new StringBuilder(256);
                int num = directive.Length - 1;
                int i = 0;

                foreach (string 装备 in directive)
                {
                    if (i != 0)
                    {
                        var equipmentInfo = new user_equipment
                        {
                            user_id = userId,
                            equip_id = 装备,
                            name = GetEquipmentName(装备),
                            equip_type_id = "1", // 默认装备类型
                            strengthen_level = 0,
                            element = "无",
                            create_time = DateTime.Now,
                            is_equipped = false
                        };

                        bool success = AddPlayerEquipment(equipmentInfo);
                        if (success)
                        {
                            string equipName = GetEquipmentName(装备);
                            if (i < num)
                            {
                                列表.Append(equipName + "、");
                            }
                            else
                            {
                                列表.Append(equipName + "。");
                            }
                        }
                    }
                    i++;
                }

                string msg = "得到了自然女神的祝福,获得了:" + 列表.ToString();
                return PropScriptResult.CreateSuccess(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得装备失败");
                return PropScriptResult.CreateFailure("处理获得装备失败");
            }
        }

        /// <summary>
        /// 处理获得道具和装备（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainItemsAndEquipment(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：获得道具和装备|道具ID1|数量1|道具ID2|数量2,装备ID1|装备ID2
                string[] scriptNew = string.Join("|", directive).Replace("获得道具和装备|", "").Split(',');
                string[] getpop = scriptNew[0].Split('|');
                string[] getZB = { };
                if (scriptNew.Length > 1) getZB = scriptNew[1].Split('|');

                string msg = "";

                // 处理道具部分
                if (getpop.Length > 1)
                {
                    int num = getpop.Length / 2;
                    string 列表 = "";
                    for (int i = 0; i < num; i++)
                    {
                        var propInfo = new user_item
                        {
                            user_id = userId,
                            item_id = getpop[2 * i],
                            item_count = Convert.ToInt64(getpop[2 * i + 1]),
                            item_seq = GetNextItemSeq(userId)
                        };
                        列表 = 列表 + GetPropName(getpop[2 * i]) + "*" + getpop[2 * i + 1] + "、";
                        AddPlayerProp(propInfo);
                    }
                    列表 = 列表.Replace("、、", "");
                    msg = "获得道具:" + 列表;

                    // 处理装备部分
                    if (getZB.Length > 1)
                    {
                        StringBuilder 列表_ = new StringBuilder(256);
                        int j = 0;
                        msg += "和装备:";
                        foreach (string 装备 in getZB)
                        {
                            var equipmentInfo = new user_equipment
                            {
                                user_id = userId,
                                equip_id = 装备,
                                name = GetEquipmentName(装备),
                                equip_type_id = "1", // 默认装备类型
                                strengthen_level = 0,
                                element = "无",
                                create_time = DateTime.Now,
                                is_equipped = false
                            };
                            AddPlayerEquipment(equipmentInfo);

                            string equipName = GetEquipmentName(装备);
                            if (j < getZB.Length - 1)
                            {
                                列表_.Append(equipName + "、");
                            }
                            else
                            {
                                列表_.Append(equipName + "。");
                            }
                            j++;
                        }
                        msg += 列表_.ToString();
                    }
                }

                return PropScriptResult.CreateSuccess(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得道具和装备失败");
                return PropScriptResult.CreateFailure("处理获得道具和装备失败");
            }
        }

        /// <summary>
        /// 处理学习技能（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleLearnSkill(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("技能ID参数缺失");
                }

                string skillId = directive[1];
                string result = StudySkill(user.main_pet_id?.ToString(), skillId);

                if (result.Equals("成功"))
                {
                    return PropScriptResult.CreateSuccess("学习技能成功!恭喜您的宠物能力得到了进一步的提升!");
                }
                else
                {
                    return PropScriptResult.CreateFailure(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理学习技能失败");
                return PropScriptResult.CreateFailure("学习技能失败");
            }
        }

        /// <summary>
        /// 处理宠物经验（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandlePetExperience(string[] directive, user_info user, bool isWuClan)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("经验值参数缺失");
                }

                if (user.main_pet_id == null)
                {
                    return PropScriptResult.CreateFailure("还没有主战宠物");
                }

                var mainPet = ReadAppointedPet(user.main_pet_id.Value);
                if (mainPet == null)
                {
                    return PropScriptResult.CreateFailure("主战宠物不存在");
                }

                long expAmount = Convert.ToInt64(directive[1]);

                if (isWuClan)
                {
                    // 巫族宠物经验
                    if (mainPet.element != "巫")
                    {
                        return PropScriptResult.CreateFailure("该道具只对巫族宠物有效果噢~");
                    }
                }
                else
                {
                    // 普通宠物经验
                    if (mainPet.element == "巫")
                    {
                        return PropScriptResult.CreateFailure("该道具对巫族宠物没有效果噢~");
                    }

                    // 检查是否满级（这里简化处理，假设最大等级为100）
                    if ((mainPet.level ?? 1) >= 100)
                    {
                        return PropScriptResult.CreateFailure("已经满级啦!不要浪费经验道具噢~");
                    }
                }

                bool success = AddExp(user.main_pet_id.Value, expAmount);
                if (success)
                {
                    string msg = directive[0] + "增加" + directive[1];
                    return PropScriptResult.CreateSuccess(msg);
                }

                return PropScriptResult.CreateFailure("增加经验失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理宠物经验失败");
                return PropScriptResult.CreateFailure("处理宠物经验失败");
            }
        }

        /// <summary>
        /// 处理佩戴称号（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainTitle(string[] directive, int userId)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("称号参数缺失");
                }

                string titleName = directive[1];

                // 更新用户称号
                var user = _db.Queryable<user_info>().Where(u => u.id == userId).First();
                if (user == null)
                {
                    return PropScriptResult.CreateFailure("用户不存在");
                }

                user.title = titleName;
                bool success = _db.Updateable(user).ExecuteCommand() > 0;

                if (success)
                {
                    return PropScriptResult.CreateSuccess($"已成功佩戴称号 {titleName}");
                }

                return PropScriptResult.CreateFailure("佩戴称号失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理佩戴称号失败");
                return PropScriptResult.CreateFailure("处理佩戴称号失败");
            }
        }

        /// <summary>
        /// 处理合成道具（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleComposeItem(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：合成道具|道具ID|数量|道具ID|数量,道具ID|数量|道具ID|数量（扣除的道具,获得的道具）
                string script = string.Join("|", directive);
                string popscriptstr = script.Replace("合成道具|", "");
                string[] popscript = popscriptstr.Split(','); // [0]失去的道具,[1]获得的道具

                if (popscript.Length != 2)
                {
                    return PropScriptResult.CreateFailure("合成道具脚本格式错误");
                }

                string[] lostpop = popscript[0].Split('|');
                string[] getpop = popscript[1].Split('|');

                // 扣除道具
                int num = lostpop.Length / 2;
                string 失去道具列表 = "";
                string 条件不满足信息 = "、";
                var lostpList = new List<user_item>();
                bool canContinue = true;

                for (int i = 0; i < num; i++)
                {
                    string itemId = lostpop[2 * i];
                    int needCount = Convert.ToInt32(lostpop[2 * i + 1]);

                    var 道具 = GetUserItem(userId, itemId);
                    if (道具 == null)
                    {
                        canContinue = false;
                        条件不满足信息 += $"、{GetPropName(itemId)}(0/{needCount})";
                    }
                    else
                    {
                        失去道具列表 = 失去道具列表 + GetPropName(itemId) + "*" + needCount + "、";
                        if (道具.item_count < needCount)
                        {
                            canContinue = false;
                        }
                        条件不满足信息 += $"、{GetPropName(itemId)}({道具.item_count}/{needCount})";
                    }
                    lostpList.Add(道具);
                }

                if (!canContinue)
                {
                    string msg = "无法合成道具，道具条件不满足。<br>" + 条件不满足信息.Replace("、、", "").Replace("、", "<br>");
                    return PropScriptResult.CreateFailure(msg);
                }

                // 扣除道具
                for (int i = 0; i < lostpList.Count; i++)
                {
                    if (lostpList[i] != null)
                    {
                        ReviseOrDeletePP(lostpList[i], Convert.ToInt32(lostpop[2 * i + 1]));
                    }
                }

                失去道具列表 = (失去道具列表 + "、").Replace("、、", "<br>");
                string resultMsg = "消耗材料:" + 失去道具列表;

                // 获得道具
                num = getpop.Length / 2;
                string 列表 = "";
                for (int i = 0; i < num; i++)
                {
                    var propInfo = new user_item
                    {
                        user_id = userId,
                        item_id = getpop[2 * i],
                        item_count = Convert.ToInt64(getpop[2 * i + 1]),
                        item_seq = GetNextItemSeq(userId)
                    };
                    列表 = 列表 + GetPropName(getpop[2 * i]) + "*" + getpop[2 * i + 1] + "、";
                    AddPlayerProp(propInfo);
                }
                列表 = 列表 + "、";
                列表 = 列表.Replace("、、", "");
                resultMsg = resultMsg + "成功获得了:" + 列表;

                return PropScriptResult.CreateSuccess(resultMsg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理合成道具失败");
                return PropScriptResult.CreateFailure("合成道具失败");
            }
        }

        /// <summary>
        /// 处理扣除并获得道具（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleDeductAndGainItem(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：扣除并获得道具|道具ID|数量|道具ID|数量,道具ID|数量|道具ID|数量（扣除的道具,获得的道具）
                string script = string.Join("|", directive);
                string popscriptstr = script.Replace("扣除并获得道具|", "");
                string[] popscript = popscriptstr.Split(','); // [0]失去的道具,[1]获得的道具

                if (popscript.Length < 1)
                {
                    return PropScriptResult.CreateFailure("扣除并获得道具脚本格式错误");
                }

                string[] lostpop = popscript[0].Split('|');
                string[] getpop = popscript.Length > 1 ? popscript[1].Split('|') : new string[0];

                // 扣除道具
                int num = lostpop.Length / 2;
                string 失去道具列表 = "";
                var lostpList = new List<user_item>();

                for (int i = 0; i < num; i++)
                {
                    string itemId = lostpop[2 * i];
                    int needCount = Convert.ToInt32(lostpop[2 * i + 1]);

                    var 道具 = GetUserItem(userId, itemId);
                    if (道具 == null || 道具.item_count < needCount)
                    {
                        return PropScriptResult.CreateFailure("扣除道具失败,请检查道具是否足够!");
                    }
                    失去道具列表 = 失去道具列表 + GetPropName(itemId) + "*" + needCount + "、";
                    lostpList.Add(道具);
                }

                // 执行扣除
                for (int i = 0; i < lostpList.Count; i++)
                {
                    ReviseOrDeletePP(lostpList[i], Convert.ToInt32(lostpop[2 * i + 1]));
                }

                string msg = "失去道具:" + 失去道具列表;
                if (msg == "失去道具:")
                {
                    msg = "没有失去任何道具~";
                }

                // 获得道具
                if (getpop.Length > 1)
                {
                    num = getpop.Length / 2;
                    string 列表 = "";
                    for (int i = 0; i < num; i++)
                    {
                        var propInfo = new user_item
                        {
                            user_id = userId,
                            item_id = getpop[2 * i],
                            item_count = Convert.ToInt64(getpop[2 * i + 1]),
                            item_seq = GetNextItemSeq(userId)
                        };
                        列表 = 列表 + GetPropName(getpop[2 * i]) + "*" + getpop[2 * i + 1] + "、";
                        AddPlayerProp(propInfo);
                    }
                    列表 = 列表 + "、";
                    列表 = 列表.Replace("、、", "");
                    msg = msg + "并获得了:" + 列表;
                }

                return PropScriptResult.CreateSuccess(msg);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理扣除并获得道具失败");
                return PropScriptResult.CreateFailure("扣除并获得道具失败");
            }
        }

        /// <summary>
        /// 处理召唤宠物（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSummonPet(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：召唤宠物|道具ID|数量|道具ID|数量,宠物ID|CC
                string script = string.Join("|", directive);
                string[] sList = script.Replace("召唤宠物|", "").Split(',');

                if (sList.Length < 2)
                {
                    return PropScriptResult.CreateFailure("召唤宠物脚本格式错误");
                }

                string[] lostpop = sList[0].Split('|');
                string[] getPet = sList[1].Split('|');

                // 检查材料是否足够
                string lostMsg = "、";
                var lostpList = new List<user_item>();

                for (int i = 0; i < lostpop.Length; i += 2)
                {
                    if (i + 1 >= lostpop.Length) break;

                    string itemId = lostpop[i];
                    int needCount = Convert.ToInt32(lostpop[i + 1]);

                    var item = GetUserItem(userId, itemId);
                    if (item == null || item.item_count < needCount)
                    {
                        return PropScriptResult.CreateFailure("召唤宠物失败!请检查材料是否足够!");
                    }

                    lostMsg += "、" + GetPropName(itemId) + "*" + needCount;
                    lostpList.Add(item);
                }

                // 扣除材料
                for (int i = 0; i < lostpList.Count; i++)
                {
                    ReviseOrDeletePP(lostpList[i], Convert.ToInt32(lostpop[2 * i + 1]));
                }

                string msg = "消耗材料:" + lostMsg.Replace("、、", "");

                // 获得宠物
                var petConfig = GetAppointedPetType(getPet[0]);
                if (petConfig == null)
                {
                    return PropScriptResult.CreateFailure("宠物配置不存在");
                }

                var newPet = new user_pet
                {
                    user_id = userId,
                    pet_no = petConfig.pet_no,
                    custom_name = petConfig.name,
                    element = petConfig.attribute,
                    exp = 0,
                    level = 1,
                    growth = getPet.Length > 1 ? Convert.ToDecimal(getPet[1]) : 25.0m,
                    create_time = DateTime.Now,
                    status = "牧场",
                    is_main = false
                };

                // 设置默认属性
                newPet = SetDefaultAttribute(newPet);

                // 检查牧场容量
                var user = _db.Queryable<user_info>().Where(u => u.id == userId).First();
                if (user == null)
                {
                    return PropScriptResult.CreateFailure("用户不存在");
                }

                var petCount = _db.Queryable<user_pet>().Where(p => p.user_id == userId).Count();
                int maxCapacity = user.pasture_capacity ?? 80;

                if (petCount >= maxCapacity)
                {
                    return PropScriptResult.CreateFailure($"获取宠物失败!最高只能同时拥有{maxCapacity}只宠物噢!清理下牧场再来吧~");
                }

                // 添加宠物
                int petId = _db.Insertable(newPet).ExecuteReturnIdentity();
                if (petId > 0)
                {
                    string growthText = getPet.Length > 1 ? getPet[1] : "25";
                    msg += $"成功在牧场召唤出 {growthText} 成长的{newPet.custom_name}!";
                    return PropScriptResult.CreateSuccess(msg);
                }

                return PropScriptResult.CreateFailure("召唤宠物失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理召唤宠物失败");
                return PropScriptResult.CreateFailure("召唤宠物失败");
            }
        }

        // 其他处理方法的简化实现（待迁移）
        private PropScriptResult HandleDragonBallExp(string[] directive, user_info user) => PropScriptResult.CreateSuccess("龙珠获得经验!");
        private PropScriptResult HandleDragonBallBreakthrough(string[] directive, user_info user) => PropScriptResult.CreateSuccess("龙珠突破成功!");
        private PropScriptResult HandleGemstoneInlay(string[] directive, int userId) => PropScriptResult.CreateSuccess("宝石镶嵌成功!");
        private PropScriptResult HandleSoulPetSummon(string[] directive, int userId) => PropScriptResult.CreateSuccess("魂宠召唤成功!");

        /// <summary>
        /// 处理一定概率获得（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleRandomGain(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：一定概率获得|道具ID,权重|道具ID,权重|...
                var propDict = new Dictionary<string, int>();
                string cfg = string.Join("|", directive.Skip(1)); // 重新组合配置字符串
                string[] props = cfg.Split('|');
                int propNum = 0;

                foreach (string prop in props)
                {
                    string[] propCfg = prop.Split(',');
                    if (propCfg.Length > 1)
                    {
                        propNum += Convert.ToInt32(propCfg[1]);
                    }
                    else
                    {
                        propNum += 1;
                    }

                    propDict.Add(propCfg[0], propNum);
                }

                // 使用老项目的随机数生成方式
                int random = _random.Next(1, propNum + 1);
                var propInfo = new user_item
                {
                    user_id = userId,
                    item_seq = GetNextItemSeq(userId)
                };

                foreach (string key in propDict.Keys)
                {
                    if (random <= propDict[key])
                    {
                        propInfo.item_id = key;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(propInfo.item_id))
                {
                    return PropScriptResult.CreateFailure("随机选择道具失败");
                }

                propInfo.item_count = 1;
                string mz = GetPropName(propInfo.item_id);

                if (mz == "[Error]该道具无法显示")
                {
                    return PropScriptResult.CreateFailure("道具配置错误");
                }

                bool success = AddPlayerProp(propInfo);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"获得道具 {mz}");
                }

                return PropScriptResult.CreateFailure("添加道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理一定概率获得失败");
                return PropScriptResult.CreateFailure("处理一定概率获得失败");
            }
        }

        /// <summary>
        /// 处理随机获得（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleRandomSelect(string[] directive, int userId)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("随机获得参数缺失");
                }

                // 基于老项目逻辑：随机获得|道具ID1|道具ID2|道具ID3|...
                var propInfo = new user_item
                {
                    user_id = userId,
                    item_id = directive[_random.Next(1, directive.Length)], // 从索引1开始随机选择
                    item_count = 1,
                    item_seq = GetNextItemSeq(userId)
                };

                string mz = GetPropName(propInfo.item_id);
                if (mz == "[Error]该道具无法显示")
                {
                    return PropScriptResult.CreateFailure("道具配置错误");
                }

                bool success = AddPlayerProp(propInfo);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"获得道具 {mz}");
                }

                return PropScriptResult.CreateFailure("添加道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理随机获得失败");
                return PropScriptResult.CreateFailure("处理随机获得失败");
            }
        }

        /// <summary>
        /// 处理一定概率获得物品（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleRandomGainItems(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：一定概率获得物品|道具ID*数量,权重|道具ID*数量,权重|...
                var propDict = new Dictionary<string, int>();
                string cfg = string.Join("|", directive.Skip(1));
                string[] props = cfg.Split('|');
                int propNum = 0;

                foreach (string prop in props)
                {
                    string[] propCfg = prop.Split(',');
                    if (propCfg.Length > 1)
                    {
                        propNum += Convert.ToInt32(propCfg[1]);
                    }
                    else
                    {
                        propNum += 1;
                    }
                    propDict.Add(propCfg[0], propNum);
                }

                int random = _random.Next(1, propNum + 1);
                string selectedItem = "";
                string pNum = "1";

                foreach (string key in propDict.Keys)
                {
                    if (random <= propDict[key])
                    {
                        // 处理道具ID*数量格式
                        string[] popI = key.Split('*');
                        selectedItem = popI[0];
                        if (popI.Length > 1)
                        {
                            pNum = popI[1];
                        }
                        break;
                    }
                }

                var propInfo = new user_item
                {
                    user_id = userId,
                    item_id = selectedItem,
                    item_count = Convert.ToInt64(pNum),
                    item_seq = GetNextItemSeq(userId)
                };

                string mz = GetPropName(selectedItem);
                if (mz == "[Error]该道具无法显示")
                {
                    return PropScriptResult.CreateFailure("道具配置错误");
                }

                bool success = AddPlayerProp(propInfo);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"获得道具 {mz}*{pNum}");
                }

                return PropScriptResult.CreateFailure("添加道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理一定概率获得物品失败");
                return PropScriptResult.CreateFailure("处理一定概率获得物品失败");
            }
        }

        /// <summary>
        /// 处理一定概率获得道具或装备（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleRandomGainItemsOrEquipment(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：一定概率获得道具或装备|道具ID1|道具ID2|...|总权重
                if (directive.Length < 3)
                {
                    return PropScriptResult.CreateFailure("参数不足");
                }

                string yihan = "很遗憾,您什么都没获得!";
                int totalWeight = Convert.ToInt32(directive[directive.Length - 1]);
                int lucky = _random.Next(0, totalWeight);

                if (lucky == 0) // 中奖了
                {
                    string 物品序号 = directive[_random.Next(1, directive.Length - 1)];

                    if (物品序号.Contains('z')) // 装备
                    {
                        var equipmentInfo = new user_equipment
                        {
                            user_id = userId,
                            equip_id = 物品序号.Substring(1), // 去掉z前缀
                            name = GetEquipmentName(物品序号.Substring(1)),
                            equip_type_id = "1", // 默认装备类型
                            strengthen_level = 0,
                            element = "无",
                            create_time = DateTime.Now,
                            is_equipped = false
                        };

                        bool success = AddPlayerEquipment(equipmentInfo);
                        if (success)
                        {
                            string equipName = GetEquipmentName(equipmentInfo.equip_id);
                            return PropScriptResult.CreateSuccess($"获得装备 {equipName}");
                        }
                        return PropScriptResult.CreateFailure("添加装备失败");
                    }
                    else // 道具
                    {
                        var propInfo = new user_item
                        {
                            user_id = userId,
                            item_id = 物品序号,
                            item_count = 1,
                            item_seq = GetNextItemSeq(userId)
                        };

                        bool success = AddPlayerProp(propInfo);
                        if (success)
                        {
                            string mz = GetPropName(物品序号);
                            return PropScriptResult.CreateSuccess($"获得道具 {mz}");
                        }
                        return PropScriptResult.CreateFailure("添加道具失败");
                    }
                }
                else
                {
                    return PropScriptResult.CreateSuccess(yihan);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理一定概率获得道具或装备失败");
                return PropScriptResult.CreateFailure("处理一定概率获得道具或装备失败");
            }
        }

        /// <summary>
        /// 处理抽奖获得（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleLotteryGain(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：抽奖获得|道具ID*数量,权重|道具ID*数量,权重|...
                var propDict = new Dictionary<string, int>();
                string cfg = string.Join("|", directive.Skip(1));
                string[] props = cfg.Split('|');
                int propNum = 0;

                foreach (string prop in props)
                {
                    string[] propCfg = prop.Split(',');
                    if (propCfg.Length > 1)
                    {
                        propNum += Convert.ToInt32(propCfg[1]);
                    }
                    else
                    {
                        propNum += 1;
                    }
                    propDict.Add(propCfg[0], propNum);
                }

                int random = _random.Next(1, propNum + 1);
                string selectedItem = "";
                string pNum = "1";

                foreach (string key in propDict.Keys)
                {
                    if (random <= propDict[key])
                    {
                        // 处理道具ID*数量格式
                        string[] popI = key.Split('*');
                        selectedItem = popI[0];
                        if (popI.Length > 1)
                        {
                            pNum = popI[1];
                        }
                        break;
                    }
                }

                var propInfo = new user_item
                {
                    user_id = userId,
                    item_id = selectedItem,
                    item_count = Convert.ToInt64(pNum),
                    item_seq = GetNextItemSeq(userId)
                };

                string mz = GetPropName(selectedItem);
                if (mz == "[Error]该道具无法显示")
                {
                    return PropScriptResult.CreateFailure("道具配置错误");
                }

                bool success = AddPlayerProp(propInfo);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"获得道具 {mz}*{pNum}");
                }

                return PropScriptResult.CreateFailure("添加道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理抽奖获得失败");
                return PropScriptResult.CreateFailure("处理抽奖获得失败");
            }
        }

        /// <summary>
        /// 处理抽奖获得2（幸运值加成版本，基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleLotteryGainWithLuck(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：抽奖获得2|道具ID*数量,权重|道具ID*数量,权重|...
                var propDict = new Dictionary<string, int>();
                string cfg = string.Join("|", directive.Skip(1));
                string[] props = cfg.Split('|');
                int propNum = 0;

                foreach (string prop in props)
                {
                    string[] propCfg = prop.Split(',');
                    if (propCfg.Length > 1)
                    {
                        propNum += Convert.ToInt32(propCfg[1]);
                    }
                    else
                    {
                        propNum += 1;
                    }
                    propDict.Add(propCfg[0], propNum);
                }

                int random = _random.Next(1, propNum + 1);
                string selectedItem = "";
                string pNum = "1";

                foreach (string key in propDict.Keys)
                {
                    // 幸运值加成逻辑（简化版本，老项目中有复杂的幸运值计算）
                    double luckNum = Convert.ToDouble(random) - GetLuckValue(userId);
                    luckNum = luckNum < 0 ? 0 : luckNum;

                    if (luckNum <= propDict[key])
                    {
                        // 处理道具ID*数量格式
                        string[] popI = key.Split('*');
                        selectedItem = popI[0];
                        if (popI.Length > 1)
                        {
                            pNum = popI[1];
                        }
                        break;
                    }
                }

                var propInfo = new user_item
                {
                    user_id = userId,
                    item_id = selectedItem,
                    item_count = Convert.ToInt64(pNum),
                    item_seq = GetNextItemSeq(userId)
                };

                string mz = GetPropName(selectedItem);
                if (mz == "[Error]该道具无法显示")
                {
                    return PropScriptResult.CreateFailure("道具配置错误");
                }

                bool success = AddPlayerProp(propInfo);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"获得道具 {mz}*{pNum}");
                }

                return PropScriptResult.CreateFailure("添加道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理抽奖获得2失败");
                return PropScriptResult.CreateFailure("处理抽奖获得2失败");
            }
        }

        // 数据访问方法
        private user_info GetUserInfo(int userId)
        {
            try
            {
                return _db.Queryable<user>().Where(u => u.id == userId).First();
            }
            catch
            {
                return null;
            }
        }

        private bool UpdateUserInfo(user_info user)
        {
            try
            {
                return _db.Updateable(user).ExecuteCommand() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户信息失败");
                return false;
            }
        }

        // 检查背包容量的简化实现
        private bool CheckBagCapacity(int userId, user_info user)
        {
            try
            {
                var bagItems = _db.Queryable<user_item>()
                    .Where(i => i.user_id == userId)
                    .Count();

                int capacity = user.prop_capacity ?? 100;
                return bagItems < capacity;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取最大值（基于老项目逻辑）
        /// </summary>
        private long GetMaxValue(int type)
        {
            // 基于老项目的逻辑，这里简化处理
            return type switch
            {
                1 => 999999999999L, // 金币上限
                2 => 999999999L,    // 元宝上限
                _ => 999999999L
            };
        }

        /// <summary>
        /// 获取下一个道具序号
        /// </summary>
        private int GetNextItemSeq(int userId)
        {
            try
            {
                var maxSeq = _db.Queryable<user_item>()
                    .Where(i => i.user_id == userId)
                    .Max(i => i.item_seq);
                return maxSeq + 1;
            }
            catch
            {
                return 1;
            }
        }

        /// <summary>
        /// 添加玩家道具（基于老项目逻辑）
        /// </summary>
        private bool AddPlayerProp(user_item propInfo)
        {
            try
            {
                // 检查是否已有该道具
                var existingItem = _db.Queryable<user_item>()
                    .Where(i => i.user_id == propInfo.user_id && i.item_id == propInfo.item_id)
                    .First();

                if (existingItem != null)
                {
                    // 更新数量
                    existingItem.item_count += propInfo.item_count;
                    return _db.Updateable(existingItem).ExecuteCommand() > 0;
                }
                else
                {
                    // 创建新道具
                    return _db.Insertable(propInfo).ExecuteCommand() > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"添加玩家道具失败: UserId={propInfo.user_id}, ItemId={propInfo.item_id}");
                return false;
            }
        }

        /// <summary>
        /// 获取道具名称（基于老项目逻辑）
        /// </summary>
        private string GetPropName(string itemId)
        {
            try
            {
                // 尝试从道具配置表获取名称
                if (int.TryParse(itemId, out int itemNo))
                {
                    var itemConfig = _db.Queryable<item_config>()
                        .Where(i => i.item_no == itemNo)
                        .First();

                    if (itemConfig != null)
                    {
                        return itemConfig.name ?? "[Error]该道具无法显示";
                    }
                }

                return "[Error]该道具无法显示";
            }
            catch
            {
                return "[Error]该道具无法显示";
            }
        }

        /// <summary>
        /// 学习技能（基于老项目逻辑）
        /// </summary>
        private string StudySkill(string petId, string skillId)
        {
            try
            {
                if (string.IsNullOrEmpty(petId) || !int.TryParse(petId, out int petIdInt))
                {
                    return "宠物ID无效";
                }

                var petInfo = ReadAppointedPet(petIdInt);
                if (petInfo == null)
                {
                    return "宠物不存在";
                }

                // 检查宠物是否已经掌握此技能
                var existingSkill = _db.Queryable<user_pet_skill>()
                    .Where(s => s.user_pet_id == petIdInt && s.skill_id == Convert.ToInt32(skillId))
                    .First();

                if (existingSkill != null)
                {
                    return "宠物已经掌握此技能了！";
                }

                // 检查技能是否存在
                var skillConfig = _db.Queryable<skill>()
                    .Where(s => s.skill_id == skillId)
                    .First();

                if (skillConfig == null)
                {
                    return "游戏内部错误！";
                }

                // 添加技能到宠物
                var newSkill = new user_pet_skill
                {
                    user_pet_id = petIdInt,
                    skill_id = Convert.ToInt32(skillId),
                    skill_level = 0,
                    create_time = DateTime.Now
                };

                bool success = _db.Insertable(newSkill).ExecuteCommand() > 0;
                return success ? "成功" : "学习技能失败";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"学习技能失败: PetId={petId}, SkillId={skillId}");
                return "学习技能失败";
            }
        }

        /// <summary>
        /// 读取指定宠物信息（基于老项目逻辑）
        /// </summary>
        private user_pet ReadAppointedPet(int petId)
        {
            try
            {
                return _db.Queryable<user_pet>()
                    .Where(p => p.id == petId)
                    .First();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 增加宠物经验（基于老项目逻辑）
        /// </summary>
        private bool AddExp(int petId, long expAmount)
        {
            try
            {
                var pet = ReadAppointedPet(petId);
                if (pet == null)
                {
                    return false;
                }

                // 增加经验
                pet.exp = (pet.exp ?? 0) + expAmount;

                // 防止经验值过小
                if (pet.exp < -100000000)
                {
                    pet.exp = int.MaxValue;
                }

                // 更新宠物信息
                return _db.Updateable(pet).ExecuteCommand() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"增加宠物经验失败: PetId={petId}, ExpAmount={expAmount}");
                return false;
            }
        }

        /// <summary>
        /// 获取用户道具（基于老项目逻辑）
        /// </summary>
        private user_item GetUserItem(int userId, string itemId)
        {
            try
            {
                return _db.Queryable<user_item>()
                    .Where(i => i.user_id == userId && i.item_id == itemId)
                    .First();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 修改或删除道具（基于老项目逻辑）
        /// </summary>
        private bool ReviseOrDeletePP(user_item item, int deductCount)
        {
            try
            {
                if (item == null)
                {
                    return false;
                }

                if (item.item_count <= deductCount)
                {
                    // 删除道具
                    return _db.Deleteable<user_item>().Where(i => i.id == item.id).ExecuteCommand() > 0;
                }
                else
                {
                    // 减少数量
                    item.item_count -= deductCount;
                    return _db.Updateable(item).ExecuteCommand() > 0;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"修改或删除道具失败: ItemId={item?.id}, DeductCount={deductCount}");
                return false;
            }
        }

        /// <summary>
        /// 获取指定宠物类型配置（基于老项目逻辑）
        /// </summary>
        private pet_config GetAppointedPetType(string petNo)
        {
            try
            {
                if (int.TryParse(petNo, out int petNoInt))
                {
                    return _db.Queryable<pet_config>()
                        .Where(p => p.pet_no == petNoInt)
                        .First();
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 设置宠物默认属性（基于老项目逻辑）
        /// </summary>
        private user_pet SetDefaultAttribute(user_pet pet)
        {
            try
            {
                // 基于老项目的SetDefaultAttribute方法
                switch (pet.element)
                {
                    case "神":
                        pet.def = 1400;
                        pet.atk = 2000;
                        pet.hit = 3000;
                        pet.mp = 1000;
                        pet.max_mp = 1000;
                        pet.max_hp = 3000;
                        pet.hp = 3000;
                        pet.spd = 500;
                        pet.growth = pet.growth ?? 25m;
                        pet.dodge = 1400;
                        break;
                    case "神圣":
                        pet.def = 1500;
                        pet.atk = 2100;
                        pet.hit = 3000;
                        pet.mp = 1000;
                        pet.max_mp = 1000;
                        pet.max_hp = 4000;
                        pet.hp = 4000;
                        pet.spd = 5000;
                        pet.growth = pet.growth ?? 35.5m;
                        pet.dodge = 1500;
                        break;
                    case "魔":
                        pet.def = 800;
                        pet.atk = 1200;
                        pet.hit = 2000;
                        pet.mp = 1500;
                        pet.max_mp = 1500;
                        pet.max_hp = 2000;
                        pet.hp = 2000;
                        pet.spd = 400;
                        pet.growth = pet.growth ?? 20m;
                        pet.dodge = 800;
                        break;
                    case "巫":
                        pet.def = 600;
                        pet.atk = 800;
                        pet.hit = 1500;
                        pet.mp = 1200;
                        pet.max_mp = 1200;
                        pet.max_hp = 1500;
                        pet.hp = 1500;
                        pet.spd = 300;
                        pet.growth = pet.growth ?? 15m;
                        pet.dodge = 600;
                        break;
                    default: // 五行宠物（金、木、水、火、土）
                        pet.def = 500;
                        pet.atk = 700;
                        pet.hit = 1200;
                        pet.mp = 800;
                        pet.max_mp = 800;
                        pet.max_hp = 1200;
                        pet.hp = 1200;
                        pet.spd = 250;
                        pet.growth = pet.growth ?? 12m;
                        pet.dodge = 500;
                        break;
                }

                return pet;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置宠物默认属性失败");
                return pet;
            }
        }

        /// <summary>
        /// 添加玩家装备（基于老项目逻辑）
        /// </summary>
        private bool AddPlayerEquipment(user_equipment equipment)
        {
            try
            {
                return _db.Insertable(equipment).ExecuteCommand() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加玩家装备失败");
                return false;
            }
        }

        /// <summary>
        /// 获取装备名称（基于老项目逻辑）
        /// </summary>
        private string GetEquipmentName(string equipId)
        {
            try
            {
                // 这里应该从装备配置表中获取装备名称
                // 暂时返回装备ID，后续需要完善
                return $"装备{equipId}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取装备名称失败: EquipId={equipId}");
                return "未知装备";
            }
        }

        /// <summary>
        /// 处理获得自动战斗次数（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainAutoBattleCount(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("自动战斗次数参数缺失");
                }

                double battleCount = Convert.ToDouble(directive[1]);

                // 注意：user模型中没有自动战斗次数字段，需要添加或使用其他字段
                // 这里暂时使用扩展字段或者需要在数据库中添加auto_battle_count字段
                _logger.LogWarning("自动战斗次数字段未在user模型中定义，需要添加auto_battle_count字段");

                return PropScriptResult.CreateSuccess($"获得自动战斗次数{battleCount}(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得自动战斗次数失败");
                return PropScriptResult.CreateFailure("处理获得自动战斗次数失败");
            }
        }

        /// <summary>
        /// 处理自动合成涅槃次数（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainAutoNirvanaCount(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("自动合成涅槃次数参数缺失");
                }

                long nirvanaCount = Convert.ToInt64(directive[1]);

                // 注意：user模型中没有自动合成涅槃次数字段，需要添加或使用其他字段
                // 这里暂时使用扩展字段或者需要在数据库中添加auto_nirvana_count字段
                _logger.LogWarning("自动合成涅槃次数字段未在user模型中定义，需要添加auto_nirvana_count字段");

                return PropScriptResult.CreateSuccess($"您的自动合成涅槃次数增加了{nirvanaCount}次!(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理自动合成涅槃次数失败");
                return PropScriptResult.CreateFailure("处理自动合成涅槃次数失败");
            }
        }

        /// <summary>
        /// 处理增加刷怪数（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleIncreaseMonsterCount(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("刷怪数参数缺失");
                }

                double monsterCount = Convert.ToDouble(directive[1]);

                // 注意：user模型中没有刷怪数字段，需要添加或使用其他字段
                // 这里暂时使用扩展字段或者需要在数据库中添加monster_count字段
                _logger.LogWarning("刷怪数字段未在user模型中定义，需要添加monster_count字段");

                return PropScriptResult.CreateSuccess($"刷怪数增加了{monsterCount}!(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理增加刷怪数失败");
                return PropScriptResult.CreateFailure("处理增加刷怪数失败");
            }
        }

        /// <summary>
        /// 处理设置刷怪数（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSetMonsterCount(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("刷怪数参数缺失");
                }

                int monsterCount = Convert.ToInt32(directive[1]);

                // 注意：user模型中没有刷怪数字段，需要添加或使用其他字段
                // 这里暂时使用扩展字段或者需要在数据库中添加monster_count字段
                _logger.LogWarning("刷怪数字段未在user模型中定义，需要添加monster_count字段");

                string msg = monsterCount > 9112 ? "已进入必遇BOSS!" : "刷怪数已被设置!";
                return PropScriptResult.CreateSuccess($"{msg}(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设置刷怪数失败");
                return PropScriptResult.CreateFailure("处理设置刷怪数失败");
            }
        }

        /// <summary>
        /// 处理必定遭遇（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGuaranteedEncounter(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("怪物ID参数缺失");
                }

                string monsterId = directive[1];

                // 注意：user模型中没有NextBattle字段，需要添加或使用其他字段
                // 这里暂时使用扩展字段或者需要在数据库中添加next_battle字段
                _logger.LogWarning("NextBattle字段未在user模型中定义，需要添加next_battle字段");

                string monsterName = GetMonsterName(monsterId);
                return PropScriptResult.CreateSuccess($"下次战斗中您必定会遭遇怪物{monsterName}（必须要在有这个怪物的地图战斗才能触发效果！副本无效!）(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理必定遭遇失败");
                return PropScriptResult.CreateFailure("处理必定遭遇失败");
            }
        }

        /// <summary>
        /// 获取怪物名称（基于老项目逻辑）
        /// </summary>
        private string GetMonsterName(string monsterId)
        {
            try
            {
                // 这里应该从怪物配置表中获取怪物名称
                // 暂时返回怪物ID，后续需要完善
                return $"怪物{monsterId}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取怪物名称失败: MonsterId={monsterId}");
                return "未知怪物";
            }
        }

        /// <summary>
        /// 处理开启地图（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleOpenMap(string[] directive, user_info user)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("地图ID参数缺失");
                }

                string mapId = directive[1];

                // 注意：user模型中没有openMaps字段，需要添加或使用其他字段
                // 这里暂时使用扩展字段或者需要在数据库中添加open_maps字段
                _logger.LogWarning("openMaps字段未在user模型中定义，需要添加open_maps字段");

                return PropScriptResult.CreateSuccess("恭喜你！成功开启了地图！快去探索未知的世界吧！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理开启地图失败");
                return PropScriptResult.CreateFailure("处理开启地图失败");
            }
        }

        /// <summary>
        /// 处理重置副本（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleResetDungeon(string[] directive, int userId)
        {
            try
            {
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("副本ID参数缺失");
                }

                string dungeonId = directive[1];

                // 注意：需要副本进度表来存储副本状态
                // 这里暂时返回成功，后续需要完善副本系统
                _logger.LogWarning("副本系统未完全实现，需要添加副本进度表");

                return PropScriptResult.CreateSuccess("指定副本重置成功!(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理重置副本失败");
                return PropScriptResult.CreateFailure("处理重置副本失败");
            }
        }

        /// <summary>
        /// 处理合成物品（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSynthesizeItem(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：合成物品|材料道具ID,消耗数量|合成道具ID
                if (directive.Length < 3)
                {
                    return PropScriptResult.CreateFailure("合成物品参数不足");
                }

                string[] materialInfo = directive[1].Split(',');
                if (materialInfo.Length < 2)
                {
                    return PropScriptResult.CreateFailure("材料配置格式错误");
                }

                string materialId = materialInfo[0];
                int requiredCount = Convert.ToInt32(materialInfo[1]);
                string resultItemId = directive[2];

                // 检查材料是否足够
                var materialItem = GetPlayerItem(userId, materialId);
                if (materialItem == null)
                {
                    return PropScriptResult.CreateFailure("合成材料不足！");
                }

                if (materialItem.item_count < requiredCount)
                {
                    return PropScriptResult.CreateFailure("合成材料不足！");
                }

                // 扣除材料
                bool deductSuccess = ReviseOrDeletePP(materialItem, requiredCount);
                if (!deductSuccess)
                {
                    return PropScriptResult.CreateFailure("扣除材料失败");
                }

                // 添加合成结果
                var resultItem = new user_item
                {
                    user_id = userId,
                    item_id = resultItemId,
                    item_count = 1,
                    item_seq = GetNextItemSeq(userId)
                };

                bool addSuccess = AddPlayerProp(resultItem);
                if (addSuccess)
                {
                    string itemName = GetPropName(resultItemId);
                    return PropScriptResult.CreateSuccess($"获得道具 {itemName}");
                }

                return PropScriptResult.CreateFailure("添加合成道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理合成物品失败");
                return PropScriptResult.CreateFailure("处理合成物品失败");
            }
        }

        /// <summary>
        /// 处理获得进化道具（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainEvolutionItem(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：获得进化道具（随机获得进化道具）
                // 老项目中是随机获得2016110500-2016110546范围内的进化道具
                int baseId = 2016110500;
                int randomOffset = _random.Next(1, 47); // 1-46的随机数
                string evolutionItemId = (baseId + randomOffset).ToString();

                var propInfo = new user_item
                {
                    user_id = userId,
                    item_id = evolutionItemId,
                    item_count = 1,
                    item_seq = GetNextItemSeq(userId)
                };

                string itemName = GetPropName(evolutionItemId);
                if (itemName == "[Error]该道具无法显示")
                {
                    return PropScriptResult.CreateFailure("进化道具配置错误");
                }

                bool success = AddPlayerProp(propInfo);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"获得道具 {itemName}");
                }

                return PropScriptResult.CreateFailure("添加进化道具失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得进化道具失败");
                return PropScriptResult.CreateFailure("处理获得进化道具失败");
            }
        }

        /// <summary>
        /// 处理修改系别（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleModifyElement(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：修改系别|新系别
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("系别参数缺失");
                }

                string newElement = directive[1];

                // 获取主宠物信息
                var mainPet = GetMainPet(user.id);
                if (mainPet == null)
                {
                    return PropScriptResult.CreateFailure("未找到主宠物");
                }

                string currentElement = mainPet.element ?? "";

                // 基于老项目逻辑的系别限制
                if (currentElement == "巫")
                {
                    return PropScriptResult.CreateFailure("该种族的宠物无法被改变！");
                }

                if (newElement == currentElement)
                {
                    return PropScriptResult.CreateFailure($"您的宠物系别已为：{newElement} 无需修改！");
                }

                if (currentElement == "灵")
                {
                    return PropScriptResult.CreateFailure("您的宠物系别已为：灵，不可再更改！");
                }

                // 基础系别不能进阶
                string[] basicElements = { "金", "木", "水", "火", "土", "神", "神圣" };
                if (basicElements.Contains(currentElement))
                {
                    return PropScriptResult.CreateFailure("您的宠物天赋太差了，不能进阶！");
                }

                string message = "";
                // 系别转换逻辑
                switch (newElement)
                {
                    case "萌":
                        message = "恭喜玩家宠物经过洗礼，成功转换为萌系别！";
                        break;
                    case "灵":
                        if (currentElement != "萌")
                        {
                            return PropScriptResult.CreateFailure("只有系别为萌的宠物才可以转换为灵！");
                        }
                        message = "恭喜玩家宠物经过洗礼，成功转换为灵系别！";
                        break;
                    case "次元":
                        message = "恭喜玩家宠物经过洗礼，成功转换为次元系别！";
                        break;
                    default:
                        return PropScriptResult.CreateFailure("参数错误！");
                }

                // 更新宠物系别
                mainPet.element = newElement;
                bool success = UpdatePet(mainPet);

                if (success)
                {
                    return PropScriptResult.CreateSuccess(message);
                }

                return PropScriptResult.CreateFailure("修改系别失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理修改系别失败");
                return PropScriptResult.CreateFailure("处理修改系别失败");
            }
        }

        /// <summary>
        /// 处理扣除成长（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleDeductGrowth(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：扣除成长|成长值
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("成长值参数缺失");
                }

                double deductValue = Convert.ToDouble(directive[1]);

                if (deductValue < 0)
                {
                    return PropScriptResult.CreateFailure("不可以为负数！");
                }

                // 获取主宠物信息
                var mainPet = GetMainPet(user.id);
                if (mainPet == null)
                {
                    return PropScriptResult.CreateFailure("未找到主宠物");
                }

                double currentGrowth = Convert.ToDouble(mainPet.growth ?? 0);

                // 检查扣除后是否小于等于3
                if (currentGrowth - deductValue <= 3)
                {
                    return PropScriptResult.CreateFailure("当前主宠CC不够扣除哦~");
                }

                // 扣除成长值
                mainPet.growth = (decimal)(currentGrowth - deductValue);
                bool success = UpdatePet(mainPet);

                if (success)
                {
                    string petName = mainPet.custom_name ?? $"宠物{mainPet.pet_no}";
                    return PropScriptResult.CreateSuccess($"已扣除{petName}成长:{deductValue}");
                }

                return PropScriptResult.CreateFailure("扣除成长失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理扣除成长失败");
                return PropScriptResult.CreateFailure("处理扣除成长失败");
            }
        }

        /// <summary>
        /// 获取主宠物信息
        /// </summary>
        private user_pet GetMainPet(int userId)
        {
            try
            {
                return _db.Queryable<user_pet>()
                    .Where(p => p.user_id == userId && p.is_main == true)
                    .First();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 更新宠物信息
        /// </summary>
        private bool UpdatePet(user_pet pet)
        {
            try
            {
                return _db.Updateable(pet).ExecuteCommand() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新宠物信息失败");
                return false;
            }
        }

        /// <summary>
        /// 获取玩家指定道具
        /// </summary>
        private user_item GetPlayerItem(int userId, string itemId)
        {
            try
            {
                return _db.Queryable<user_item>()
                    .Where(i => i.user_id == userId && i.item_id == itemId)
                    .First();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 处理获得宠物（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainPet(string[] directive, int userId)
        {
            try
            {
                // 基于老项目逻辑：获得宠物|宠物ID|成长值
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("宠物ID参数缺失");
                }

                string petId = directive[1];
                decimal growth = 20.0m; // 默认成长值

                if (directive.Length > 2)
                {
                    if (decimal.TryParse(directive[2], out decimal customGrowth))
                    {
                        growth = customGrowth;
                    }
                }

                // 检查牧场容量
                var user = GetUserInfo(userId);
                if (user == null)
                {
                    return PropScriptResult.CreateFailure("用户信息获取失败");
                }

                int currentPetCount = GetUserPetCount(userId);
                if (currentPetCount >= user.pasture_capacity)
                {
                    return PropScriptResult.CreateFailure($"获取宠物失败!最高只能同时拥有{user.pasture_capacity}只宠物噢!清理下牧场再来吧~");
                }

                // 获取宠物配置信息
                var petConfig = GetPetConfig(petId);
                if (petConfig == null)
                {
                    return PropScriptResult.CreateFailure("宠物配置不存在");
                }

                // 创建新宠物
                var newPet = new user_pet
                {
                    user_id = userId,
                    pet_no = Convert.ToInt32(petId),
                    custom_name = petConfig.name,
                    element = petConfig.attribute ?? "无",
                    growth = growth,
                    level = 1,
                    exp = 0,
                    hp = 100, // 默认生命值
                    mp = 50,  // 默认魔法值
                    atk = 10, // 攻击力字段名是atk
                    def = 10, // 防御力字段名是def
                    spd = 10, // 速度字段名是spd
                    is_main = false,
                    status = "牧场",
                    create_time = DateTime.Now
                };

                // 如果是第一只宠物，设为主宠物
                if (currentPetCount == 0)
                {
                    newPet.is_main = true;
                }

                bool success = AddPlayerPet(newPet);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"啊!恭喜您!获得宠物:{petConfig.name},已将它放到您的宠物牧场啦~");
                }

                return PropScriptResult.CreateFailure("添加宠物失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得宠物失败");
                return PropScriptResult.CreateFailure("处理获得宠物失败");
            }
        }

        /// <summary>
        /// 处理VIP升级（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleVipUpgrade(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：VIP升级
                if (user.vip_level >= 10)
                {
                    return PropScriptResult.CreateFailure("您已经是10级VIP了，无需升级！");
                }

                user.vip_level += 1;
                bool success = UpdateUser(user);

                if (success)
                {
                    return PropScriptResult.CreateSuccess("VIP升级成功！");
                }

                return PropScriptResult.CreateFailure("VIP升级失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理VIP升级失败");
                return PropScriptResult.CreateFailure("处理VIP升级失败");
            }
        }

        /// <summary>
        /// 处理至尊VIP（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSupremeVip(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：至尊VIP
                if (user.supreme_vip == true)
                {
                    return PropScriptResult.CreateFailure("您已经是至尊VIP了，无需开通！");
                }

                if (user.vip_level < 10)
                {
                    return PropScriptResult.CreateFailure("VIP等级未到达10级,无法激活至尊VIP！");
                }

                user.supreme_vip = true;
                bool success = UpdateUser(user);

                if (success)
                {
                    return PropScriptResult.CreateSuccess("至尊VIP开通成功！");
                }

                return PropScriptResult.CreateFailure("至尊VIP开通失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理至尊VIP失败");
                return PropScriptResult.CreateFailure("处理至尊VIP失败");
            }
        }

        /// <summary>
        /// 处理星辰VIP（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleStarVip(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：星辰VIP
                if (user.vip_level < 10)
                {
                    return PropScriptResult.CreateFailure("VIP等级未到达10级,无法激活星辰VIP！");
                }

                if (user.supreme_vip != true)
                {
                    return PropScriptResult.CreateFailure("未激活至尊VIP,无法激活星辰VIP！");
                }

                // 检查是否已经是星辰VIP
                if (user.star_vip.HasValue && user.star_vip.Value == true &&
                    user.star_vip_expire.HasValue && user.star_vip_expire > DateTime.Now)
                {
                    return PropScriptResult.CreateFailure("您已经是星辰VIP了！");
                }

                // 开通星辰VIP
                user.star_vip = true;
                user.star_vip_expire = DateTime.Now.AddDays(30); // 30天有效期

                bool success = UpdateUser(user);
                if (success)
                {
                    return PropScriptResult.CreateSuccess("星辰VIP开通成功！");
                }

                return PropScriptResult.CreateFailure("星辰VIP开通失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理星辰VIP失败");
                return PropScriptResult.CreateFailure("处理星辰VIP失败");
            }
        }

        /// <summary>
        /// 处理星辰VIP直升（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleStarVipDirect(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：星辰VIP直升
                user.vip_level = 10;
                user.supreme_vip = true;
                user.star_vip = true; // 设置星辰VIP状态
                user.star_vip_expire = DateTime.Now.AddDays(30); // 设置30天有效期

                bool success = UpdateUser(user);

                if (success)
                {
                    return PropScriptResult.CreateSuccess("星辰VIP直升成功！VIP等级已提升至10级，至尊VIP和星辰VIP已开通！");
                }

                return PropScriptResult.CreateFailure("星辰VIP直升失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理星辰VIP直升失败");
                return PropScriptResult.CreateFailure("处理星辰VIP直升失败");
            }
        }

        /// <summary>
        /// 处理重置地狱（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleResetHell(string[] directive, user_info user)
        {
            try
            {
                // 注意：user模型中没有地狱层数字段，需要添加hell_floor字段
                _logger.LogWarning("地狱层数字段未在user模型中定义，需要添加hell_floor字段");

                // 基于老项目逻辑：重置地狱
                // 暂时返回成功，实际需要添加hell_floor字段
                return PropScriptResult.CreateSuccess("地狱之门层数已重置！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理重置地狱失败");
                return PropScriptResult.CreateFailure("处理重置地狱失败");
            }
        }

        /// <summary>
        /// 处理重置通天（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleResetTower(string[] directive, user_info user)
        {
            try
            {
                // 注意：user模型中没有通天塔层数字段，需要添加tower_floor字段
                _logger.LogWarning("通天塔层数字段未在user模型中定义，需要添加tower_floor字段");

                // 基于老项目逻辑：重置通天
                // 暂时返回成功，实际需要添加tower_floor字段
                return PropScriptResult.CreateSuccess("通天塔层数已重置！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理重置通天失败");
                return PropScriptResult.CreateFailure("处理重置通天失败");
            }
        }

        /// <summary>
        /// 处理主宠改名（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleMainPetRename(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：主宠改名|新名称
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("新名称参数缺失");
                }

                string newName = directive[1];

                if (string.IsNullOrWhiteSpace(newName))
                {
                    return PropScriptResult.CreateFailure("宠物名称不能为空");
                }

                // 获取主宠物
                var mainPet = GetMainPet(user.id);
                if (mainPet == null)
                {
                    return PropScriptResult.CreateFailure("未找到主宠物");
                }

                // 更新宠物名称
                mainPet.custom_name = newName;
                bool success = UpdatePet(mainPet);

                if (success)
                {
                    return PropScriptResult.CreateSuccess("宠物改名成功！");
                }

                return PropScriptResult.CreateFailure("宠物改名失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理主宠改名失败");
                return PropScriptResult.CreateFailure("处理主宠改名失败");
            }
        }

        /// <summary>
        /// 处理开启任务助手（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleEnableTaskHelper(string[] directive, user_info user)
        {
            try
            {
                // 注意：user模型中没有任务助手字段，需要添加task_helper_enabled字段
                _logger.LogWarning("任务助手字段未在user模型中定义，需要添加task_helper_enabled字段");

                // 基于老项目逻辑：开启任务助手
                // 暂时返回成功，实际需要添加task_helper_enabled字段
                return PropScriptResult.CreateSuccess("开启任务助手成功！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理开启任务助手失败");
                return PropScriptResult.CreateFailure("处理开启任务助手失败");
            }
        }

        /// <summary>
        /// 获取用户宠物数量
        /// </summary>
        private int GetUserPetCount(int userId)
        {
            try
            {
                return _db.Queryable<user_pet>()
                    .Where(p => p.user_id == userId)
                    .Count();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户宠物数量失败");
                return 0;
            }
        }

        /// <summary>
        /// 获取宠物配置信息
        /// </summary>
        private pet_config GetPetConfig(string petId)
        {
            try
            {
                return _db.Queryable<pet_config>()
                    .Where(p => p.pet_no == Convert.ToInt32(petId))
                    .First();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 添加玩家宠物
        /// </summary>
        private bool AddPlayerPet(user_pet pet)
        {
            try
            {
                return _db.Insertable(pet).ExecuteCommand() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加玩家宠物失败");
                return false;
            }
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        private bool UpdateUser(user_info user)
        {
            try
            {
                return _db.Updateable(user).ExecuteCommand() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户信息失败");
                return false;
            }
        }

        /// <summary>
        /// 处理占卜屋（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleDivinationHouse(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：占卜屋|卡牌ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("卡牌ID参数缺失");
                }

                string cardId = directive[1];

                // 注意：需要实现卡牌系统相关的数据库表和逻辑
                _logger.LogWarning("占卜屋功能需要实现卡牌系统相关的数据库表");

                // 暂时返回成功，实际需要实现完整的卡牌系统
                return PropScriptResult.CreateSuccess($"成功获得了卡牌: {cardId} (功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理占卜屋失败");
                return PropScriptResult.CreateFailure("处理占卜屋失败");
            }
        }

        /// <summary>
        /// 处理召唤侍灵（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSummonSpirit(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：召唤侍灵|侍灵ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("侍灵ID参数缺失");
                }

                string spiritId = directive[1];

                // 注意：需要实现侍灵系统相关的数据库表和逻辑
                _logger.LogWarning("召唤侍灵功能需要实现侍灵系统相关的数据库表");

                // 暂时返回成功，实际需要实现完整的侍灵系统
                return PropScriptResult.CreateSuccess($"成功召唤了侍灵: {spiritId} (功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理召唤侍灵失败");
                return PropScriptResult.CreateFailure("处理召唤侍灵失败");
            }
        }

        /// <summary>
        /// 处理神兵（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleDivineWeapon(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：神兵|神兵ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("神兵ID参数缺失");
                }

                string weaponId = directive[1];

                // 注意：需要实现神兵系统相关的数据库表和逻辑
                _logger.LogWarning("神兵功能需要实现神兵系统相关的数据库表");

                // 暂时返回成功，实际需要实现完整的神兵系统
                return PropScriptResult.CreateSuccess($"已经激活：{weaponId}！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理神兵失败");
                return PropScriptResult.CreateFailure("处理神兵失败");
            }
        }

        /// <summary>
        /// 处理扩容页面（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleExpandPage(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：扩容页面|页面类型|扩容数量
                if (directive.Length < 3)
                {
                    return PropScriptResult.CreateFailure("页面类型或扩容数量参数缺失");
                }

                string pageType = directive[1];
                if (!int.TryParse(directive[2], out int expandCount))
                {
                    return PropScriptResult.CreateFailure("扩容数量必须是数字");
                }

                // 根据页面类型进行扩容
                switch (pageType)
                {
                    case "背包":
                        user.prop_capacity = (user.prop_capacity ?? 100) + expandCount;
                        break;
                    case "牧场":
                        user.pasture_capacity = (user.pasture_capacity ?? 80) + expandCount;
                        break;
                    default:
                        // 注意：其他页面类型需要实现相应的数据库字段
                        _logger.LogWarning($"页面类型 {pageType} 需要实现相应的数据库字段");
                        return PropScriptResult.CreateSuccess($"已经扩容{pageType}页到：{expandCount}！(功能待完善)");
                }

                bool success = UpdateUser(user);
                if (success)
                {
                    return PropScriptResult.CreateSuccess($"已经扩容{pageType}页成功！");
                }

                return PropScriptResult.CreateFailure("扩容失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理扩容页面失败");
                return PropScriptResult.CreateFailure("处理扩容页面失败");
            }
        }

        /// <summary>
        /// 处理皮肤（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSkin(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：皮肤|皮肤ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("皮肤ID参数缺失");
                }

                string skinId = directive[1];

                // 注意：需要实现皮肤系统相关的数据库表和逻辑
                _logger.LogWarning("皮肤功能需要实现皮肤系统相关的数据库表");

                // 暂时返回成功，实际需要实现完整的皮肤系统
                return PropScriptResult.CreateSuccess($"已经激活皮肤：{skinId}！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理皮肤失败");
                return PropScriptResult.CreateFailure("处理皮肤失败");
            }
        }

        /// <summary>
        /// 处理魂器（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSoulWeapon(string[] directive, user_info user)
        {
            try
            {
                // 基于老项目逻辑：魂器|魂器ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("魂器ID参数缺失");
                }

                string soulWeaponId = directive[1];

                // 注意：需要实现魂器系统相关的数据库表和逻辑
                _logger.LogWarning("魂器功能需要实现魂器系统相关的数据库表");

                // 暂时返回成功，实际需要实现完整的魂器系统
                return PropScriptResult.CreateSuccess($"已经激活：{soulWeaponId}！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理魂器失败");
                return PropScriptResult.CreateFailure("处理魂器失败");
            }
        }

        /// <summary>
        /// 处理提示信息（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleShowMessage(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：提示信息|消息内容
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("消息内容参数缺失");
                }

                string message = directive[1];

                // 返回消息内容，但不消耗道具
                return PropScriptResult.CreateFailure(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理提示信息失败");
                return PropScriptResult.CreateFailure("处理提示信息失败");
            }
        }

        /// <summary>
        /// 处理发送神谕（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSendOracle(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：发送神谕|神谕内容
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("神谕内容参数缺失");
                }

                string oracleMessage = directive[1];

                // 注意：需要实现全服消息系统
                _logger.LogWarning($"发送神谕功能需要实现全服消息系统: {oracleMessage}");

                // 暂时返回成功，实际需要实现全服消息推送
                return PropScriptResult.CreateFailure("收到一条神谕");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理发送神谕失败");
                return PropScriptResult.CreateFailure("处理发送神谕失败");
            }
        }

        /// <summary>
        /// 处理装备魂宠（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleEquipSoulPet(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：装备魂宠|魂宠ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("魂宠ID参数缺失");
                }

                string soulPetId = directive[1];

                // 注意：需要实现魂宠系统相关的数据库字段
                _logger.LogWarning("装备魂宠功能需要实现魂宠系统相关的数据库字段");

                // 暂时返回成功，实际需要实现魂宠装备逻辑
                return PropScriptResult.CreateSuccess($"召唤{soulPetId}成功~(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理装备魂宠失败");
                return PropScriptResult.CreateFailure("处理装备魂宠失败");
            }
        }

        /// <summary>
        /// 处理设置主宠（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSetMainPet(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：设置主宠|宠物ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("宠物ID参数缺失");
                }

                string petId = directive[1];

                // 获取指定宠物
                var targetPet = GetPlayerPetById(user.id, petId);
                if (targetPet == null)
                {
                    return PropScriptResult.CreateFailure("未找到指定宠物");
                }

                // 取消当前主宠物
                var currentMainPet = GetMainPet(user.id);
                if (currentMainPet != null)
                {
                    currentMainPet.is_main = false;
                    UpdatePet(currentMainPet);
                }

                // 设置新的主宠物
                targetPet.is_main = true;
                bool success = UpdatePet(targetPet);

                if (success)
                {
                    return PropScriptResult.CreateSuccess("设置主宠物成功！");
                }

                return PropScriptResult.CreateFailure("设置主宠物失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理设置主宠失败");
                return PropScriptResult.CreateFailure("处理设置主宠失败");
            }
        }

        /// <summary>
        /// 处理提升境界（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleUpgradeRealm(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：提升境界
                var mainPet = GetMainPet(user.id);
                if (mainPet == null)
                {
                    return PropScriptResult.CreateFailure("未找到主宠物");
                }

                if (mainPet.level != 130)
                {
                    return PropScriptResult.CreateFailure("只有满级宠物才能提升境界！");
                }

                // 获取当前境界，如果为空则设置为初始境界
                string currentRealm = mainPet.realm ?? "元神初具";

                // 检查境界是否可以使用修炼丹提升
                int currentRealmLevel = GetRealmLevel(currentRealm);
                if (currentRealmLevel >= 15) // 天地同寿及以上
                {
                    return PropScriptResult.CreateFailure("当前境界无法使用修炼丹,请使用玄元丹继续提升！");
                }

                if (currentRealm == "天地同寿")
                {
                    return PropScriptResult.CreateFailure("当前境界已达到修炼丹的极限！");
                }

                // 基于老项目逻辑：约1.1%的成功率 (1/90)
                var random = new Random();
                int lucky = random.Next(1, 91);

                if (lucky == 45) // 成功
                {
                    // 提升境界
                    string nextRealm = GetNextRealm(currentRealm);
                    if (nextRealm != null)
                    {
                        mainPet.realm = nextRealm;
                        mainPet.level = 1;
                        mainPet.exp = 1;

                        bool success = UpdatePet(mainPet);
                        if (success)
                        {
                            _logger.LogInformation($"用户{user.id}的宠物{mainPet.id}境界提升成功：{currentRealm} -> {nextRealm}");
                            return PropScriptResult.CreateSuccess("宠物境界提升成功！");
                        }
                        else
                        {
                            return PropScriptResult.CreateFailure("境界提升失败，数据保存错误");
                        }
                    }
                    else
                    {
                        return PropScriptResult.CreateFailure("无法获取下一境界信息");
                    }
                }
                else // 失败
                {
                    // 基于老项目逻辑：失败扣除10000金币
                    if (user.gold < 10000)
                    {
                        return PropScriptResult.CreateFailure("境界提升失败，金币不足以承受失败惩罚！");
                    }

                    user.gold -= 10000;
                    bool success = UpdateUser(user);

                    if (success)
                    {
                        _logger.LogInformation($"用户{user.id}境界提升失败，扣除10000金币");
                        return PropScriptResult.CreateSuccess("宠物境界没有丝毫提升,并损失了10000金币！");
                    }
                    else
                    {
                        return PropScriptResult.CreateFailure("境界提升失败，数据保存错误");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理提升境界失败");
                return PropScriptResult.CreateFailure("处理提升境界失败");
            }
        }

        /// <summary>
        /// 处理突破境界（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleBreakRealm(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：突破境界
                var mainPet = GetMainPet(user.id);
                if (mainPet == null)
                {
                    return PropScriptResult.CreateFailure("未找到主宠物");
                }

                if (mainPet.level != 130)
                {
                    return PropScriptResult.CreateFailure("只有满级宠物才能突破境界！");
                }

                // 获取当前境界
                string currentRealm = mainPet.realm ?? "元神初具";

                // 检查是否已达到最高境界
                if (currentRealm == "神轮境")
                {
                    return PropScriptResult.CreateFailure("无法继续突破境界！");
                }

                // 检查境界等级是否达到突破要求
                int currentRealmLevel = GetRealmLevel(currentRealm);
                if (currentRealmLevel < 15) // 低于天地同寿
                {
                    return PropScriptResult.CreateFailure("当前境界无法使用玄元丹,请先使用修炼丹提升境界！");
                }

                // 基于老项目逻辑：突破境界直接成功（玄元丹100%成功）
                string nextRealm = GetNextRealm(currentRealm);
                if (nextRealm != null)
                {
                    mainPet.realm = nextRealm;
                    mainPet.level = 1;
                    mainPet.exp = 1;

                    bool success = UpdatePet(mainPet);
                    if (success)
                    {
                        _logger.LogInformation($"用户{user.id}的宠物{mainPet.id}突破境界成功：{currentRealm} -> {nextRealm}");
                        return PropScriptResult.CreateSuccess("宠物突破境界成功！");
                    }
                    else
                    {
                        return PropScriptResult.CreateFailure("突破境界失败，数据保存错误");
                    }
                }
                else
                {
                    return PropScriptResult.CreateFailure("无法获取下一境界信息");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理突破境界失败");
                return PropScriptResult.CreateFailure("处理突破境界失败");
            }
        }

        /// <summary>
        /// 处理宠物改名（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleRenamePet(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：宠物改名|宠物ID|新名称
                if (directive.Length < 3)
                {
                    return PropScriptResult.CreateFailure("宠物ID或新名称参数缺失");
                }

                string petId = directive[1];
                string newName = directive[2];

                if (string.IsNullOrWhiteSpace(newName))
                {
                    return PropScriptResult.CreateFailure("宠物名称不能为空");
                }

                // 获取指定宠物
                var pet = GetPlayerPetById(user.id, petId);
                if (pet == null)
                {
                    return PropScriptResult.CreateFailure("未找到指定宠物");
                }

                // 更新宠物名称
                pet.custom_name = newName;
                bool success = UpdatePet(pet);

                if (success)
                {
                    return PropScriptResult.CreateSuccess("宠物改名成功！");
                }

                return PropScriptResult.CreateFailure("宠物改名失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理宠物改名失败");
                return PropScriptResult.CreateFailure("处理宠物改名失败");
            }
        }

        /// <summary>
        /// 处理宠物放生（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleReleasePet(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：宠物放生|宠物ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("宠物ID参数缺失");
                }

                string petId = directive[1];

                // 获取指定宠物
                var pet = GetPlayerPetById(user.id, petId);
                if (pet == null)
                {
                    return PropScriptResult.CreateFailure("未找到指定宠物");
                }

                if (pet.is_main == true)
                {
                    return PropScriptResult.CreateFailure("不能放生主宠物");
                }

                // 删除宠物
                bool success = DeletePet(pet.id);

                if (success)
                {
                    return PropScriptResult.CreateSuccess("宠物放生成功！");
                }

                return PropScriptResult.CreateFailure("宠物放生失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理宠物放生失败");
                return PropScriptResult.CreateFailure("处理宠物放生失败");
            }
        }

        /// <summary>
        /// 处理技能遗忘（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleForgetSkill(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：技能遗忘|技能ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("技能ID参数缺失");
                }

                string skillId = directive[1];

                // 注意：需要实现技能系统相关的数据库表
                _logger.LogWarning("技能遗忘功能需要实现技能系统相关的数据库表");

                return PropScriptResult.CreateSuccess($"技能{skillId}遗忘成功！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理技能遗忘失败");
                return PropScriptResult.CreateFailure("处理技能遗忘失败");
            }
        }

        /// <summary>
        /// 处理默认技能（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleDefaultSkill(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：默认技能
                var mainPet = GetMainPet(user.id);
                if (mainPet == null)
                {
                    return PropScriptResult.CreateFailure("未找到主宠物");
                }

                // 基于老项目逻辑：学习宠物的默认技能
                bool success = StudyDefaultSkill(mainPet);

                if (success)
                {
                    _logger.LogInformation($"用户{user.id}的宠物{mainPet.id}学习默认技能成功");
                    return PropScriptResult.CreateSuccess("刷新宠物的默认技能成功！");
                }
                else
                {
                    return PropScriptResult.CreateSuccess("该宠物没有默认技能或已经习得所有默认技能！");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理默认技能失败");
                return PropScriptResult.CreateFailure("处理默认技能失败");
            }
        }

        /// <summary>
        /// 学习默认技能（基于老项目逻辑）
        /// </summary>
        private bool StudyDefaultSkill(user_pet pet)
        {
            try
            {
                // 基于老项目逻辑：根据宠物形象获取默认技能列表
                var defaultSkills = GetPetDefaultSkills(pet.pet_no);
                if (defaultSkills == null || defaultSkills.Count == 0)
                {
                    return false; // 没有默认技能
                }

                // 获取宠物当前技能列表
                var currentSkills = GetPetCurrentSkills(pet.id);

                bool hasNewSkill = false;
                foreach (string skillId in defaultSkills)
                {
                    if (string.IsNullOrEmpty(skillId)) continue;

                    // 检查是否已经学会该技能
                    if (!currentSkills.Contains(skillId))
                    {
                        // 学习新技能
                        AddPetSkill(pet.id, skillId);
                        hasNewSkill = true;
                        _logger.LogDebug($"宠物{pet.id}学习默认技能：{skillId}");
                    }
                }

                return hasNewSkill;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"学习默认技能失败，宠物ID：{pet.id}");
                return false;
            }
        }

        /// <summary>
        /// 获取宠物默认技能列表（基于老项目逻辑）
        /// </summary>
        private List<string> GetPetDefaultSkills(int petNo)
        {
            try
            {
                // 注意：这里需要从宠物配置表中获取默认技能
                // 基于老项目逻辑，每种宠物都有对应的默认技能列表
                // 暂时返回空列表，后续需要完善宠物配置系统
                _logger.LogWarning($"获取宠物{petNo}默认技能需要完善宠物配置系统");
                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取宠物{petNo}默认技能失败");
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取宠物当前技能列表
        /// </summary>
        private List<string> GetPetCurrentSkills(int petId)
        {
            try
            {
                // 注意：这里需要从宠物技能表中获取当前技能
                // 暂时返回空列表，后续需要完善技能系统
                _logger.LogWarning($"获取宠物{petId}当前技能需要完善技能系统");
                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取宠物{petId}当前技能失败");
                return new List<string>();
            }
        }

        /// <summary>
        /// 为宠物添加技能
        /// </summary>
        private bool AddPetSkill(int petId, string skillId)
        {
            try
            {
                // 注意：这里需要向宠物技能表中添加技能
                // 暂时返回true，后续需要完善技能系统
                _logger.LogWarning($"为宠物{petId}添加技能{skillId}需要完善技能系统");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"为宠物{petId}添加技能{skillId}失败");
                return false;
            }
        }

        /// <summary>
        /// 处理属性重置（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleResetAttributes(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：属性重置|重置类型
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("重置类型参数缺失");
                }

                string resetType = directive[1];

                // 注意：需要实现属性重置逻辑
                _logger.LogWarning("属性重置功能需要实现属性重置相关逻辑");

                return PropScriptResult.CreateSuccess($"{resetType}属性重置成功！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理属性重置失败");
                return PropScriptResult.CreateFailure("处理属性重置失败");
            }
        }

        /// <summary>
        /// 处理装备强化（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleEnhanceEquipment(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：装备强化|装备ID|强化等级
                if (directive.Length < 3)
                {
                    return PropScriptResult.CreateFailure("装备ID或强化等级参数缺失");
                }

                string equipId = directive[1];
                string enhanceLevel = directive[2];

                // 注意：需要实现装备强化系统
                _logger.LogWarning("装备强化功能需要实现装备强化系统");

                return PropScriptResult.CreateSuccess($"装备{equipId}强化到{enhanceLevel}级成功！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理装备强化失败");
                return PropScriptResult.CreateFailure("处理装备强化失败");
            }
        }

        /// <summary>
        /// 处理装备分解（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleDecomposeEquipment(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：装备分解|装备ID
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("装备ID参数缺失");
                }

                string equipId = directive[1];

                // 注意：需要实现装备分解系统
                _logger.LogWarning("装备分解功能需要实现装备分解系统");

                return PropScriptResult.CreateSuccess($"装备{equipId}分解成功！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理装备分解失败");
                return PropScriptResult.CreateFailure("处理装备分解失败");
            }
        }

        /// <summary>
        /// 处理宝石合成（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleSynthesizeGem(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：宝石合成|宝石类型|数量
                if (directive.Length < 3)
                {
                    return PropScriptResult.CreateFailure("宝石类型或数量参数缺失");
                }

                string gemType = directive[1];
                string quantity = directive[2];

                // 注意：需要实现宝石合成系统
                _logger.LogWarning("宝石合成功能需要实现宝石合成系统");

                return PropScriptResult.CreateSuccess($"合成{gemType}宝石{quantity}个成功！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理宝石合成失败");
                return PropScriptResult.CreateFailure("处理宝石合成失败");
            }
        }



        /// <summary>
        /// 处理获得法宝（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainTreasure(string[] directive, user user)
        {
            try
            {
                // 暂时返回成功，后续完善
                return PropScriptResult.CreateSuccess("获得法宝功能待完善");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得法宝失败");
                return PropScriptResult.CreateFailure("处理获得法宝失败");
            }
        }

        /// <summary>
        /// 处理获得灵饰（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleGainAccessory(string[] directive, user user)
        {
            try
            {
                // 暂时返回成功，后续完善
                return PropScriptResult.CreateSuccess("获得灵饰功能待完善");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理获得灵饰失败");
                return PropScriptResult.CreateFailure("处理获得灵饰失败");
            }
        }

        /// <summary>
        /// 处理经验转移（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleTransferExp(string[] directive, user user)
        {
            try
            {
                // 暂时返回成功，后续完善
                return PropScriptResult.CreateSuccess("经验转移功能待完善");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理经验转移失败");
                return PropScriptResult.CreateFailure("处理经验转移失败");
            }
        }

        /// <summary>
        /// 根据ID获取玩家宠物
        /// </summary>
        private user_pet GetPlayerPetById(int userId, string petId)
        {
            try
            {
                return _db.Queryable<user_pet>()
                    .Where(p => p.user_id == userId && p.id.ToString() == petId)
                    .First();
            }
            catch
            {
                return null;
            }
        }



        /// <summary>
        /// 删除宠物
        /// </summary>
        private bool DeletePet(int petId)
        {
            try
            {
                return _db.Deleteable<user_pet>().Where(p => p.id == petId).ExecuteCommand() > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除宠物失败");
                return false;
            }
        }

        /// <summary>
        /// 处理佩戴称号（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleEquipTitle(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：佩戴称号|称号名称|类型|属性
                if (directive.Length < 2)
                {
                    return PropScriptResult.CreateFailure("称号名称参数缺失");
                }

                string titleName = directive[1];

                // 设置用户称号
                user.title = titleName;

                // 处理属性类称号
                if (directive.Length >= 4 && directive[2].Equals("U"))
                {
                    // 属性格式：攻击:100 或 防御:50 等
                    string attributeStr = directive[3];

                    // 注意：user模型中没有title_attributes字段
                    // 需要添加title_attributes字段到数据库和模型
                    _logger.LogWarning($"称号属性加成功能需要完善，需要添加title_attributes字段: {attributeStr}");

                    // 暂时只记录日志，实际需要添加数据库字段
                }

                bool success = UpdateUser(user);

                if (success)
                {
                    return PropScriptResult.CreateSuccess($"您已成功佩戴称号 {titleName}");
                }

                return PropScriptResult.CreateFailure("佩戴称号失败");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理佩戴称号失败");
                return PropScriptResult.CreateFailure("处理佩戴称号失败");
            }
        }

        /// <summary>
        /// 处理卸下魂宠（基于老项目逻辑）
        /// </summary>
        private PropScriptResult HandleUnequipSoulPet(string[] directive, user user)
        {
            try
            {
                // 基于老项目逻辑：卸下魂宠
                // 注意：user模型中没有soul_pet字段
                // 需要添加soul_pet字段到数据库和模型
                _logger.LogWarning("卸下魂宠功能需要完善，需要添加soul_pet字段到user模型");

                // 暂时返回成功，实际需要添加数据库字段
                return PropScriptResult.CreateSuccess("卸载魂宠成功！(功能待完善)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理卸下魂宠失败");
                return PropScriptResult.CreateFailure("处理卸下魂宠失败");
            }
        }

        /// <summary>
        /// 获取幸运值（基于老项目逻辑）
        /// </summary>
        private double GetLuckValue(int userId)
        {
            try
            {
                // 基于老项目逻辑的完整幸运值计算
                double totalLuck = 1.0; // 基础幸运值

                // 1. 获取用户信息
                var user = _db.Queryable<user>().Where(u => u.id == userId).First();
                if (user == null) return 1.0;

                // 2. VIP等级加成（基于老项目逻辑）
                totalLuck += GetVipLuckBonus(user);

                // 3. 装备幸运值加成
                totalLuck += GetEquipmentLuckBonus(userId);

                // 4. 称号幸运值加成
                totalLuck += GetTitleLuckBonus(user);

                // 5. 道具幸运值加成（临时BUFF）
                totalLuck += GetItemLuckBonus(userId);

                // 6. 幸运值上限检查（基于老项目逻辑）
                if (totalLuck > 100 && !HasSpecialPrivilege(user))
                {
                    totalLuck = 100; // 普通用户幸运值上限100
                }

                _logger.LogDebug($"用户{userId}幸运值计算: 基础1.0 + VIP加成 + 装备加成 + 称号加成 + 道具加成 = {totalLuck}");

                return totalLuck;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"计算用户{userId}幸运值失败");
                return 1.0; // 异常时返回基础幸运值
            }
        }

        /// <summary>
        /// 获取VIP幸运值加成
        /// </summary>
        private double GetVipLuckBonus(user user)
        {
            try
            {
                double vipBonus = 0.0;

                // VIP等级加成
                if (user.vip_level.HasValue && user.vip_level > 0)
                {
                    vipBonus += user.vip_level.Value * 0.5; // 每级VIP增加0.5幸运值
                }

                // 至尊VIP加成
                if (user.supreme_vip.HasValue && user.supreme_vip.Value == true)
                {
                    vipBonus += 5.0; // 至尊VIP额外增加5点幸运值
                }

                // 星辰VIP加成
                if (user.star_vip.HasValue && user.star_vip.Value == true && user.star_vip_expire.HasValue && user.star_vip_expire > DateTime.Now)
                {
                    vipBonus += 10.0; // 星辰VIP额外增加10点幸运值
                }

                return vipBonus;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算VIP幸运值加成失败");
                return 0.0;
            }
        }

        /// <summary>
        /// 获取装备幸运值加成
        /// </summary>
        private double GetEquipmentLuckBonus(int userId)
        {
            try
            {
                // 注意：这里需要实现装备系统的幸运值加成计算
                // 基于老项目逻辑，装备可以提供幸运值加成
                // 暂时返回0，后续需要完善装备系统
                _logger.LogDebug($"装备幸运值加成计算需要完善装备系统");
                return 0.0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算装备幸运值加成失败");
                return 0.0;
            }
        }

        /// <summary>
        /// 获取称号幸运值加成
        /// </summary>
        private double GetTitleLuckBonus(user user)
        {
            try
            {
                if (string.IsNullOrEmpty(user.title))
                    return 0.0;

                // 基于老项目逻辑，某些称号提供幸运值加成
                if (!string.IsNullOrEmpty(user.title_attributes))
                {
                    // 解析称号属性，格式如：幸运值:5
                    var attributes = user.title_attributes.Split(',');
                    foreach (var attr in attributes)
                    {
                        var parts = attr.Split(':');
                        if (parts.Length == 2 && parts[0].Trim() == "幸运值")
                        {
                            if (double.TryParse(parts[1].Trim(), out double luckValue))
                            {
                                return luckValue;
                            }
                        }
                    }
                }

                return 0.0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算称号幸运值加成失败");
                return 0.0;
            }
        }

        /// <summary>
        /// 获取道具幸运值加成（临时BUFF）
        /// </summary>
        private double GetItemLuckBonus(int userId)
        {
            try
            {
                // 注意：这里需要实现道具BUFF系统的幸运值加成
                // 基于老项目逻辑，某些道具可以临时提供幸运值加成
                // 暂时返回0，后续需要完善BUFF系统
                _logger.LogDebug($"道具幸运值加成计算需要完善BUFF系统");
                return 0.0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算道具幸运值加成失败");
                return 0.0;
            }
        }

        /// <summary>
        /// 检查是否有特殊权限（基于老项目逻辑）
        /// </summary>
        private bool HasSpecialPrivilege(user user)
        {
            try
            {
                // 基于老项目逻辑，某些特殊用户可以突破幸运值上限
                // 这里可以根据需要实现特殊权限检查
                return false; // 暂时都没有特殊权限
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取境界等级（基于老项目逻辑）
        /// </summary>
        private int GetRealmLevel(string realmName)
        {
            try
            {
                foreach (var kvp in PetStates)
                {
                    if (kvp.Value == realmName)
                    {
                        return kvp.Key;
                    }
                }
                return 1; // 默认返回1级
            }
            catch
            {
                return 1;
            }
        }

        /// <summary>
        /// 获取下一境界（基于老项目逻辑）
        /// </summary>
        private string GetNextRealm(string currentRealm)
        {
            try
            {
                int currentLevel = GetRealmLevel(currentRealm);
                if (PetStates.ContainsKey(currentLevel + 1))
                {
                    return PetStates[currentLevel + 1];
                }
                return null; // 已达到最高境界
            }
            catch
            {
                return null;
            }
        }

    }
}
