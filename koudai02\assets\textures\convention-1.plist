<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{4,-41}</string>
                <key>spriteSize</key>
                <string>{139,255}</string>
                <key>spriteSourceSize</key>
                <string>{197,337}</string>
                <key>textureRect</key>
                <string>{{1881,1507},{139,255}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{23,25}</string>
                <key>spriteSourceSize</key>
                <string>{25,25}</string>
                <key>textureRect</key>
                <string>{{1398,1764},{23,25}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>20.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-3,-153}</string>
                <key>spriteSize</key>
                <string>{177,31}</string>
                <key>spriteSourceSize</key>
                <string>{197,337}</string>
                <key>textureRect</key>
                <string>{{1398,1585},{177,31}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{19,19}</string>
                <key>spriteSourceSize</key>
                <string>{19,19}</string>
                <key>textureRect</key>
                <string>{{1005,1722},{19,19}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{141,62}</string>
                <key>spriteSourceSize</key>
                <string>{141,62}</string>
                <key>textureRect</key>
                <string>{{1881,1764},{141,62}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{141,61}</string>
                <key>spriteSourceSize</key>
                <string>{141,61}</string>
                <key>textureRect</key>
                <string>{{1881,1828},{141,61}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>BackpackClosed.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{12,12}</string>
                <key>spriteSourceSize</key>
                <string>{12,12}</string>
                <key>textureRect</key>
                <string>{{2012,1413},{12,12}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>BtnNoviceBase.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{83,24}</string>
                <key>spriteSourceSize</key>
                <string>{83,24}</string>
                <key>textureRect</key>
                <string>{{1911,1175},{83,24}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>EXP_bk.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{1,11}</string>
                <key>spriteSourceSize</key>
                <string>{1,11}</string>
                <key>textureRect</key>
                <string>{{2013,1371},{1,11}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>HP_bk.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{2,16}</string>
                <key>spriteSourceSize</key>
                <string>{2,16}</string>
                <key>textureRect</key>
                <string>{{781,1841},{2,16}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>MP_bk.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{1,16}</string>
                <key>spriteSourceSize</key>
                <string>{1,16}</string>
                <key>textureRect</key>
                <string>{{435,2015},{1,16}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>PopFrame3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{1024,695}</string>
                <key>spriteSourceSize</key>
                <string>{1024,695}</string>
                <key>textureRect</key>
                <string>{{1,903},{1024,695}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ProgressBarBk4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{432,50}</string>
                <key>spriteSourceSize</key>
                <string>{432,50}</string>
                <key>textureRect</key>
                <string>{{1,1975},{432,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>attack.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,-4}</string>
                <key>spriteSize</key>
                <string>{34,46}</string>
                <key>spriteSourceSize</key>
                <string>{38,80}</string>
                <key>textureRect</key>
                <string>{{1431,1635},{34,46}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>attackSettings.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,-3}</string>
                <key>spriteSize</key>
                <string>{45,62}</string>
                <key>spriteSourceSize</key>
                <string>{47,80}</string>
                <key>textureRect</key>
                <string>{{897,1953},{45,62}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>autoAttack.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{2,-3}</string>
                <key>spriteSize</key>
                <string>{61,60}</string>
                <key>spriteSourceSize</key>
                <string>{69,80}</string>
                <key>textureRect</key>
                <string>{{897,1891},{61,60}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>auxiliary.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,-2}</string>
                <key>spriteSize</key>
                <string>{40,50}</string>
                <key>spriteSourceSize</key>
                <string>{46,80}</string>
                <key>textureRect</key>
                <string>{{1437,1891},{40,50}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>background.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{348,27}</string>
                <key>spriteSourceSize</key>
                <string>{348,27}</string>
                <key>textureRect</key>
                <string>{{1911,825},{348,27}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>base.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,1}</string>
                <key>spriteSize</key>
                <string>{778,18}</string>
                <key>spriteSourceSize</key>
                <string>{778,62}</string>
                <key>textureRect</key>
                <string>{{1,1841},{778,18}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>beibao_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{47,45}</string>
                <key>spriteSourceSize</key>
                <string>{47,45}</string>
                <key>textureRect</key>
                <string>{{1489,1879},{47,45}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>beijin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{290,239}</string>
                <key>spriteSourceSize</key>
                <string>{290,239}</string>
                <key>textureRect</key>
                <string>{{713,1600},{290,239}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>blue.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{1,1861},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>blue_r.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{400,112}</string>
                <key>spriteSourceSize</key>
                <string>{400,112}</string>
                <key>textureRect</key>
                <string>{{403,1861},{400,112}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>btn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{46,19}</string>
                <key>spriteSourceSize</key>
                <string>{46,19}</string>
                <key>textureRect</key>
                <string>{{1911,1260},{46,19}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>bz_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,-2}</string>
                <key>spriteSize</key>
                <string>{40,22}</string>
                <key>spriteSourceSize</key>
                <string>{46,38}</string>
                <key>textureRect</key>
                <string>{{1775,1879},{40,22}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>catch.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{46,54}</string>
                <key>spriteSourceSize</key>
                <string>{46,80}</string>
                <key>textureRect</key>
                <string>{{1911,1459},{46,54}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>chat_bg.jpg</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{710,239}</string>
                <key>spriteSourceSize</key>
                <string>{710,239}</string>
                <key>textureRect</key>
                <string>{{1,1600},{710,239}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>cheku_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-3,0}</string>
                <key>spriteSize</key>
                <string>{46,42}</string>
                <key>spriteSourceSize</key>
                <string>{52,86}</string>
                <key>textureRect</key>
                <string>{{961,1952},{46,42}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>colour.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{44,19}</string>
                <key>spriteSourceSize</key>
                <string>{44,19}</string>
                <key>textureRect</key>
                <string>{{1991,1413},{44,19}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>cz_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-2}</string>
                <key>spriteSize</key>
                <string>{39,22}</string>
                <key>spriteSourceSize</key>
                <string>{47,38}</string>
                <key>textureRect</key>
                <string>{{1817,1879},{39,22}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>daoju_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-4,-1}</string>
                <key>spriteSize</key>
                <string>{78,44}</string>
                <key>spriteSourceSize</key>
                <string>{86,86}</string>
                <key>textureRect</key>
                <string>{{1911,1413},{78,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>dr02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{31,37}</string>
                <key>spriteSourceSize</key>
                <string>{31,37}</string>
                <key>textureRect</key>
                <string>{{1431,1683},{31,37}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>escape.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-3,-1}</string>
                <key>spriteSize</key>
                <string>{45,58}</string>
                <key>spriteSourceSize</key>
                <string>{55,80}</string>
                <key>textureRect</key>
                <string>{{1422,1933},{45,58}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>et_09.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-11,4}</string>
                <key>spriteSize</key>
                <string>{44,40}</string>
                <key>spriteSourceSize</key>
                <string>{78,86}</string>
                <key>textureRect</key>
                <string>{{1729,1879},{44,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>friends.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{16,15}</string>
                <key>spriteSourceSize</key>
                <string>{16,15}</string>
                <key>textureRect</key>
                <string>{{880,1971},{16,15}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>gonggao_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-4,-3}</string>
                <key>spriteSize</key>
                <string>{59,42}</string>
                <key>spriteSourceSize</key>
                <string>{67,86}</string>
                <key>textureRect</key>
                <string>{{960,1891},{59,42}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>gw_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-2}</string>
                <key>spriteSize</key>
                <string>{39,22}</string>
                <key>spriteSourceSize</key>
                <string>{47,38}</string>
                <key>textureRect</key>
                <string>{{1911,1308},{39,22}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>huangg_07.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,-1}</string>
                <key>spriteSize</key>
                <string>{46,42}</string>
                <key>spriteSourceSize</key>
                <string>{60,86}</string>
                <key>textureRect</key>
                <string>{{1634,1879},{46,42}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>ico_btn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{59,17}</string>
                <key>spriteSourceSize</key>
                <string>{59,17}</string>
                <key>textureRect</key>
                <string>{{1449,825},{59,17}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>ico_msg.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{18,15}</string>
                <key>spriteSourceSize</key>
                <string>{18,15}</string>
                <key>textureRect</key>
                <string>{{1449,886},{18,15}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>inp.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{1368,76}</string>
                <key>spriteSourceSize</key>
                <string>{1368,76}</string>
                <key>textureRect</key>
                <string>{{1948,1},{1368,76}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>jiao_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-3}</string>
                <key>spriteSize</key>
                <string>{62,44}</string>
                <key>spriteSourceSize</key>
                <string>{62,86}</string>
                <key>textureRect</key>
                <string>{{1376,1933},{62,44}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>kf_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-2}</string>
                <key>spriteSize</key>
                <string>{39,22}</string>
                <key>spriteSourceSize</key>
                <string>{49,38}</string>
                <key>textureRect</key>
                <string>{{1469,1933},{39,22}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>lamp.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{81,79}</string>
                <key>spriteSourceSize</key>
                <string>{81,79}</string>
                <key>textureRect</key>
                <string>{{805,1841},{81,79}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>login.jpg</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{822,543}</string>
                <key>spriteSourceSize</key>
                <string>{822,543}</string>
                <key>textureRect</key>
                <string>{{1403,1},{822,543}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>login.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{100,40}</string>
                <key>spriteSourceSize</key>
                <string>{100,40}</string>
                <key>textureRect</key>
                <string>{{644,1975},{100,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>lt_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,-2}</string>
                <key>spriteSize</key>
                <string>{38,22}</string>
                <key>spriteSourceSize</key>
                <string>{48,38}</string>
                <key>textureRect</key>
                <string>{{1775,1903},{38,22}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>map_sksd.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{67,40}</string>
                <key>spriteSourceSize</key>
                <string>{67,40}</string>
                <key>textureRect</key>
                <string>{{746,1975},{67,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>muc_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{5,-1}</string>
                <key>spriteSize</key>
                <string>{45,42}</string>
                <key>spriteSourceSize</key>
                <string>{59,86}</string>
                <key>textureRect</key>
                <string>{{1682,1879},{45,42}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pack.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{370,410}</string>
                <key>spriteSourceSize</key>
                <string>{370,410}</string>
                <key>textureRect</key>
                <string>{{1469,1507},{370,410}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>page_r.jpg</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{440,680}</string>
                <key>spriteSourceSize</key>
                <string>{440,680}</string>
                <key>textureRect</key>
                <string>{{1027,903},{440,680}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>page_r.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{440,680}</string>
                <key>spriteSourceSize</key>
                <string>{440,680}</string>
                <key>textureRect</key>
                <string>{{1469,825},{440,680}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pm_wp07.jpg</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{1400,900}</string>
                <key>spriteSourceSize</key>
                <string>{1400,900}</string>
                <key>textureRect</key>
                <string>{{1,1},{1400,900}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>prop.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,-1}</string>
                <key>spriteSize</key>
                <string>{38,52}</string>
                <key>spriteSourceSize</key>
                <string>{40,80}</string>
                <key>textureRect</key>
                <string>{{1397,1879},{38,52}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>reg.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{100,40}</string>
                <key>spriteSourceSize</key>
                <string>{100,40}</string>
                <key>textureRect</key>
                <string>{{1911,1371},{100,40}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>renwu_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{45,45}</string>
                <key>spriteSourceSize</key>
                <string>{45,45}</string>
                <key>textureRect</key>
                <string>{{1587,1879},{45,45}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>rfrpg2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{30,32}</string>
                <key>spriteSourceSize</key>
                <string>{32,32}</string>
                <key>textureRect</key>
                <string>{{1431,1722},{30,32}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>s1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{72,19}</string>
                <key>spriteSourceSize</key>
                <string>{72,19}</string>
                <key>textureRect</key>
                <string>{{1005,1600},{72,19}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>shenmi_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-3}</string>
                <key>spriteSize</key>
                <string>{76,44}</string>
                <key>spriteSourceSize</key>
                <string>{76,86}</string>
                <key>textureRect</key>
                <string>{{1403,825},{76,44}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>side.jpg</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{197,369}</string>
                <key>spriteSourceSize</key>
                <string>{197,369}</string>
                <key>textureRect</key>
                <string>{{1027,1585},{197,369}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>side.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{197,369}</string>
                <key>spriteSourceSize</key>
                <string>{197,369}</string>
                <key>textureRect</key>
                <string>{{1005,1784},{197,369}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>skill.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,-3}</string>
                <key>spriteSize</key>
                <string>{34,48}</string>
                <key>spriteSourceSize</key>
                <string>{40,80}</string>
                <key>textureRect</key>
                <string>{{1431,1585},{34,48}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>so_bg.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{207,38}</string>
                <key>spriteSourceSize</key>
                <string>{207,38}</string>
                <key>textureRect</key>
                <string>{{435,1975},{207,38}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>so_btn.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{46,19}</string>
                <key>spriteSourceSize</key>
                <string>{46,19}</string>
                <key>textureRect</key>
                <string>{{1005,1674},{46,19}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>tc_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,-2}</string>
                <key>spriteSize</key>
                <string>{38,22}</string>
                <key>spriteSourceSize</key>
                <string>{48,38}</string>
                <key>textureRect</key>
                <string>{{1815,1903},{38,22}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>teijiang_08.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,-3}</string>
                <key>spriteSize</key>
                <string>{63,44}</string>
                <key>spriteSourceSize</key>
                <string>{75,86}</string>
                <key>textureRect</key>
                <string>{{815,1971},{63,44}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>xiaoxi_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{47,45}</string>
                <key>spriteSourceSize</key>
                <string>{47,45}</string>
                <key>textureRect</key>
                <string>{{1538,1879},{47,45}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>zd01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{147,19}</string>
                <key>spriteSourceSize</key>
                <string>{147,19}</string>
                <key>textureRect</key>
                <string>{{1376,1784},{147,19}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>zd02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{90,47}</string>
                <key>spriteSourceSize</key>
                <string>{90,47}</string>
                <key>textureRect</key>
                <string>{{805,1922},{90,47}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>zd03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{99,48}</string>
                <key>spriteSourceSize</key>
                <string>{99,48}</string>
                <key>textureRect</key>
                <string>{{888,1841},{99,48}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>zhuangbei_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{46,45}</string>
                <key>spriteSourceSize</key>
                <string>{46,45}</string>
                <key>textureRect</key>
                <string>{{1967,1459},{46,45}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>convention-1.png</string>
            <key>size</key>
            <string>{2025,2026}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:93c3259f429dca920d6eb347f1a9f120:ecb65d32632ee30fb2db9b7b5d4fae42:b3796d7b229a28a27416386515018810$</string>
            <key>textureFileName</key>
            <string>convention-1.png</string>
        </dict>
    </dict>
</plist>
