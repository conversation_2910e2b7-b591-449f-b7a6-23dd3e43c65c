using WebApplication_HM.Models;

namespace WebApplication_HM.Services
{
    /// <summary>
    /// 地图特殊规则配置
    /// 基于原项目Fight.cs的地图特殊处理逻辑
    /// </summary>
    public static class MapSpecialRules
    {
        /// <summary>
        /// 应用地图特殊伤害规则 (基于原项目Fight.cs第674-706行)
        /// </summary>
        /// <param name="damage">基础伤害</param>
        /// <param name="mapId">地图ID</param>
        /// <param name="user">用户信息</param>
        /// <param name="monster">怪物信息</param>
        /// <returns>应用规则后的伤害</returns>
        public static long ApplyMapDamageRules(long damage, string mapId, user user, map_monster monster)
        {
            // 虚弱怪物或特殊怪物
            if (monster.monster_name?.Contains("虚弱-") == true || monster.monster_id == 98)
            {
                return 1;
            }
            
            // 赫拉神殿特殊规则
            if (mapId == "201901")
            {
                if (IsZhizunVip(user))
                {
                    long specialDamage = 1 + 1;
                    if (IsXingchenVip(user))
                    {
                        specialDamage = 1 + 1 + GetRandomBonus(7);
                    }
                    
                    if (monster.monster_name?.Contains("§") == true) // BOSS
                    {
                        if (IsXingchenVip(user))
                        {
                            specialDamage = 1 + 8 + GetRandomBonus(2);
                        }
                        else if (IsZhizunVip(user))
                        {
                            specialDamage = 1 + 5;
                        }
                        else
                        {
                            specialDamage = 1;
                        }
                    }
                    
                    return specialDamage;
                }
                else
                {
                    return 1;
                }
            }
            
            // 负伤害处理
            if (damage < 0)
            {
                return 1;
            }
            
            return damage;
        }
        
        /// <summary>
        /// 检查是否为限制输出的地图或怪物
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="monster">怪物信息</param>
        /// <returns>是否限制输出</returns>
        public static bool IsRestrictedDamage(string mapId, map_monster monster)
        {
            return monster.monster_name?.Contains("弱化-") == true || 
                   monster.monster_id == 98 || 
                   mapId == "201901";
        }
        
        /// <summary>
        /// 应用地图特效限制
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <param name="lifeStealRate">吸血率</param>
        /// <param name="damageReductionRate">抵消率</param>
        /// <returns>调整后的特效率</returns>
        public static (double lifeSteal, double damageReduction) ApplyMapEffectLimits(string mapId, double lifeStealRate, double damageReductionRate)
        {
            // 冰岛特殊限制
            if (mapId == "23")
            {
                lifeStealRate *= 0.2;
                damageReductionRate *= 0.3;
            }
            // 赫拉神殿禁止吸血
            else if (mapId == "201901")
            {
                lifeStealRate = 0;
            }
            
            return (lifeStealRate, damageReductionRate);
        }
        
        /// <summary>
        /// 获取地图经验倍率
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>经验倍率</returns>
        public static double GetMapExpMultiplier(string mapId)
        {
            return mapId switch
            {
                "201901" => 2.0, // 赫拉神殿双倍经验
                "23" => 1.5,     // 冰岛1.5倍经验
                _ => 1.0
            };
        }
        
        /// <summary>
        /// 获取地图金币倍率
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>金币倍率</returns>
        public static double GetMapGoldMultiplier(string mapId)
        {
            return mapId switch
            {
                "201901" => 3.0, // 赫拉神殿三倍金币
                "23" => 2.0,     // 冰岛双倍金币
                _ => 1.0
            };
        }
        
        /// <summary>
        /// 获取地图元宝倍率
        /// </summary>
        /// <param name="mapId">地图ID</param>
        /// <returns>元宝倍率</returns>
        public static double GetMapYuanbaoMultiplier(string mapId)
        {
            return mapId switch
            {
                "201901" => 5.0, // 赫拉神殿五倍元宝
                "23" => 2.0,     // 冰岛双倍元宝
                _ => 1.0
            };
        }
        
        /// <summary>
        /// 检查是否为至尊VIP
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>是否为至尊VIP</returns>
        private static bool IsZhizunVip(user user)
        {
            // 这里需要根据实际的VIP字段来判断
            // 假设有vip_level字段，至尊VIP为等级5
            return user.vip_level.HasValue && user.vip_level.Value >= 5;
        }

        /// <summary>
        /// 检查是否为星辰VIP
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>是否为星辰VIP</returns>
        private static bool IsXingchenVip(user user)
        {
            // 这里需要根据实际的VIP字段来判断
            // 假设有vip_level字段，星辰VIP为等级6
            return user.vip_level.HasValue && user.vip_level.Value >= 6;
        }
        
        /// <summary>
        /// 获取随机加成 (模拟原项目的ggj方法)
        /// </summary>
        /// <param name="max">最大值</param>
        /// <returns>随机加成值</returns>
        private static int GetRandomBonus(int max)
        {
            return new Random().Next(0, max + 1);
        }
        
        /// <summary>
        /// 检查是否为BOSS怪物
        /// </summary>
        /// <param name="monster">怪物信息</param>
        /// <returns>是否为BOSS</returns>
        public static bool IsBossMonster(map_monster monster)
        {
            return monster.monster_name?.Contains("§") == true;
        }
        
        /// <summary>
        /// 检查是否为虚弱怪物
        /// </summary>
        /// <param name="monster">怪物信息</param>
        /// <returns>是否为虚弱怪物</returns>
        public static bool IsWeakMonster(map_monster monster)
        {
            return monster.monster_name?.Contains("虚弱-") == true || 
                   monster.monster_name?.Contains("弱化-") == true;
        }
        
        /// <summary>
        /// 获取怪物特殊掉落倍率
        /// </summary>
        /// <param name="monster">怪物信息</param>
        /// <returns>掉落倍率</returns>
        public static double GetMonsterDropMultiplier(map_monster monster)
        {
            if (IsBossMonster(monster))
            {
                return 5.0; // BOSS五倍掉落
            }
            else if (IsWeakMonster(monster))
            {
                return 0.1; // 虚弱怪物十分之一掉落
            }
            
            return 1.0;
        }
    }
}
