using System.Reflection;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using System.Net.WebSockets;
using WebApplication_HM.DTOs.RequestDTO;
using WebApplication_HM.DTOs.ResultDTO;
using System.Collections.Concurrent;
using WebApplication_HM.Interface;
using WebApplication_HM.Models.Define;

namespace WebApplication_HM.Services.SocketInfo
{
    public class UniversalWebSocketHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly RealTimeNotificationService _realTimeService;
        // 全局连接管理（简单实现，生产建议用单独管理器）
        private static readonly ConcurrentDictionary<string, WebSocket> _connections = new();

        public UniversalWebSocketHandler(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _realTimeService = new RealTimeNotificationService();
        }

        /// <summary>
        /// 处理WebSocket消息，支持通用反射和聊天(chat/private)功能
        /// </summary>
        public async Task HandleAsync(WebSocket socket, string message)
        {  
            // 记录连接（简单实现，实际可用唯一标识）
            string connId = socket.GetHashCode().ToString();
            _connections[connId] = socket;

            using (var scope = _serviceProvider.CreateScope())
            {
                object result = "";
                var scopedProvider = scope.ServiceProvider;
                var response = new WebSocketResponseDTO();
                try
                {
                    // 聊天消息优先处理
                    var chatMsg = TryParse<ChatMessageRtDTO>(message);
                    if (chatMsg != null)
                    {
                        await HandleChatMessage(socket, chatMsg);
                        return;
                    }

                    // 其余类型走原有通用反射
                    var request = JsonSerializer.Deserialize<WebSocketRequestDTO>(message);
                    if (request == null)
                        throw new Exception("请求格式错误");

                    var serviceType = Type.GetType($"WebApplication_HM.Services.{request.Service}Service");
                    if (serviceType == null)
                        throw new Exception($"服务 {request.Service}Service 不存在");

                    var service = scopedProvider.GetService(serviceType);
                    if (service == null)
                        throw new Exception($"服务 {request.Service}Service 未注册");

                    var method = serviceType.GetMethod(request.Method);
                    if (method == null)
                        throw new Exception($"方法 {request.Method} 不存在");

                    var parameters = method.GetParameters();
                    object[] invokeParams = new object[parameters.Length];
                    for (int i = 0; i < parameters.Length; i++)
                    {
                        if (request.Parameters != null && i < request.Parameters.Length && request.Parameters[i] != null)
                        {
                            invokeParams[i] = JsonSerializer.Deserialize(request.Parameters[i].ToString(), parameters[i].ParameterType);
                        }
                        else
                        {
                            invokeParams[i] = parameters[i].HasDefaultValue ? parameters[i].DefaultValue : null;
                        }
                    }

                    if (typeof(Task).IsAssignableFrom(method.ReturnType))
                    {
                        dynamic task = method.Invoke(service, invokeParams);
                        await task;
                        result = task.GetAwaiter().GetResult();
                    }
                    else
                    {
                        result = method.Invoke(service, invokeParams);
                    }
                }
                catch (Exception ex)
                {
                    response.Success = false;
                    response.Message = ex.Message;
                    response.Data = null;
                    result = response;
                }

                var respStr = JsonSerializer.Serialize(result);
                await socket.SendAsync(Encoding.UTF8.GetBytes(respStr), WebSocketMessageType.Text, true, CancellationToken.None);
            }
        }

        /// <summary>
        /// 群发消息到所有连接
        /// </summary>
        private async Task SendMessageToAllAsync(string msg)
        {
            foreach (var ws in _connections.Values)
            {
                if (ws.State == WebSocketState.Open)
                {
                    await ws.SendAsync(Encoding.UTF8.GetBytes(msg), WebSocketMessageType.Text, true, CancellationToken.None);
                }
            }
        }

        /// <summary>
        /// 发送消息到指定用户（TargetUserId为连接Id或自定义标识）
        /// </summary>
        private async Task SendMessageToUserAsync(string targetUserId, string msg)
        {
            if (_connections.TryGetValue(targetUserId, out var ws) && ws.State == WebSocketState.Open)
            {
                await ws.SendAsync(Encoding.UTF8.GetBytes(msg), WebSocketMessageType.Text, true, CancellationToken.None);
            }
        }

        /// <summary>
        /// 处理聊天消息
        /// </summary>
        /// <param name="socket">WebSocket连接</param>
        /// <param name="chatMsg">聊天消息</param>
        private async Task HandleChatMessage(WebSocket socket, ChatMessageRtDTO chatMsg)
        {
            try
            {
                string connId = socket.GetHashCode().ToString();
                
                if (chatMsg.Type == MessageTypeEnum.Login)
                {
                    // 处理登录消息
                    await HandleLogin(socket, chatMsg);
                }
                else if (chatMsg.Type == "chat")
                {
                    // 群发聊天
                    await _realTimeService.BroadcastToAll(JsonSerializer.Serialize(chatMsg));
                }
                else if (chatMsg.Type == "private" && !string.IsNullOrEmpty(chatMsg.TargetUserId))
                {
                    // 私聊消息
                    if (int.TryParse(chatMsg.TargetUserId, out int targetPlayerId))
                    {
                        await _realTimeService.SendToPlayer(targetPlayerId, JsonSerializer.Serialize(chatMsg));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理聊天消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理登录消息
        /// </summary>
        /// <param name="socket">WebSocket连接</param>
        /// <param name="loginMsg">登录消息</param>
        private async Task HandleLogin(WebSocket socket, ChatMessageRtDTO loginMsg)
        {
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var playerService = scope.ServiceProvider.GetService<IPlayerService>();
                    if (playerService != null)
                    {
                        // 验证登录信息
                        var loginRequest = new LoginRequestDTO
                        {
                            Number = loginMsg.number,
                            Password = loginMsg.password
                        };

                        var loginResult = playerService.Login(loginRequest);
                        
                        if (loginResult.Success)
                        {
                            // 登录成功，添加到实时通知服务
                            _realTimeService.AddPlayerConnection(loginMsg.playerId, socket, loginMsg.number);

                            // 发送登录成功响应
                            var response = new ChatMessageRtDTO
                            {
                                Type = MessageTypeEnum.LoginSuccess,
                                playerId = loginMsg.playerId,
                                Name = loginMsg.number,
                                Content = "登录成功"
                            };

                            var responseJson = JsonSerializer.Serialize(response);
                            await socket.SendAsync(Encoding.UTF8.GetBytes(responseJson), WebSocketMessageType.Text, true, CancellationToken.None);
                        }
                        else
                        {
                            // 登录失败
                            var response = new
                            {
                                Type = "login_error",
                                Message = loginResult.Message
                            };

                            var responseJson = JsonSerializer.Serialize(response);
                            await socket.SendAsync(Encoding.UTF8.GetBytes(responseJson), WebSocketMessageType.Text, true, CancellationToken.None);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理登录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理连接断开
        /// </summary>
        /// <param name="socket">WebSocket连接</param>
        public void HandleDisconnection(WebSocket socket)
        {
            string connId = socket.GetHashCode().ToString();
            var playerId = _realTimeService.GetPlayerIdByConnection(connId);
            
            if (playerId.HasValue)
            {
                _realTimeService.RemovePlayerConnection(playerId.Value);
            }

            _connections.TryRemove(connId, out _);
        }

        /// <summary>
        /// 尝试反序列化为指定类型，失败返回null
        /// </summary>
        private T? TryParse<T>(string json) where T : class
        {
            try { return JsonSerializer.Deserialize<T>(json); } catch { return null; }
        }
    }
}