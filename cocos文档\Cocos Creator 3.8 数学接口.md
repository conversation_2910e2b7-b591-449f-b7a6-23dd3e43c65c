# Cocos Creator 3.8 数学相关接口规则文档

## 一、命名空间
### 1. bits
`bits` 命名空间导出了一些与位操作相关的功能模块。虽然文档未详细说明具体功能，但推测可能包含如位掩码操作、位计数等功能，用于处理二进制数据的相关操作。
### 2. math
`math` 命名空间包含了 Cocos Creator 3.8 中丰富的数学功能，涵盖了向量、矩阵运算以及其他相关数学类型的操作，是数学计算的核心命名空间。

## 二、类
### 1. AffineTransform
#### 说明
二维仿射变换矩阵，用于描述平移、旋转和缩放操作。在 2D 图形处理中，经常需要对图形进行这些变换，`AffineTransform` 类提供了方便的方式来实现这些操作。
#### 使用方式
```typescript
import { AffineTransform } from 'cc';
// 创建一个仿射变换矩阵实例
const transform = new AffineTransform();
// 设置平移变换
transform.tx = 100; // x 轴平移量
transform.ty = 50;  // y 轴平移量
// 可以使用该矩阵对 2D 图形进行变换操作
```
### 2. Color
#### 说明
通过 Red、Green、Blue 颜色通道表示颜色，并通过 Alpha 通道表示不透明度。每个通道的值范围是 [0, 255] 的整数。在游戏开发中，常用于设置物体的颜色和透明度。
#### 使用方式
```typescript
import { Color } from 'cc';
// 创建一个颜色实例，红色，不透明度为 255（完全不透明）
const redColor = new Color(255, 0, 0, 255);
// 设置节点的颜色
const node = new cc.Node();
node.color = redColor;
```
### 3. Mat3
#### 说明
表示三维（3x3）矩阵，常用于 2D 图形的变换计算，如旋转、缩放等操作。
#### 使用方式
```typescript
import { Mat3 } from 'cc';
// 创建一个 3x3 矩阵实例
const mat3 = new Mat3();
// 设置矩阵的值
mat3.m00 = 1; mat3.m01 = 0; mat3.m02 = 0;
mat3.m10 = 0; mat3.m11 = 1; mat3.m12 = 0;
mat3.m20 = 0; mat3.m21 = 0; mat3.m22 = 1;
// 可以使用该矩阵对 2D 向量进行变换
```
### 4. Mat4
#### 说明
与 `Mat3` 类似，但为 4x4 矩阵，常用于 3D 图形的变换计算，如平移、旋转、缩放和投影等操作。
#### 使用方式
```typescript
import { Mat4 } from 'cc';
// 创建一个 4x4 矩阵实例
const mat4 = new Mat4();
// 设置矩阵的值
mat4.m00 = 1; mat4.m01 = 0; mat4.m02 = 0; mat4.m03 = 0;
mat4.m10 = 0; mat4.m11 = 1; mat4.m12 = 0; mat4.m13 = 0;
mat4.m20 = 0; mat4.m21 = 0; mat4.m22 = 1; mat4.m23 = 0;
mat4.m30 = 0; mat4.m31 = 0; mat4.m32 = 0; mat4.m33 = 1;
// 可以使用该矩阵对 3D 向量进行变换
```
### 5. MathBase
文档未详细说明其具体功能，推测可能是数学相关类的基类，为其他数学类提供一些基础的属性和方法。
### 6. Quat
#### 说明
四元数，用于表示 3D 空间中的旋转。相比于欧拉角，四元数可以避免万向节锁的问题，更适合用于 3D 物体的旋转计算。
#### 使用方式
```typescript
import { Quat } from 'cc';
// 创建一个四元数实例
const quat = new Quat();
// 设置绕 y 轴旋转 90 度的四元数
Quat.fromEuler(quat, 0, 90, 0);
// 可以使用该四元数对 3D 物体进行旋转操作
```
### 7. Rect
#### 说明
表示一个二维矩形，由左下角的 x、y 坐标以及宽度和高度组成。常用于碰撞检测、裁剪等操作。
#### 使用方式
```typescript
import { Rect } from 'cc';
// 创建一个矩形实例
const rect = new Rect(0, 0, 100, 50);
// 判断一个点是否在矩形内
const point = { x: 20, y: 30 };
const isInside = rect.contains(point);
console.log('点是否在矩形内:', isInside);
```
### 8. Size
#### 说明
表示二维尺寸，包含宽度和高度信息。常用于描述物体的大小。
#### 使用方式
```typescript
import { Size } from 'cc';
// 创建一个尺寸实例
const size = new Size(100, 50);
// 获取尺寸的宽度和高度
console.log('宽度:', size.width, '高度:', size.height);
```
### 9. Vec2
#### 说明
二维向量，用于表示 2D 空间中的点或方向。在 2D 游戏开发中，常用于处理位置、位移、速度等信息。
#### 使用方式
```typescript
import { Vec2 } from 'cc';
// 创建一个二维向量实例
const vec2 = new Vec2(10, 20);
// 向量加法
const anotherVec2 = new Vec2(5, 5);
const sum = Vec2.add(new Vec2(), vec2, anotherVec2);
console.log('向量相加结果:', sum.x, sum.y);
```
### 10. Vec3
#### 说明
三维向量，用于表示 3D 空间中的点或方向。在 3D 游戏开发中，常用于处理位置、位移、速度、方向等信息。
#### 使用方式
```typescript
import { Vec3 } from 'cc';
// 创建一个三维向量实例
const vec3 = new Vec3(10, 20, 30);
// 向量点积
const anotherVec3 = new Vec3(5, 5, 5);
const dotProduct = Vec3.dot(vec3, anotherVec3);
console.log('向量点积结果:', dotProduct);
```
### 11. Vec4
#### 说明
四维向量，常用于表示 3D 空间中的点或方向，同时还可以包含额外的信息，如颜色的 RGBA 值等。
#### 使用方式
```typescript
import { Vec4 } from 'cc';
// 创建一个四维向量实例，可用于表示颜色的 RGBA 值
const vec4 = new Vec4(255, 0, 0, 255);
```

## 三、接口
### 1. IColorLike
文档未详细说明，推测该接口定义了一个类似 `Color` 类的结构，可能用于传递颜色相关信息。
### 2. IMat3Like
推测该接口定义了一个类似 `Mat3` 类的结构，可能用于传递 3x3 矩阵相关信息。
### 3. IMat4Like
推测该接口定义了一个类似 `Mat4` 类的结构，可能用于传递 4x4 矩阵相关信息。
### 4. IQuatLike
推测该接口定义了一个类似 `Quat` 类的结构，可能用于传递四元数相关信息。
### 5. IRectLike
推测该接口定义了一个类似 `Rect` 类的结构，可能用于传递矩形相关信息。
### 6. ISizeLike
推测该接口定义了一个类似 `Size` 类的结构，可能用于传递尺寸相关信息。
### 7. IVec2Like
推测该接口定义了一个类似 `Vec2` 类的结构，可能用于传递二维向量相关信息。
### 8. IVec3Like
推测该接口定义了一个类似 `Vec3` 类的结构，可能用于传递三维向量相关信息。
### 9. IVec4Like
推测该接口定义了一个类似 `Vec4` 类的结构，可能用于传递四维向量相关信息。

## 四、函数
### 1. abs
#### 说明
计算整数的绝对值。
#### 使用方式
```typescript
import { abs } from 'cc';
const num = -10;
const absNum = abs(num);
console.log('绝对值:', absNum);
```
### 2. absMax
#### 说明
对 a 和 b 的绝对值进行比较大小，返回绝对值最大的值。
#### 使用方式
```typescript
import { absMax } from 'cc';
const a = -5; const b = 3;
const result = absMax(a, b);
console.log('绝对值最大的值:', result);
```
### 3. absMaxComponent
#### 说明
对所有分量的绝对值进行比较大小，返回绝对值最大的分量。对于向量等多分量数据结构很有用。
#### 使用方式
```typescript
import { Vec3, absMaxComponent } from 'cc';
const vec3 = new Vec3(-10, 5, -3);
const maxComponent = absMaxComponent(vec3);
console.log('绝对值最大的分量:', maxComponent);
```
### 4. approx
#### 说明
通过给定的最大差异，测试参数是否具有近似相同的值。常用于判断两个浮点数是否近似相等。
#### 使用方式
```typescript
import { approx } from 'cc';
const num1 = 1.001; const num2 = 1.002;
const maxDiff = 0.01;
const isApprox = approx(num1, num2, maxDiff);
console.log('两个数是否近似相等:', isApprox);
```
### 5. clamp
#### 说明
返回最小浮点数和最大浮点数之间的一个数值。可以使用 `clamp` 函数将不断变化的数值限制在范围内。
#### 使用方式
```typescript
import { clamp } from 'cc';
const value = 15; const min = 10; const max = 20;
const clampedValue = clamp(value, min, max);
console.log('限制后的数值:', clampedValue);
```
### 6. clamp01
#### 说明
将值限制在 0 和 1 之间。常用于处理比例、透明度等需要在 [0, 1] 范围内的值。
#### 使用方式
```typescript
import { clamp01 } from 'cc';
const value = 1.5;
const clampedValue = clamp01(value);
console.log('限制在 0 和 1 之间的值:', clampedValue);
```
### 7. color
文档未详细说明其具体功能，推测可能用于创建或操作 `Color` 类的实例。
### 8. countTrailingZeros
#### 说明
计算传入数字二进制表示尾随零的数量。在位操作中可能会用到。
#### 使用方式
```typescript
import { countTrailingZeros } from 'cc';
const num = 8; // 二进制为 1000
const trailingZeros = countTrailingZeros(num);
console.log('尾随零的数量:', trailingZeros);
```
### 9. deinterleave2
#### 说明
提取第 n 个交错分量。可能用于处理交错编码的数据。
#### 使用方式
```typescript
import { deinterleave2 } from 'cc';
// 假设这里有交错编码的数据
const num1 = 0b1010; const num2 = 0b0101;
const n = 1;
const result = deinterleave2(num1, num2, n);
console.log('提取的交错分量:', result);
```
### 10. deinterleave3
#### 说明
提取三个数字中的第 n 个交错分量。同样用于处理交错编码的数据。
#### 使用方式
```typescript
import { deinterleave3 } from 'cc';
// 假设这里有三个交错编码的数据
const num1 = 0b1010; const num2 = 0b0101; const num3 = 0b1100;
const n = 1;
const result = deinterleave3(num1, num2, num3, n);
console.log('提取的交错分量:', result);
```
### 11. enumerableProps
文档未详细说明其具体功能，推测可能用于使指定类的特定属性可被枚举。
### 12. equals
#### 说明
在 glMatrix 的绝对或相对容差范围内，测试参数是否具有近似相同的值。与 `approx` 类似，但使用了 glMatrix 的容差规则。
#### 使用方式
```typescript
import { equals } from 'cc';
const num1 = 1.001; const num2 = 1.002;
const isEqual = equals(num1, num2);
console.log('两个数是否在容差范围内相等:', isEqual);
```
### 13. floatToHalf
文档未详细说明其具体功能，推测可能用于将浮点数转换为半精度浮点数。
### 14. halfToFloat
文档未详细说明其具体功能，推测可能用于将半精度浮点数转换为浮点数。
### 15. interleave2
#### 说明
将两个 16 位数字按位交错编码。有利于在快速四叉树中使用。
#### 使用方式
```typescript
import { interleave2 } from 'cc';
const num1 = 0b1010; const num2 = 0b0101;
const interleaved = interleave2(num1, num2);
console.log('交错编码后的结果:', interleaved);
```
### 16. interleave3
#### 说明
将三个数字按位交错编码，每个数字占十位。有利于在八叉树中使用。
#### 使用方式
```typescript
import { interleave3 } from 'cc';
const num1 = 0b1010; const num2 = 0b0101; const num3 = 0b1100;
const interleaved = interleave3(num1, num2, num3);
console.log('交错编码后的结果:', interleaved);
```
### 17. inverseLerp
#### 说明
返回给定范围内的值的比率。例如，在 [0, 100] 范围内，值为 50 时，比率为 0.5。
#### 使用方式
```typescript
import { inverseLerp } from 'cc';
const min = 0; const max = 100; const value = 50;
const ratio = inverseLerp(min, max, value);
console.log('值的比率:', ratio);
```
### 18. isPow2
#### 说明
检查一个数字是否是 2 的幂。在处理内存分配、纹理大小等方面可能会用到。
#### 使用方式
```typescript
import { isPow2 } from 'cc';
const num = 8;
const isPowerOf2 = isPow2(num);
console.log('该数字是否是 2 的幂:', isPowerOf2);
```
### 19. lerp
#### 说明
两个数之间的线性插值。常用于实现平滑过渡效果，如颜色渐变、位置移动等。
#### 使用方式
```typescript
import { lerp } from 'cc';
const start = 0; const end = 100; const ratio = 0.5;
const interpolatedValue = lerp(start, end, ratio);
console.log('线性插值结果:', interpolatedValue);
```
### 20. log10
#### 说明
计算以 10 为底的 v 的对数。
#### 使用方式
```typescript
import { log10 } from 'cc';
const num = 100;
const logValue = log10(num);
console.log('以 10 为底的对数:', logValue);
```
### 21. log2
#### 说明
计算以 2 为底的 v 的对数。在处理二进制数据、内存分配等方面可能会用到。
#### 使用方式
```typescript
import { log2 } from 'cc';
const num = 8;
const logValue = log2(num);
console.log('以 2 为底的对数:', logValue);
```
### 22. mat4
文档未详细说明其具体功能，推测可能用于创建或操作 `Mat4` 类的实例。
### 23. max
#### 说明
计算整数 x 和 y 中的最大值。
#### 使用方式
```typescript
import { max } from 'cc';
const x = 5; const y = 10;
const maxValue = max(x, y);
console.log('最大值:', maxValue);
```
### 24. min
#### 说明
计算整数 x 和 y 中的最小值。
#### 使用方式
```typescript
import { min } from 'cc';
const x = 5; const y = 10;
const minValue = min(x, y);
console.log('最小值:', minValue);
```
### 25. nextCombination
#### 说明
计算下一组字典序的比特排列。在处理二进制组合问题时可能会用到。
#### 使用方式
```typescript
import { nextCombination } from 'cc';
const num = 0b1010;
const nextComb = nextCombination(num);
console.log('下一组字典序的比特排列:', nextComb.toString(2));
```
### 26. nextPow2
#### 说明
计算大于等于 v 的最小的二的整数次幂的数字。在处理内存分配、纹理大小等方面可能会用到。
#### 使用方式
```typescript
import { nextPow2 } from 'cc';
const num = 15;
const nextPowerOf2 = nextPow2(num);
console.log('大于等于该数的最小 2 的幂:', nextPowerOf2);
```
### 27. packRGBE
#### 说明
将三通道 rgb 颜色打包成四通道 rbge 格式。在处理颜色编码时可能会用到。
#### 使用方式
```typescript
import { packRGBE } from 'cc';
const r = 255; const g = 0; const b = 0;
const packed = packRGBE(r, g, b);
console.log('打包后的颜色:', packed);
```
### 28. parity
#### 说明
奇偶校验。用于判断一个数字的二进制表示中 1 的数量是奇数还是偶数。
#### 使用方式
```typescript
import { parity } from 'cc';
const num = 0b1010;
const isOdd = parity(num);
console.log('二进制中 1 的数量是否为奇数:', isOdd);
```
### 29. pingPong
#### 说明
返回乒乓模式下的相对时间。常用于实现循环往复的动画效果。
#### 使用方式
```typescript
import { pingPong } from 'cc';
const t = 2; const length = 3;
const pingPongValue = pingPong(t, length);
console.log('乒乓模式下的相对时间:', pingPongValue);
```
### 30. popCount
#### 说明
计算传入数字二进制表示中 1 的数量。在处理位操作、哈希算法等方面可能会用到。
#### 使用方式
```typescript
import { popCount } from 'cc';
const num = 0b1010;
const count = popCount(num);
console.log('二进制中 1 的数量:', count);
```
### 31. prevPow2
#### 说明
计算小于等于 v 的最小的二的整数次幂的数字。在处理内存分配、纹理大小等方面可能会用到。
#### 使用方式
```typescript
import { prevPow2 } from 'cc';
const num = 15;
const prevPowerOf2 = prevPow2(num);
console.log('小于等于该数的最小 2 的幂:', prevPowerOf2);
```
### 32. pseudoRandom
#### 说明
使用 Hull - Dobell 算法的线性同余生成器构造伪随机数。
#### 使用方式
```typescript
import { pseudoRandom } from 'cc';
const randomNum = pseudoRandom();
console.log('伪随机数:', randomNum);
```
### 33. pseudoRandomRange
#### 说明
返回一个在范围内的浮点伪随机数，注意，不包含边界值。
#### 使用方式
```typescript
import { pseudoRandomRange } from 'cc';
const min = 0; const max = 10;
const randomRangeNum = pseudoRandomRange(min, max);
console.log('范围内的浮点伪随机数:', randomRangeNum);
```
### 34. pseudoRandomRangeInt
#### 说明
返回最小（包含）和最大（不包含）之间的浮点伪随机数。
#### 使用方式
```typescript
import { pseudoRandomRangeInt } from 'cc';
const min = 0; const max = 10;
const randomRangeInt = pseudoRandomRangeInt(min, max);
console.log('范围内的整数伪随机数:', randomRangeInt);
```
### 35. quat
文档未详细说明其具体功能，推测可能用于创建或操作 `Quat` 类的实例。
### 36. random
文档未详细说明其具体功能，推测可能用于生成随机数。
### 37. randomRange
#### 说明
返回最小（包含）和最大（不包含）之间的浮点随机数。
#### 使用方式
```typescript
import { randomRange } from 'cc';
const min = 0; const max = 10;
const randomRangeNum = randomRange(min, max);
console.log('范围内的浮点随机数:', randomRangeNum);
```
### 38. randomRangeInt
#### 说明
返回最小（包含）和最大（不包含）之间的随机整数。
#### 使用方式
```typescript
import { randomRangeInt } from 'cc';
const min = 0; const max = 10;
const randomRangeInt = randomRangeInt(min, max);
console.log('范围内的随机整数:', randomRangeInt);
```
### 39. rect
#### 说明
构造与指定矩形相等的矩形。等价于 `new Rect(rect)`。
#### 使用方式
```typescript
import { Rect, rect } from 'cc';
const originalRect = new Rect(0, 0, 100, 50);
const newRect = rect(originalRect);
console.log('新矩形的宽度:', newRect.width, '高度:', newRect.height);
```
### 40. repeat
#### 说明
返回 t / length 的浮点余数。常用于实现循环效果。
#### 使用方式
```typescript
import { repeat } from 'cc';
const t = 5; const length = 3;
const remainder = repeat(t, length);
console.log('浮点余数:', remainder);
```
### 41. reverse
#### 说明
翻转 32 位二进制数字。在处理位操作时可能会用到。
#### 使用方式
```typescript
import { reverse } from 'cc';
const num = 0b1010;
const reversed = reverse(num);
console.log('翻转后的二进制数:', reversed.toString(2));
```
### 42. setRandGenerator
#### 说明
设置自定义随机数生成器，默认为 `Math.random`。在需要自定义随机数生成逻辑时可以使用。
#### 使用方式
```typescript
import { setRandGenerator } from 'cc';
// 自定义随机数生成器函数
const customRandGenerator = () => { 
    return Math.random() * 10; 
}; 
setRandGenerator(customRandGenerator); 
```
### 43. sign
#### 说明
根据 x 的符号返回 - 1，0，+ 1。常用于判断一个数的正负性。
#### 使用方式
```typescript
import { sign } from 'cc';
const num = -5;
const signValue = sign(num);
console.log('数的符号:', signValue);
```
### 44. size
#### 说明
等价于 `new Size(other)`。用于创建一个与指定尺寸相同的尺寸实例。
#### 使用方式
```typescript
import { Size, size } from 'cc';
const originalSize = new Size(100, 50);
const newSize = size(originalSize);
console.log('新尺寸的宽度:', newSize.width, '高度:', newSize.height);
```
### 45. toDegree
#### 说明
把弧度换算成角度。在处理角度和弧度的转换时会用到。
#### 使用方式
```typescript
import { toDegree } from 'cc';
const radian = Math.PI / 2;
const degree = toDegree(radian);
console.log('弧度转换为角度:', degree);
```
### 46. toRadian
#### 说明
把角度换算成弧度。在处理角度和弧度的转换时会用到。
#### 使用方式
```typescript
import { toRadian } from 'cc';
const degree = 90;
const radian = toRadian(degree);
console.log('角度转换为弧度:', radian);
```
### 47. v2
文档未详细说明其具体功能，推测可能用于创建或操作 `Vec2` 类的实例。
### 48. v3
文档未详细说明其具体功能，推测可能用于创建或操作 `Vec3` 类的实例。
### 49. v4
文档未详细说明其具体功能，推测可能用于创建或操作 `Vec4` 类的实例。

## 五、变量
### 1. EPSILON
文档未详细说明其具体值和用途，推测可能是一个用于比较浮点数的极小值，用于处理浮点数的精度问题。
### 2. HALF_PI
值为 π / 2，常用于角度和弧度的计算。
#### 使用方式
```typescript
import { HALF_PI } from 'cc';
console.log('π / 2 的值:', HALF_PI);
```
### 3. INT_BITS
整型类型的 bit 数，在处理位操作、内存分配等方面可能会用到。
### 4. INT_MAX
最大有符号整型数，在处理整数范围时可能会用到。
### 5. INT_MIN
最小有符号整型数，在处理整数范围时可能会用到。
### 6. MATH_FLOAT_ARRAY
文档未详细说明其具体用途，推测可能是一个与浮点数数组相关的变量，用于存储和处理浮点数数据。
### 7. TWO_PI
值为 2π，常用于角度和弧度的计算。
#### 使用方式
```typescript
import { TWO_PI } from 'cc';
console.log('2π 的值:', TWO_PI);
```
