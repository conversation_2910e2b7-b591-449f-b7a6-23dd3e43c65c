-- 创建道具丢弃记录表
CREATE TABLE IF NOT EXISTS `item_discard_log` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID（主键，自增）',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `original_item_seq` int(11) NOT NULL COMMENT '原道具序号',
  `item_id` varchar(50) NOT NULL COMMENT '道具ID',
  `item_name` varchar(100) NOT NULL COMMENT '道具名称（冗余存储，方便查询）',
  `discard_count` bigint(20) NOT NULL DEFAULT '1' COMMENT '丢弃的道具数量',
  `discard_reason` varchar(200) DEFAULT '用户主动丢弃' COMMENT '丢弃原因',
  `discard_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '丢弃时间',
  `is_recovered` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已找回（0=未找回，1=已找回）',
  `recover_time` datetime DEFAULT NULL COMMENT '找回时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_discard_time` (`discard_time`),
  KEY `idx_is_recovered` (`is_recovered`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='道具丢弃记录表';

-- 添加一些示例数据（可选）
-- INSERT INTO `item_discard_log` (`user_id`, `original_item_seq`, `item_id`, `item_name`, `discard_count`, `discard_reason`, `discard_time`, `is_recovered`) 
-- VALUES 
-- (1, 123, '1', '玄天露水', 1, '用户主动丢弃', NOW(), 0),
-- (1, 124, '2', '回复药水', 2, '背包整理', NOW(), 1);
