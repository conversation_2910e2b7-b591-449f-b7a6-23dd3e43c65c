import { _decorator, Component, Node, find } from 'cc';
const { ccclass, property } = _decorator;

import { Global } from "./Global";  
@ccclass('panel_field')
export class panel_field extends Component {
    
    @property(Node)
    private fightStatePanel: Node;//战斗准备面板

    @property(Node)
    private fightMain: Node;//战斗主面板

    start() {
        this.fightMain.active = false;
        this.fightStatePanel.active = false;
       
        if (!this.fightStatePanel) {
            console.error("找不到战斗面板");
            return;
        }

        // 🔧 动态处理从100到118的所有地图按钮
        this.setupMapButtons(100, 118);
    }

    /**
     * 🆕 节点激活时的处理（每次进入页面都会调用）
     */
    onEnable() {
        console.log("🔄 panel_field页面被激活");
        
        // 🔧 确保战斗相关面板处于隐藏状态
        if (this.fightMain) {
            this.fightMain.active = false;
        }
        if (this.fightStatePanel) {
            this.fightStatePanel.active = false;
        }
        
        console.log("✅ panel_field页面状态已重置");
    }

    /**
     * 🆕 动态设置地图按钮事件监听
     * @param startId 起始按钮ID
     * @param endId 结束按钮ID
     */
    private setupMapButtons(startId: number, endId: number): void {
        console.log(`🗺️ 开始设置地图按钮事件监听 (${startId} - ${endId})`);
        
        let successCount = 0;
        let failCount = 0;
        
        for (let i = startId; i <= endId; i++) {
            const buttonPath = `/MAP/BtnNoviceBase_${i}`;
            const buttonNode = find(buttonPath, this.node);
            
            if (buttonNode) {
                buttonNode.on(Node.EventType.MOUSE_DOWN, this.onMapButtonClickByEvent, this);
                successCount++;
                console.log(`✅ 按钮 BtnNoviceBase_${i} 事件监听设置成功`);
            } else {
                failCount++;
                console.warn(`⚠️ 按钮 BtnNoviceBase_${i} 未找到`);
            }
        }
        
        console.log(`📊 地图按钮设置完成 - 成功: ${successCount}, 失败: ${failCount}, 总计: ${endId - startId + 1}`);
    }

    // 🔧 通过事件对象获取按钮信息并设置对应的地图ID
    onMapButtonClickByEvent(event: any) {
        // 可以通过按钮节点名称或自定义属性来确定地图ID
        const buttonNode = event.target;
        let mapId = 1; // 默认值
        
        // 🆕 使用正则表达式动态提取按钮ID
        const buttonName = buttonNode.name;
        const match = buttonName.match(/BtnNoviceBase_(\d+)/);
        
        if (match && match[1]) {
            const buttonId = parseInt(match[1]);
            // 🔧 将按钮ID转换为对应的地图ID
            mapId = this.convertButtonIdToMapId(buttonId);
            console.log(`✅ 按钮 ${buttonName} 点击，按钮ID: ${buttonId}, 地图ID: ${mapId}`);
        } else {
            console.warn(`⚠️ 无法解析按钮名称: ${buttonName}，使用默认地图ID: ${mapId}`);
        }
        
        Global.currentMapId = mapId;
        console.log(`🗺️ 设置当前地图ID: ${mapId}`);
        this.fightStatePanel.active = true;
        this.node.active = false;
    }

    /**
     * 🆕 将按钮ID转换为地图ID
     * @param buttonId 按钮ID (100-118)
     * @returns 对应的地图ID
     */
    private convertButtonIdToMapId(buttonId: number): number {
        // 🔧 根据实际需求调整转换规则
        // 目前使用简单的映射：按钮ID直接对应地图ID
        // 如果需要特殊映射规则，可以在这里修改
        
        if (buttonId >= 100 && buttonId <= 118) {
            // 方案1: 直接使用按钮ID作为地图ID
            return buttonId;
            
            // 方案2: 如果需要映射到1-19，可以使用：
            // return buttonId - 99;
            
            // 方案3: 如果需要自定义映射，可以使用对象映射：
            // const idMap = {
            //     100: 1, 101: 2, 102: 3, 103: 4, 104: 5,
            //     105: 6, 106: 7, 107: 8, 108: 9, 109: 10,
            //     110: 11, 111: 12, 112: 13, 113: 14, 114: 15,
            //     115: 16, 116: 17, 117: 18, 118: 19
            // };
            // return idMap[buttonId] || 1;
        }
        
        console.warn(`⚠️ 按钮ID ${buttonId} 超出范围 (100-118)，使用默认地图ID: 1`);
        return 1; // 默认地图ID
    }

 
}


