using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace WebApplication_HM.Utils
{
    public class DataProcess
    {
        private static readonly Random GameRandNum;
        private static readonly object RandNumLock = new object();

        static DataProcess()
        {
            GameRandNum = new Random(DateTime.Now.Millisecond);
        }

        /// <summary>
        /// 初始化工具类
        /// </summary>
        public static void Init()
        {
            // C#的Random不需要像Go那样显式初始化
            // 已在静态构造函数中完成初始化
        }

        /// <summary>
        /// 计算字符串的MD5哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>MD5哈希值的十六进制字符串</returns>
        public static string MD5Hash(string input)
        {
            using (var md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);
                return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
            }
        }

        /// <summary>
        /// 获取当前时间戳，精确到秒
        /// </summary>
        /// <returns>Unix时间戳（秒）</returns>
        public static long Time_getTimestamp()
        {
            return DateTimeOffset.Now.ToUnixTimeSeconds();
        }

        /// <summary>
        /// 将对象序列化为JSON字符串，如果错误返回空字符串
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>JSON字符串</returns>
        public static string JsonToStr(object obj)
        {
            try
            {
                return JsonSerializer.Serialize(obj);
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取0-1之间的随机数，越大的数随机到的概率越小
        /// </summary>
        /// <returns>随机数值</returns>
        public static double Config_getRandom()
        {
            lock (RandNumLock)
            {
                double randomValue = GameRandNum.NextDouble();
                return 1 - randomValue; // 使用倒数处理
            }
        }
    }
}